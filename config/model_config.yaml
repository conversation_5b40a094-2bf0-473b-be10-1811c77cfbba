# 模型训练配置文件

# 全局数据集配置
dataset:
  config_path: "data/final/dataset.yaml"  # 固定参数训练使用的数据集配置文件路径
  search_config_path: "data/search/dataset.yaml"  # 超参数搜索使用的数据集配置文件路径
  statistics_path: "data/final/statistics.json"  # 数据集统计信息文件

# 基础模型配置
model:
  # 模型架构配置
  dir_path: "models/"
  architecture: "yolo11"  # YOLO11模型架构
  scale: "n"  # 模型规模: n(nano), s(small), m(medium), l(large), x(xlarge)
  pretrained_weights: "yolo11n.pt"  # 预训练权重文件，会根据scale自动调整

# 环境配置
environment:
  # 设备配置（统一配置）
  device: "cuda"  # 计算设备: cuda(GPU), cpu, mps(Apple Silicon)
  
  # 内存优化
  memory_optimization:
    pin_memory: true      # 将数据固定在内存中，加速GPU数据传输
    non_blocking: true    # 非阻塞数据传输
    
  # 性能优化
  performance:
    benchmark: true       # 启用cudnn benchmark，加速固定输入尺寸的训练
    deterministic: false  # 是否使用确定性算法（影响性能）
    
  # 调试配置
  debug:
    profile: false        # 是否启用性能分析
    verbose: true         # 是否显示详细日志

# 训练模式配置
training_modes:
  # 固定参数训练模式（用于测试代码可行性）
  fixed_params:
    # === 基础训练参数 ===
    epochs: 10           # 训练轮数
    batch: 32             # 批次大小（建议根据GPU内存调整）
    imgsz: 640           # 输入图像尺寸（正方形）
    workers: 12           # 数据加载进程数（建议为CPU核心数）
    project: "runs/train" # 训练结果保存项目目录
    name: "fixed_params_exp"  # 实验名称
    
    # === 优化器参数 ===
    optimizer: "AdamW"    # 优化器类型: SGD, Adam, AdamW, NAdam, RAdam, RMSProp
    lr0: 0.024            # 初始学习率
    lrf: 0.2              # 最终学习率比例 (final_lr = lr0 * lrf)
    momentum: 0.937       # SGD动量/Adam beta1
    weight_decay: 0.0005  # 权重衰减（L2正则化）
    warmup_epochs: 3      # 学习率预热轮数
    warmup_momentum: 0.8  # 预热阶段动量
    warmup_bias_lr: 0.1   # 预热阶段偏置学习率
    
    # === 数据增强参数 ===
    hsv_h: 0.015         # HSV色调增强范围 [0-1]
    hsv_s: 0.7          # HSV饱和度增强范围 [0-1]
    hsv_v: 0.4          # HSV明度增强范围 [0-1]
    degrees: 0.0        # 旋转角度范围 [-degrees, +degrees]
    translate: 0.1      # 平移范围 [0-1]
    scale: 0.5          # 缩放范围 [1-scale, 1+scale]
    shear: 0.0          # 剪切角度范围 [-shear, +shear]
    perspective: 0.0    # 透视变换范围 [0-perspective]
    flipud: 0.0         # 垂直翻转概率 [0-1]
    fliplr: 0.5         # 水平翻转概率 [0-1]
    mosaic: 1.0         # Mosaic增强概率 [0-1]
    mixup: 0.0          # MixUp增强概率 [0-1]
    copy_paste: 0.0     # Copy-Paste增强概率 [0-1]
    
    # === 损失函数参数 ===
    box: 7.5            # 边界框损失权重
    cls: 0.5            # 分类损失权重
    dfl: 1.5            # 分布焦点损失权重
    
    # === 验证和保存参数 ===
    val: true           # 是否在训练过程中进行验证
    save: true          # 是否保存检查点
    save_period: 10     # 每N个epoch保存一次检查点
    patience: 10        # 早停耐心值（验证指标不改善的epoch数）
    
    # === 其他训练参数 ===
    verbose: true       # 显示详细训练信息
    seed: 0             # 随机种子（0为随机）
    deterministic: true # 确定性训练（可重现结果）
    single_cls: false   # 是否单类训练
    rect: false         # 矩形训练（最小填充）
    cos_lr: false       # 余弦学习率调度
    close_mosaic: 10    # 在最后N个epoch关闭mosaic增强
    resume: false       # 是否从检查点恢复训练
    amp: true           # 自动混合精度训练
    fraction: 1.0       # 使用的数据集比例 [0-1]
    profile: false      # 性能分析
    freeze: null        # 冻结层数（null为不冻结）
    
  # 超参数搜索模式（用于最终模型训练）
  hyperparameter_search:
    # === 基础训练参数 ===
    epochs: 3         # 每次试验的训练轮数
    batch: 32           # 批次大小
    imgsz: 640         # 输入图像尺寸
    workers: 12         # 数据加载进程数
    project: "runs/tune" # 调优结果保存目录
    name: "hyperparameter_search_exp"  # 调优实验名称
    iterations: 3     # 搜索迭代次数（替代max_samples）
    
    # === 并行搜索配置 ===
    # 注意：通过这些配置控制超参数搜索的并行化行为
    enable_parallel: true      # 是否启用并行搜索（false=单线程，true=多线程）
    max_workers: 2            # 最大并行工作线程数（仅在enable_parallel=true时有效）
    search_strategy: "ultralytics" # 搜索策略选择：
                                   # - ultralytics: 使用原生ultralytics搜索（推荐）
                                   # - random: 多线程随机搜索（需要enable_parallel=true）
                                   # - grid: 多线程网格搜索（需要enable_parallel=true）
    num_samples: 15           # 随机搜索样本数（仅在search_strategy="random"时有效）
    # max_combinations: 30      # 网格搜索最大组合数（仅在search_strategy="grid"时有效）
    
    # === 可搜索的超参数范围 ===
    search_space:
      # 学习率相关
      lr0: [0.0001, 0.01]      # 学习率搜索范围
      lrf: [0.001, 0.1]        # 最终学习率比例范围
      momentum: [0.85, 0.95]   # 动量搜索范围
      weight_decay: [0.0001, 0.001]  # 权重衰减搜索范围
      
      # 训练相关
      warmup_epochs: [1, 5]    # 预热轮数搜索范围
      
      # 损失函数权重
      box: [5.0, 12.0]         # 边界框损失权重范围
      cls: [0.2, 1.0]          # 分类损失权重范围
      dfl: [1.0, 2.0]          # 分布焦点损失权重范围
      
      # 数据增强参数
      hsv_h: [0.01, 0.05]      # HSV色调增强范围
      hsv_s: [0.5, 0.9]        # HSV饱和度增强范围
      hsv_v: [0.2, 0.6]        # HSV明度增强范围
      degrees: [0.0, 20.0]     # 旋转角度范围
      translate: [0.05, 0.2]   # 平移范围
      scale: [0.3, 0.7]        # 缩放范围
      shear: [0.0, 5.0]        # 剪切角度范围
      fliplr: [0.0, 1.0]       # 水平翻转概率范围
      mosaic: [0.5, 1.0]       # Mosaic增强概率范围
      mixup: [0.0, 0.3]        # MixUp增强概率范围
      copy_paste: [0.0, 0.5]   # Copy-Paste增强概率范围

# 测试配置
testing:
  # === 测试数据配置 ===
  split: "test"         # 测试数据集分割（test/val）
  batch: 32            # 测试批次大小（可以比训练更大）
  imgsz: 640           # 测试图像尺寸
  workers: 12           # 数据加载进程数
  
  # === 检测参数 ===
  conf: 0.001          # 置信度阈值（较低值检测更多目标）
  iou: 0.6             # NMS IoU阈值（重叠抑制）
  max_det: 300         # 每张图像最大检测数量
  half: false          # 是否使用半精度推理
  dnn: false           # 是否使用OpenCV DNN后端
  
  # === 输出控制 ===
  save_json: true      # 保存COCO格式的结果JSON
  save_hybrid: false   # 保存混合标签（标签+预测）
  plots: true          # 生成预测结果图
  verbose: true        # 显示详细测试信息
  
  # === 输出目录 ===
  project: "runs/test" # 测试结果保存目录
  name: "test_exp"     # 测试实验名称

# 模型管理配置
model_management:
  # 模型保存配置
  save_config:
    save_best: true     # 保存最佳模型
    save_last: true     # 保存最后一个模型
    save_period: 10     # 定期保存间隔
    
  # 模型查找配置
  model_discovery:
    # 自动查找最新/最佳模型的目录
    search_dirs:
      - "runs/train"    # 固定参数训练结果目录
      - "runs/tune"     # 超参数搜索结果目录
      - "models"        # 手动保存的模型目录
    
    # 模型文件优先级：best > last > latest_by_date
    priority: ["best.pt", "last.pt"]
    
    # 是否递归搜索子目录
    recursive: true

# 输出配置
output:
  # 日志配置
  logging:
    level: "INFO"       # 日志级别: DEBUG, INFO, WARNING, ERROR
    save_logs: true     # 是否保存日志文件
    
  # 可视化配置
  visualization:
    save_plots: true    # 保存训练曲线图
    plot_metrics: true  # 绘制评估指标
    plot_predictions: true  # 绘制预测结果示例
    
  # 报告配置
  reports:
    generate_training_report: true   # 生成训练报告
    generate_test_report: true       # 生成测试报告
    export_format: ["json", "html"]  # 报告导出格式

# 错误处理配置
error_handling:
  # 训练错误处理
  training:
    continue_on_error: false  # 训练出错时是否继续
    max_retries: 3           # 最大重试次数
    retry_delay: 5.0         # 重试延迟（秒）
    
  # 测试错误处理
  testing:
    continue_on_error: true   # 测试出错时是否继续
    fallback_to_cpu: true     # GPU出错时是否回退到CPU
    
# 实验管理配置
experiment:
  # 实验标识
  tags: []              # 实验标签
  description: ""       # 实验描述
  
  # 结果比较
  compare_experiments: true      # 是否比较实验结果
  baseline_experiment: null     # 基线实验名称

# 智能配置建议（由config_advisor_tool.py使用）
auto_config:
  # 是否启用自动配置建议
  enabled: true
  
  # 硬件检测配置
  hardware_detection:
    check_gpu_memory: true      # 检测GPU内存
    check_cpu_cores: true       # 检测CPU核心数
    check_system_memory: true   # 检测系统内存
    
  # 配置推荐规则
  recommendation_rules:
    # batch size推荐规则（基于GPU内存）
    batch_size_rules:
      # GPU内存(GB) -> 推荐batch size
      4: 8
      6: 12  
      8: 16
      11: 20
      24: 32
      
    # workers推荐规则（基于CPU核心数）
    workers_rules:
      min_workers: 2
      max_workers: 16
      ratio: 0.5  # workers = cpu_cores * ratio
      
    # 其他参数推荐
    other_params:
      # 基于数据集大小的epochs推荐
      epochs_by_dataset_size:
        small: 150    # < 1000样本
        medium: 100   # 1000-10000样本  
        large: 50     # > 10000样本 
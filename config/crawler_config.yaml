# 爬虫配置文件

# 搜索关键词配置
search_keywords:
  # 关键词组
  keyword_groups:
    construction_equipment:
      keywords:
        - "excavator"
        - "bulldozer"
        - "crane construction"
        - "dump truck"
        - "concrete mixer"
        - "road roller"
      languages: ["en", "zh"]
      
    vehicles:
      keywords:
        - "car"
        - "truck"
        - "bus"
        - "motorcycle"
        - "bicycle"
      languages: ["en"]
      
    animals:
      keywords:
        - "dog"
        - "cat"
        - "bird"
        - "horse"
        - "cow"
      languages: ["en"]
  
  # 关键词修饰符
  modifiers:
    quality: ["high resolution", "clear", "professional"]
    context: ["outdoor", "real world", "natural environment"]
    exclude: ["cartoon", "drawing", "sketch", "anime"]

# 爬取参数设置
crawling_parameters:
  # 基础爬取参数
  basic:
    max_images_per_keyword: 1000
    max_total_images: 10000
    threads: 4
    delay: 1.0  # 请求间隔（秒）
    timeout: 30
    
  # 搜索引擎配置
  search_engines:
    google:
      enabled: true
      max_images: 1000
      safe_search: false
      image_type: "photo"  # photo, clipart, lineart
      
    bing:
      enabled: true
      max_images: 500
      safe_search: false
      
    baidu:
      enabled: false
      max_images: 300
      
    flickr:
      enabled: false
      api_key: ""  # 需要申请API密钥
      max_images: 200
  
  # 并发控制
  concurrency:
    max_workers: 4
    batch_size: 50
    queue_size: 1000

# 过滤条件配置
filtering_conditions:
  # 图像尺寸过滤
  image_size:
    min_width: 224
    min_height: 224
    max_width: 4096
    max_height: 4096
    aspect_ratio_range: [0.5, 2.0]  # 宽高比范围
    
  # 文件格式过滤
  file_format:
    allowed_formats: ["jpg", "jpeg", "png", "webp"]
    exclude_formats: ["gif", "svg", "bmp"]
    max_file_size: "10MB"
    min_file_size: "10KB"
    
  # 图像质量过滤
  image_quality:
    min_quality_score: 0.7  # 需要实现质量评估
    check_blurriness: true
    check_contrast: true
    check_brightness: true
    
  # 内容过滤
  content_filtering:
    # 人脸检测过滤（可选）
    face_detection:
      enabled: false
      max_faces: 10
      
    # 文字检测过滤
    text_detection:
      enabled: true
      max_text_ratio: 0.3  # 文字区域占比阈值
      
    # 重复图像过滤
    duplicate_detection:
      enabled: true
      similarity_threshold: 0.95
      hash_algorithm: "dhash"  # dhash, phash, ahash

# 存储路径设置
storage_paths:
  # 基础路径
  base_path: "data/crawled"
  
  # 按关键词分类存储
  organize_by_keyword: true
  
  # 目录结构
  directory_structure:
    images: "images"
    metadata: "metadata"
    logs: "logs"
    temp: "temp"
    
  # 文件命名规则
  naming_convention:
    pattern: "{keyword}_{timestamp}_{index}"
    timestamp_format: "%Y%m%d_%H%M%S"
    index_digits: 6

# 元数据保存配置
metadata_saving:
  # 保存元数据
  enabled: true
  
  # 保存内容
  save_content:
    url: true
    source_engine: true
    download_time: true
    file_size: true
    image_dimensions: true
    keyword: true
    
  # 元数据格式
  format: "json"  # json, csv, yaml
  
  # 批量保存
  batch_save: true
  batch_size: 100

# 错误处理和重试配置
error_handling:
  # 重试设置
  retry:
    enabled: true
    max_retries: 3
    retry_delay: 2.0
    exponential_backoff: true
    
  # 错误处理策略
  error_strategy:
    continue_on_error: true
    log_errors: true
    save_failed_urls: true
    
  # 超时处理
  timeout_handling:
    connection_timeout: 10.0
    read_timeout: 30.0
    total_timeout: 60.0

# 反爬虫应对策略
anti_bot_measures:
  # 用户代理轮换
  user_agents:
    enabled: true
    rotate: true
    custom_agents:
      - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
      - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
      - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
      
  # 代理设置
  proxies:
    enabled: false
    proxy_list: []  # ["http://proxy1:port", "http://proxy2:port"]
    rotate: true
    
  # 请求头设置
  headers:
    accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
    accept_language: "en-US,en;q=0.5"
    accept_encoding: "gzip, deflate"
    connection: "keep-alive"
    upgrade_insecure_requests: "1"
    
  # 速率限制
  rate_limiting:
    requests_per_second: 1.0
    burst_size: 5
    adaptive: true

# 后处理配置
post_processing:
  # 自动标注
  auto_annotation:
    enabled: false
    model_path: ""
    confidence_threshold: 0.5
    
  # 图像预处理
  image_preprocessing:
    resize: false
    target_size: [640, 640]
    normalize: false
    
  # 数据验证
  validation:
    enabled: true
    check_corruption: true
    check_format: true
    remove_invalid: true

# 监控和统计
monitoring:
  # 进度监控
  progress_tracking:
    enabled: true
    update_interval: 10  # 秒
    save_progress: true
    
  # 统计信息
  statistics:
    enabled: true
    track_success_rate: true
    track_download_speed: true
    track_error_types: true
    
  # 实时日志
  logging:
    enabled: true
    log_level: "INFO"
    log_file: "crawler.log"
    console_output: true

# 批量爬取配置
batch_crawling:
  # 批量任务
  enabled: true
  
  # 任务调度
  scheduler:
    enabled: false
    cron_expression: "0 2 * * *"  # 每天凌晨2点
    max_concurrent_jobs: 2
    
  # 任务队列
  task_queue:
    max_size: 1000
    priority_enabled: true
    persistence: true

# 集成配置
integration:
  # 与数据处理模块集成
  data_processing:
    auto_trigger: false
    target_format: "yolo"
    
  # 通知设置
  notifications:
    enabled: false
    email: ""
    webhook: ""
    
  # API接口
  api:
    enabled: false
    port: 8080
    auth_required: false 
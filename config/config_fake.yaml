# Yolo_Utils 主配置文件

# 项目全局设置
project:
  name: "Yolo_Utils"
  version: "1.0.0"
  description: "YOLO模型训练与数据处理工具集"
  
# 工作目录配置
paths:
  workspace: "."
  data_dir: "data"
  models_dir: "models"
  runs_dir: "runs"
  logs_dir: "logs"
  config_dir: "config"

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_enabled: true
  console_enabled: true
  max_file_size: "10MB"
  backup_count: 5

# GPU设置
device:
  use_gpu: true
  gpu_ids: [0]  # 使用的GPU ID列表
  memory_limit: 0.9  # GPU内存使用限制 (0.0-1.0)
  allow_growth: true  # 是否允许GPU内存动态增长

# 模块开关控制
modules:
  data_acquisition:
    enabled: true
    crawler_enabled: true
    downloader_enabled: true
  
  data_processing:
    enabled: true
    format_converter_enabled: true
    data_standardizer_enabled: true
    annotation_tools_enabled: true
  
  data_integration:
    enabled: true
    dataset_merger_enabled: true
    class_mapper_enabled: true
    data_balancer_enabled: true
  
  model_training:
    enabled: true
    direct_training_enabled: true
    incremental_training_enabled: true
  
  model_evaluation:
    enabled: true
    validator_enabled: true
    tester_enabled: true

# 公共参数配置
common:
  # 图像处理参数
  image:
    default_size: [640, 640]
    supported_formats: ["jpg", "jpeg", "png", "bmp", "tiff"]
    quality_threshold: 0.8
  
  # 标注参数
  annotation:
    confidence_threshold: 0.25
    iou_threshold: 0.7
    max_objects_per_image: 100
  
  # 数据集分割比例
  dataset_split:
    train: 0.7
    val: 0.2
    test: 0.1

# 性能设置
performance:
  multiprocessing:
    enabled: true
    max_workers: 4
  
  cache:
    enabled: true
    max_cache_size: "1GB"
  
  batch_processing:
    default_batch_size: 32
    max_batch_size: 128

# 模块间协调配置
coordination:
  # 数据流配置
  data_flow:
    auto_cleanup: true
    intermediate_save: true
    backup_enabled: true
  
  # 状态管理
  state_management:
    checkpoint_enabled: true
    resume_enabled: true
    auto_save_interval: 300  # 秒

# 数据库配置
database:
  host: "localhost"
  port: 3306
  name: "database_name"
  user: "username"
  password: "password"

# 图片备份配置
image_backup:
  search_conditions: ["5", "7", "8"]  # 搜索条件
  similarity_threshold: 90            # 图片相似度阈值（百分比）
  source_dir: "/path/to/source/images"  # 源图片目录
  date_range:                         # 日期范围配置
    start_date: "2025-02-01 00:00:00"
    end_date: "2025-06-30 00:00:00"
  output_dirs:                        # 输出目录配置
    initfile: "data/raw/initfile"
    intefile: "data/raw/intefile"
    initfile_unique: "data/processed/initfile_unique"
    intefile_unique: "data/processed/intefile_unique"

# 安全设置
security:
  # API密钥管理
  api_keys:
    kaggle_key_file: "~/.kaggle/kaggle.json"
    huggingface_token_file: "~/.huggingface/token"
  
  # 文件访问控制
  file_access:
    allowed_extensions: [".yaml", ".yml", ".json", ".txt", ".py"]
    max_file_size: "100MB" 
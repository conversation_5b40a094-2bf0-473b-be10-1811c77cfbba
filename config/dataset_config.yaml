# 数据集配置文件

# 数据源配置
data_sources:
  # Kaggle数据源
  kaggle:
    enabled: true
    base_path: "data/kaggle"
    datasets:
      - name: "construction-equipment"
        dataset_id: "user/construction-equipment"
        local_path: "construction-equipment"
        description: "建筑设备检测数据集"
      - name: "bluegreen-algae-dataset"
        dataset_id: "user/bluegreen-algae-dataset"
        local_path: "bluegreen-algae-dataset"
        description: "蓝绿藻检测数据集"
  
  # HuggingFace数据源
  # huggingface:
  #   enabled: true
  #   base_path: "data/huggingface"
  #   datasets:
  #     - name: "coco-detection"
  #       dataset_id: "detection-datasets/coco"
  #       local_path: "coco-detection"
  #       description: "COCO目标检测数据集"
  
  # OpenImages数据源
  # openimages:
  #   enabled: true
  #   base_path: "data/openimages"
  #   version: "v7"
  #   categories:
  #     - "Person"
  
  # 自定义数据源
  custom:
    enabled: true
    base_path: "data/custom"
    datasets:
      - name: "custom_dataset"
        path: "data/custom/dataset1"
        format: "yolo"  # yolo, coco, voc
        description: "自定义数据集"

# 数据集路径管理
path_management:
  # 原始数据路径
  raw_data:
    images: "images"
    labels: "labels"
    annotations: "annotations"
  
  # 处理后数据路径
  processed_data:
    base_path: "data/processed"
    images: "images"
    labels: "labels"
  
  # 整合后数据路径
  integrated_data:
    base_path: "data/integrated"
    train: "train"
    val: "val"
    test: "test"
  
  # 最终训练数据路径
  final_data:
    base_path: "data/final"
    train: "train/images"
    val: "val/images"
    test: "test/images"

# 数据整合配置
data_integration:
  # 数据集整合设置
  datasets:
    # 要整合的数据集列表
    - name: "construction-equipment"
      path: "data/processed/kaggle/construction-equipment"
      format: "yolo"
      enabled: true
      selected_classes:
        - "Excavator"
    
    - name: "bluegreen-algae-dataset"
      path: "data/processed/kaggle/bluegreen-algae-dataset"
      format: "yolo"
      enabled: true
      selected_classes:
        - "Algae"
        - "BlueGreen_Algae"

    - name: "MASATI-V2"
      path: "data/processed/kaggle/MASATI-V2"
      format: "yolo"
      enabled: true
      selected_classes:
        - "boat"

    - name: "Ship-Dataset"
      path: "data/processed/kaggle/Ship-Dataset"
      format: "yolo"
      enabled: true
      selected_classes:
        - "boat"
    
    - name: "Ships-in-aerial-images"
      path: "data/processed/kaggle/Ships-in-aerial-images"
      format: "yolo"
      enabled: true
      selected_classes:
        - "boat"

    - name: "backup_annotation"
      path: "data/processed/backup_annotation"
      format: "yolo"
      enabled: true
      selected_classes:
        - "施工"
        - "管路异常"
        - "船只"

    - name: "VisDrone2019"
      path: "data/processed/VisDrone2019"
      format: "yolo"
      enabled: true
      selected_classes:
        - "person"
    
    # - name: "coco-detection"
    #   path: "data/processed/coco-detection"
    #   format: "yolo"
    #   enabled: false
    #   selected_classes:
    #     - "person"
    #     - "car"
    #     - "boat"
  
  # 类别映射配置
  class_mapping:
    # 全局目标类别定义（最终要生成的类别）
    global_classes:
      - "boat"
      - "person"
      - "excavator"
      - "algae"
      - "hpipe"
      - "aeration"
    
    # 类别映射规则：source_class -> target_class
    mapping_rules:
      # 船只相关
      "boat": "boat"
      "ship": "boat"
      "船只": "boat"
  
      # 人员相关
      "person": "person"

      # 建筑设备相关
      "Excavator": "excavator"
      "施工": "excavator"
      
      # 藻类相关
      "Algae": "algae"
      "BlueGreen_Algae": "algae"
      
      # 管道相关
      "hpipe": "hpipe"
      "管路异常": "hpipe"

      # 曝气相关
      "aeration": "aeration"
    
    # 自动映射设置
    auto_mapping:
      enabled: true
      similarity_threshold: 0.8
      manual_review_required: true
      
    # 映射验证设置
    validation:
      strict_mode: true  # 严格模式：未映射的类别会报错
      allow_unknown_classes: false  # 是否允许未知类别
      log_unmapped_classes: true  # 记录未映射的类别
    
    # 数据集间类别映射
    # dataset_mappings:
    #   construction-equipment:
    #     source_classes:
    #       - "Excavator"
    #     target_mapping:
    #       "Excavator": "excavator"
      
    #   bluegreen-algae-dataset:
    #     source_classes:
    #       - "Algae"
    #       - "BlueGreen_Algae"
    #     target_mapping:
    #       "Algae": "algae"
    #       "BlueGreen_Algae": "algae"
  
  # 数据集合并设置
  dataset_merger:
    # 输出目录
    output_dir: "data/integrated"
    
    # 文件命名策略
    file_naming:
      strategy: "dataset_prefix"  # dataset_prefix, sequential, hash
      prefix_separator: "_"
      include_original_name: true
      
    # 冲突处理
    conflict_resolution:
      duplicate_files: "rename"  # rename, skip, overwrite
      class_conflicts: "strict"  # strict, merge, ignore
      
    # 进度显示
    progress_bar: true
    verbose: true
    
    # 备份设置
    backup:
      enabled: true
      backup_dir: "data/backup/integration"
      
    # 空标签文件处理
    empty_label_keep_ratio: 0.1  # 保留10%的空标签文件作为负样本
      
  # 数据分割配置
  data_split:
    # 分割比例
    split_ratios:
      train: 0.7
      val: 0.2
      test: 0.1
      
  # 最终数据集构建配置
  final_dataset:
    # 输出目录
    output_dir: "data/final"
    
    # 数据集描述
    dataset_info:
      name: "Custom Detection Dataset"
      description: "多源数据集整合的目标检测数据集"
      version: "1.0.0"
      authors: ["Max"]
      license: "MIT"
      
    # YOLO格式配置
    yolo_config:
      generate_yaml: true
      yaml_filename: "dataset.yaml"
      include_class_colors: false
      
    # 统计信息
    generate_statistics: true
    statistics_format: ["json", "txt"]
    
    # 验证设置
    validation:
      enabled: true
      check_file_integrity: true
      verify_class_distribution: true
      generate_sample_images: true
      sample_count: 10
      
  # 数据平衡配置
  data_balancing:
    enabled: false
    
    # 平衡策略
    strategy: "mixed"  # undersample, oversample, mixed
    
    # 目标设置
    target_type: "median"  # min_class, max_class, median, custom
    custom_target_count: 5000
    
    # 过采样设置
    oversample:
      method: "duplicate"  # duplicate, augment
      max_duplicates: 1
      
    # 欠采样设置
    undersample:
      method: "random"  # random, diverse
      preserve_important: true

  # 清理配置
  cleanup:
    enabled: true
    cleanup_intermediate: true
    backup_before_cleanup: false
    backup_dir: "data/backup/cleanup"

# 数据预处理参数
preprocessing:
  # 图像预处理
  image_processing:
    resize:
      enabled: true
      target_size: [640, 640]
      keep_aspect_ratio: true
      padding_color: [114, 114, 114]
    
    quality_check:
      enabled: true
      min_size: [32, 32]
      max_size: [4096, 4096]
      min_quality: 0.7
      check_corruption: true
    
    normalization:
      enabled: false
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
  
  # 标签预处理
  label_processing:
    standardization:
      enabled: true
      normalize_coordinates: true
      filter_small_objects: true
      min_object_size: 100  # 像素

# 数据增强配置
data_augmentation:
  enabled: false  # 在数据预处理阶段通常不启用，留给训练阶段
  techniques:
    - "horizontal_flip"
    - "rotation"
    - "brightness"
    - "contrast"

# 质量控制
quality_control:
  # 数据质量检查
  quality_check:
    enabled: true
    image_corruption_check: true
    label_consistency_check: true
    duplicate_detection: true
    check_bounds: true  # 从label_processing.validation移动过来
    check_format: true  # 从label_processing.validation移动过来
    remove_invalid: true  # 从label_processing.validation移动过来
  
  # 统计分析
  statistics:
    enabled: true
    class_distribution: true
    image_size_distribution: true
    annotation_statistics: true
  
  # 报告生成
  reporting:
    enabled: true
    detailed_report: true
    visualization: true
    export_format: ["json", "html"]

# 数据集版本管理
version_control:
  enabled: true
  track_changes: true
  backup_original: true
  version_naming: "v{major}.{minor}.{patch}"

# 备份标注配置
backup_annotation:
  # 数据源配置
  data_source:
    csv_file: "data/processed/backup/unique_images.csv"
    image_dir: "data/processed/backup"
    image_base_path: "data/backup_images"
    annotation_base_path: "data/backup_annotations"
  
  # 输出配置
  output:
    base_dir: "data/processed/backup_annotation"
    images_dir: "images"
    labels_dir: "labels"
    debug_dir: "debug"
  
  # 处理参数
  processing:
    # 目标类别过滤
    target_classes: ["施工", "管路异常", "船只"]  # 空列表表示处理所有类别
    max_process_count: 2  # -1表示处理所有数据
    
    # 图像比较参数
    image_comparison:
      enabled: true
      threshold: 30
      min_contour_area: 500
      max_contour_area: 50000  # 最大轮廓面积，过滤异常大区域
    
    # 颜色检测参数
    color_detection:
      enabled: true
      use_as_verification: true  # 是否将颜色检测用作分类验证
      color_ranges:
        "施工":
          hsv_range: [[22, 175, 212], [38, 255, 255]]  # 黄色范围 (FCF928)
          class_id: 0
          class_code: 'excavator'
        "管路异常":
          hsv_range: [[0, 215, 214], [8, 255, 255]]  # 红色范围 (FE0001)
          class_id: 1
          class_code: 'hpipe'
        "船只":
          hsv_range: [[112, 214, 182], [128, 255, 255]]  # 蓝色范围 (0103DE)
          class_id: 2
          class_code: 'boat'
    
    # OCR识别参数
    ocr:
      enabled: false
      confidence_threshold: 0.7
    
    # 轮廓合并策略: intersection, union, color_only, comparison_only
    contour_merge_strategy: "color_only"
    
    # 优先处理文件配置
    priority_files:
      enabled: true  # 是否启用优先文件功能
      match_field: "intefile_name"  # 匹配的CSV字段名
      file_list:  # 优先处理的文件名列表
        # - "20250629141847_109_LinChen1_1751178141306_predict.jpg"
        # - "20250306100106_102_ChenNanFanShuiZhan2_1741226527243_predict.jpg"
        # - "20250405082207_105_ShangXianWu2_1743812576450_predict.jpg"
        # - "20250629084615_104_ShangTangHe1_1751158145131_predict.jpg"
        # - "20250322101123_110_WangJiaJin1_1742609562943_predict.jpg"
        # - "20250320155017_102_ChenXiDaQiao1_1742457089359_predict.jpg"
        - "20250320150403_104_ShangTangHe1_1742454289770_predict.jpg"
        # - "20250320144545_110_WangJiaJin1_1742453214023_predict.jpg"
        # - "20250320103415_104_ShangTangHe1_1742438101817_predict.jpg"
        - "20250320103405_104_ShangTangHe1_1742438091750_predict.jpg"
    
    # 多对象检测配置
    multi_object:
      min_area_threshold: 550  # 最小轮廓面积阈值，过滤过小的对象
    
    # 后处理配置
    post_processing:
      # 步骤1：面积过滤（上下限）
      max_area_ratio: 0.9        # 最大面积比例阈值，超过此比例的对象会被过滤
      min_area_threshold: 500    # 最小面积阈值，小于此值的对象会被过滤
      
      # 步骤2：边界扩充合并参数
      boundary_expansion:
        edge_tolerance: 2        # 边界容差（像素），用于判断边界是否一致
      
      # 步骤3：重叠合并参数
      merge_params:
        vertical_gap_threshold: 5         # 垂直间隙阈值（像素），小于此值认为垂直相邻
        width_similarity_threshold: 0.05  # 宽度相似度阈值，差异小于此比例认为宽度相似
        area_overlap_threshold: 0.8       # 面积重叠阈值，超过此比例认为需要合并
    
    # 重叠方框分离配置
    overlap_separation:
      enabled: true
      strategy: "watershed"  # 可选: "morphology", "watershed", "corner_clustering", "template_matching"
      
      # 重叠检测参数
      detection_params:
        solidity_threshold: 0.8      # 凸度阈值，低于此值认为可能重叠
        extent_threshold: 0.6        # 矩形度阈值，低于此值认为可能重叠
        min_area_for_check: 1000     # 最小面积阈值，小于此值不检查重叠
        
      # 策略1: 形态学操作优化
      morphology_params:
        kernel_size: [2, 2]          # 使用更小的核减少合并
        iterations: 1                # 减少迭代次数
        use_edge_enhancement: true   # 是否结合边缘检测
        
      # 策略2: 分水岭算法分离
      watershed_params:
        min_distance: 15             # 种子点最小距离
        distance_threshold: 0.3      # 距离变换阈值比例
        
      # 策略3: 角点检测+聚类
      corner_clustering_params:
        max_corners: 20              # 最大角点数
        quality_level: 0.01          # 角点质量阈值
        min_distance: 10             # 角点最小距离
        cluster_eps: 20              # DBSCAN聚类距离参数
        cluster_min_samples: 2       # 聚类最小样本数
        
      # 策略4: 模板匹配
      template_matching_params:
        enabled: false               # 是否启用模板匹配
        threshold: 0.8               # 匹配阈值
        templates:                   # 各类别的模板配置
          excavator:
            width: 50
            height: 50
            thickness: 2
          hpipe:
            width: 40
            height: 40
            thickness: 2
          boat:
            width: 60
            height: 45
            thickness: 2
  
  # 验证配置
  validation:
    enabled: true
    generate_debug_images: true
    create_evaluation_template: true
  
  # 调试模式
  debug_mode: False

# 超参数搜索数据抽取配置
hyperparameter_search_extractor:
  # 数据源和输出配置
  source_dir: "data/final"
  output_dir: "data/search"
  
  # 抽取参数
  sample_ratio: 0.2  # 每个类别抽取20%的数据
  random_seed: 42
  
  # 分割配置
  split_ratios:
    train: 0.8
    val: 0.2
  
  # 数据处理选项
  preserve_class_distribution: true  # 是否保持类别分布
  
  # 报告生成
  generate_statistics: true
  statistics_formats: ["json"]
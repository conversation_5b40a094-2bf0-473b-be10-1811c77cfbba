import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tools import trainer_tool, tester_tool, config_advisor_tool

def main():
    print("\n🧠 欢迎使用 YOLO_Utils 主工具入口")
    print("请选择要执行的任务：")
    print("1. 模型训练")
    print("2. 模型测试")
    print("3. 智能配置建议")

    choice = input("请输入数字选择操作 (1-3): ").strip()

    if choice == '1':
        print("\n训练模式：")
        print("  1. 固定参数训练")
        print("  2. 超参数搜索训练")
        print("  3. 恢复训练 (从checkpoint)")
        print("  4. 列出所有模型")
        print("  5. 查看最新模型")
        print("  6. 查看最佳模型")

        sub_choice = input("请选择训练子任务: ").strip()
        config_path = input("请输入配置文件路径 [默认: config/model_config.yaml]: ").strip() or "config/model_config.yaml"

        if sub_choice == '1':
            trainer_tool.train_with_fixed_params(config_path)
        elif sub_choice == '2':
            trainer_tool.train_with_hyperparameter_search(config_path)
        elif sub_choice == '3':
            ckpt = input("请输入checkpoint路径: ").strip()
            trainer_tool.resume_training(ckpt, config_path)
        elif sub_choice == '4':
            trainer_tool.list_all_models(config_path)
        elif sub_choice == '5':
            trainer_tool.get_latest_model_info(config_path=config_path)
        elif sub_choice == '6':
            trainer_tool.get_best_model_info(config_path=config_path)
        else:
            print("无效的训练子任务选择。")

    elif choice == '2':
        print("\n测试模式：")
        print("  1. 自动测试（最佳模型）")
        print("  2. 测试最新模型")
        print("  3. 测试最佳模型")
        print("  4. 测试指定模型")
        print("  5. 快速模型比较")
        print("  6. 比较所有模型")

        sub_choice = input("请选择测试子任务: ").strip()
        config_path = input("请输入配置文件路径 [默认: config/model_config.yaml]: ").strip() or "config/model_config.yaml"

        if sub_choice == '1':
            tester_tool.auto_test(config_path)
        elif sub_choice == '2':
            tester_tool.test_latest_model(config_path=config_path)
        elif sub_choice == '3':
            tester_tool.test_best_model(config_path=config_path)
        elif sub_choice == '4':
            model_path = input("请输入模型路径: ").strip()
            tester_tool.test_specific_model(model_path, config_path)
        elif sub_choice == '5':
            tester_tool.quick_model_comparison(config_path)
        elif sub_choice == '6':
            tester_tool.compare_all_models(config_path)
        else:
            print("无效的测试子任务选择。")

    elif choice == '3':
        print("\n配置建议工具：")
        print("  1. 分析系统并生成配置建议")
        print("  2. 自动更新配置文件")

        sub_choice = input("请选择操作 (1-2): ").strip()
        if sub_choice == '1':
            config_advisor_tool.analyze_system_and_recommend()
        elif sub_choice == '2':
            config_advisor_tool.update_config_with_recommendations()
        else:
            print("无效的配置建议选择。")

    else:
        print("无效选择。请重试。")

if __name__ == "__main__":
    main()

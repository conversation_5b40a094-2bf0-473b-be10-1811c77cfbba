# 重叠方框分离功能使用说明

## 功能概述

重叠方框分离功能解决了标注图片中方框重叠导致的检测精度问题，提供4种不同的分离策略。

## 配置位置

配置位于 `config/dataset_config.yaml` 文件中的 `backup_annotation.processing.overlap_separation` 部分。

## 快速开始

### 1. 启用功能
```yaml
backup_annotation:
  processing:
    overlap_separation:
      enabled: true
      strategy: "watershed"  # 推荐策略
```

### 2. 四种策略选择

#### 策略1: morphology (形态学优化)
- **适用场景**: 轻微重叠，方框边界清晰
- **优点**: 速度快，内存占用少
- **缺点**: 对严重重叠效果有限

```yaml
strategy: "morphology"
```

#### 策略2: watershed (分水岭算法) 【推荐】
- **适用场景**: 中等到严重重叠，同颜色方框重叠
- **优点**: 效果好，能有效分离连接区域
- **缺点**: 计算复杂度较高

```yaml
strategy: "watershed"
```

#### 策略3: corner_clustering (角点聚类)
- **适用场景**: 方框有明显角点特征
- **优点**: 基于几何特征，对规则方框效果好
- **缺点**: 需要sklearn依赖，对模糊边界效果差

```yaml
strategy: "corner_clustering"
```

#### 策略4: template_matching (模板匹配)
- **适用场景**: 方框大小相对固定，形状标准
- **优点**: 对标准形状检测精度高
- **缺点**: 需要预定义模板，对形状变化敏感

```yaml
strategy: "template_matching"
template_matching_params:
  enabled: true
```

### 3. 参数调优

#### 重叠检测敏感度调整
```yaml
detection_params:
  solidity_threshold: 0.8      # 凸度阈值：0.6(宽松) - 0.9(严格)
  extent_threshold: 0.6        # 矩形度阈值：0.5(宽松) - 0.9(严格)
  min_area_for_check: 1000     # 最小检查面积：500(小对象) - 2000(大对象)
```

#### 分水岭算法参数
```yaml
watershed_params:
  min_distance: 15             # 种子点距离：5(密集) - 20(稀疏)
  distance_threshold: 0.3      # 距离阈值：0.2(敏感) - 0.4(保守)
```

### 4. 启用调试模式
```yaml
debug_mode: true
```
生成的调试图像保存在 `debug/` 目录，可以直观查看分离效果。

## 测试建议

### 推荐测试顺序
1. **watershed** - 最通用，效果较好
2. **morphology** - 简单快速，作为基准对比
3. **corner_clustering** - 角点清晰时测试
4. **template_matching** - 标准形状时测试

### 快速配置模板

#### 保守策略 (减少误检)
```yaml
overlap_separation:
  enabled: true
  strategy: "morphology"
  detection_params:
    solidity_threshold: 0.9
    extent_threshold: 0.8
    min_area_for_check: 1500
```

#### 激进策略 (增加检测)
```yaml
overlap_separation:
  enabled: true
  strategy: "watershed"
  detection_params:
    solidity_threshold: 0.7
    extent_threshold: 0.5
    min_area_for_check: 600
  watershed_params:
    min_distance: 8
    distance_threshold: 0.2
```

#### 平衡策略 (默认推荐)
```yaml
overlap_separation:
  enabled: true
  strategy: "watershed"
  detection_params:
    solidity_threshold: 0.8
    extent_threshold: 0.6
    min_area_for_check: 1000
  watershed_params:
    min_distance: 15
    distance_threshold: 0.3
```

## 效果评估

1. **检测数量对比**: 分离前后的对象数量变化
2. **准确性验证**: 查看调试图像验证分离结果
3. **处理速度**: 不同策略的性能对比
4. **误检率**: 错误分离的比例

## 常见问题

**Q: 分离效果不理想？**
- 降低检测阈值参数
- 尝试不同策略
- 调整策略特定参数

**Q: 产生过多误检？**
- 提高检测阈值参数
- 增加最小面积限制
- 使用更保守的策略

**Q: 处理速度太慢？**
- 使用morphology策略
- 增加min_area_for_check
- 减少处理的图像数量

**Q: sklearn依赖问题？**
```bash
pip install scikit-learn
```
或避免使用corner_clustering策略。

## 总结

建议从watershed策略开始测试，根据实际效果调整参数或切换策略。启用调试模式可以直观看到分离效果，帮助选择最适合的配置。 
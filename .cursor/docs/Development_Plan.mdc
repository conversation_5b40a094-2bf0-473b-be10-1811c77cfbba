---
description: 当指定计划、更新计划、执行计划后都需要更新该文件
globs: 
alwaysApply: false
---
# Yolo_Utils 项目重构开发计划

## 大的功能模块

- [x] 基础架构搭建
- [ ] 数据处理模块开发
- [ ] 数据整合模块开发
- [ ] 训练模块重构
- [ ] 评估模块重构
- [ ] 数据获取模块开发

## 具体开发任务

### 基础架构搭建
- [x] 创建项目目录结构
- [x] 创建config.yaml主配置文件模板
- [x] 创建dataset_config.yaml数据集配置文件模板
- [x] 创建training_config.yaml训练配置文件模板
- [x] 创建crawler_config.yaml爬虫配置文件模板
- [x] 实现基础支撑模块（base_classes.py, config_manager.py, logger.py, exceptions.py）

### 数据处理模块开发
- [ ] 实现格式转换器（COCOToYOLOConverter, VOCToYOLOConverter, MaskToBBoxConverter, CustomFormatConverter）
- [x] 实现备份标注转换器（BackupAnnotationConverter）
- [ ] 实现数据标准化工具（ImageProcessor, LabelProcessor, QualityChecker）
- [x] 扩展标注生成器（AnnotationGenerator）提供通用YOLO标注生成服务
- [ ] 实现标注验证器（AnnotationValidator）
- [x] 创建备份标注工具（tools/backup_annotation_tool.py）
- [ ] 创建数据处理工具入口（tools/dataset_processor.py）

### 数据整合模块开发
- [ ] 实现数据集合并器（DatasetMerger）
- [ ] 实现类别映射管理器（ClassMapper）
- [ ] 实现数据平衡工具（DataBalancer）
- [ ] 实现最终数据集构建器（FinalDatasetBuilder）
- [ ] 创建数据整合工具入口（tools/dataset_merger.py）

### 训练模块重构
- [ ] 分析现有训练代码
- [ ] 实现训练策略模块（DirectTraining, IncrementalTraining, ParameterOptimizer）
- [ ] 重构训练器主类（Trainer）
- [ ] 实现训练工具模块（data_loader.py, model_utils.py, checkpoint_manager.py）
- [ ] 创建训练工具入口（tools/trainer_tool.py）

### 评估模块重构
- [ ] 重构验证器（validator.py）
- [ ] 重构测试器（tester.py）
- [ ] 实现评估指标模块（yolo_metrics.py, custom_metrics.py）
- [ ] 实现结果可视化工具（result_plotter.py, confusion_matrix.py）
- [ ] 创建评估工具入口（tools/evaluator_tool.py）

### 数据获取模块开发
- [ ] 实现网络爬虫（WebCrawler, BatchCrawler, crawler_utils.py）
- [ ] 实现数据下载器（kaggle_downloader.py, huggingface_downloader.py, openimages_downloader.py）
- [x] 整合数据库图片备份工具（扩展配置管理、创建tools/image_backup_tool.py）
- [x] 整合备份标注转换功能（创建BackupAnnotationConverter、扩展配置系统、创建独立工具）
- [ ] 创建爬虫工具入口（tools/crawler_tool.py）
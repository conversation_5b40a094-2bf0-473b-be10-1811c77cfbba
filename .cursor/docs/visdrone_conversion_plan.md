# VisDrone2019数据集转换方案

## 1. 项目概述

本文档描述了将VisDrone2019数据集从原始格式转换为YOLO格式的完整方案，以便集成到Yolo-utils项目的数据处理流程中。

## 2. 数据集分析

### 2.1 原始数据结构
```
data/raw/VisDrone2019/
├── dataset.yaml                    # 数据集配置文件
├── VisDrone2019-DET-train/        # 训练集
│   ├── images/                    # 图像文件
│   └── annotations/               # 标注文件
├── VisDrone2019-DET-val/          # 验证集
│   ├── images/
│   └── annotations/
└── VisDrone2019-DET-test-dev/     # 测试集
    ├── images/
    └── annotations/
```

### 2.2 原始标注格式
每行格式：`<bbox_left>,<bbox_top>,<bbox_width>,<bbox_height>,<score>,<object_category>,<truncation>,<occlusion>`

参数说明：
- `bbox_left`, `bbox_top`: 边界框左上角坐标（绝对像素坐标）
- `bbox_width`, `bbox_height`: 边界框宽度和高度（像素）
- `score`: 1表示参与评估，0表示忽略
- `object_category`: 类别ID（0-9对应10个类别）
- `truncation`, `occlusion`: 截断和遮挡程度

### 2.3 类别映射
原始类别（只使用前两个）：
- 0: pedestrian → person (class_id: 0)
- 1: people → person (class_id: 0)
- 2-9: 其他类别（忽略）

**注意**：两个类别都映射为同一个YOLO类别ID (0)，表示"person"

## 3. 转换方案

### 3.1 目标输出结构
```
data/processed/VisDrone2019/
├── images/
│   ├── train/
│   ├── val/
│   └── test/
├── labels/
│   ├── train/
│   ├── val/
│   └── test/
├── validation/
│   ├── train_validation.jpg
│   ├── val_validation.jpg
│   └── test_validation.jpg
└── dataset.yaml
```

**注意**：此结构与项目中其他数据集保持一致，外层为`images`和`labels`目录，内层为`train/val/test`分割。

### 3.2 坐标转换算法
从VisDrone格式转换为YOLO格式：

**输入**：`bbox_left, bbox_top, bbox_width, bbox_height`（绝对坐标）
**输出**：`class_id x_center y_center width height`（YOLO格式）

**转换公式**：
```python
class_id = 0  # 所有pedestrian和people都映射为person
x_center = (bbox_left + bbox_width/2) / image_width
y_center = (bbox_top + bbox_height/2) / image_height
width = bbox_width / image_width
height = bbox_height / image_height
```

### 3.3 过滤条件
- 只保留`score=1`的标注
- 只处理`object_category`为0或1的目标
- 过滤掉无效坐标（超出图像边界）

## 4. 实现任务清单

### 4.1 核心转换器实现
- [x] 创建`src/data_processing/format_converters/visdrone_to_yolo.py`
- [x] 实现`VisDroneToYOLOConverter`类
- [x] 实现坐标转换方法`_convert_bbox_format()`
- [x] 实现数据集处理方法`_process_dataset()`
- [x] 实现标注文件转换方法`_convert_annotation_file()`
- [x] 实现输出结构创建方法`_create_output_structure()`
- [x] 实现生成 dataset.yaml 文件方法`_generate_dataset_yaml`

### 4.2 工具入口创建
- [x] 创建`tools/visdrone_converter_tool.py`
- [x] 提供IDE直接运行支持
- [x] 实现进度显示和日志记录

### 4.3 配置文件更新
- [x] 确认`config/dataset_config.yaml`已包含VisDrone2019配置
- [x] 验证类别映射规则正确性
- [x] 统一目录结构与其他数据集保持一致

### 4.4 验证和测试
- [x] 实现基本转换功能
- [x] 集成现有工具函数
- [x] 实现验证图片生成功能
- [x] 添加目标目录存在性检查
- [x] 统一输出目录结构（images/labels外层，train/val/test内层）

## 5. 核心代码结构

### 5.1 VisDroneToYOLOConverter类设计
```python
class VisDroneToYOLOConverter(BaseConverter):
    def __init__(self, config):
        super().__init__(config)
        self.visdrone_classes = {0: 'pedestrian', 1: 'people'}
    
    def convert(self, source_path, target_path):
        # 主转换方法
        
    def _create_output_structure(self, target_path):
        # 创建输出目录结构（images/labels外层，train/val/test内层）
        
    def _process_dataset(self, source_dir, target_images_dir, target_labels_dir):
        # 处理单个数据集（train/val/test）
        
    def _convert_annotation_file(self, ann_path, img_path, output_path):
        # 转换单个标注文件
        
    def _convert_bbox_format(self, bbox_left, bbox_top, bbox_width, bbox_height, img_width, img_height):
        # 坐标格式转换
        
    def _check_target_exists(self, target_path):
        # 检查目标目录是否存在且有数据
        
    def _generate_validation_images(self, target_path):
        # 生成验证图片
        
    def _draw_annotations(self, image_path, label_path, output_path):
        # 在图像上绘制标注框

    def _generate_dataset_yaml(self, target_path: str) -> bool:
        # 生成 dataset.yaml 文件
```

### 5.2 工具入口设计
```python
def convert_visdrone_to_yolo():
    """转换VisDrone2019数据集为YOLO格式"""
    
def main():
    # IDE运行入口
    convert_visdrone_to_yolo()
```

## 6. 配置文件更新

### 6.1 dataset_config.yaml更新内容（仅在需要与dataset_merger集成时）
```yaml
data_integration:
  datasets:
    - name: "VisDrone2019"
      path: "data/processed/VisDrone2019"
      format: "yolo"
      enabled: true
      selected_classes:
        - "person"

  class_mapping:
    mapping_rules:
      "pedestrian": "person"
      "people": "person"
```

**注意**：当前转换器可以独立运行，不需要修改配置文件。只有在后续需要与`dataset_merger`集成时才需要更新配置。

## 7. 质量控制

### 7.1 内置验证功能
- [x] 坐标范围验证（确保在0-1之间）
- [x] 边界框有效性检查
- [x] 文件对应关系验证
- [x] 转换统计信息输出

### 7.2 日志信息
转换过程中会输出：
- 处理进度信息
- 转换统计（总文件数、成功数、错误数）
- 标注统计（总标注数、有效标注数）
- 错误信息和警告

## 8. 使用说明

### 8.1 运行转换
```python
# 在IDE中运行
python tools/visdrone_converter_tool.py
```

### 8.2 智能处理模式
- **首次运行**：如果目标目录不存在，执行完整的数据转换过程
- **重复运行**：如果目标目录已存在且有数据，跳过转换直接生成验证图片

### 8.3 验证结果
转换完成后检查：
- `data/processed/VisDrone2019/`目录结构
- `data/processed/VisDrone2019/validation/`目录中的验证图片：
  - `train_validation.jpg` - 训练集验证图片
  - `val_validation.jpg` - 验证集验证图片  
  - `test_validation.jpg` - 测试集验证图片
- 验证图片上的绿色边界框和"person"标签

## 9. 集成到数据处理流程

转换完成后，VisDrone2019数据集将自动集成到项目的数据整合流程中，可以通过`tools/dataset_merger.py`与其他数据集合并，最终生成用于YOLO训练的统一数据集。

## 10. 注意事项

1. 确保原始数据完整性，特别是图像和标注文件的对应关系
2. 转换过程中保持原始数据不变，只生成处理后的副本
3. 定期验证转换结果，确保数据质量
4. 关注内存使用，大数据集处理时考虑分批处理 
# Yolo-utils - YOLO模型训练与数据处理工具集

## 项目概述

Yolo-utils是一个专门用于收集、准备、整合用于YOLO模型训练的数据，并提供完整的模型训练、验证、测试流程的工具集。项目的核心目标是解决多源异构数据集的统一整合问题，以及YOLO模型的高效训练与优化。

项目支持多种数据源：
- **网络爬虫数据**：通过关键词搜索获取网络图像数据
- **公开数据集**：Kaggle、HuggingFace、OpenImages等平台数据
- **数据库数据**：从MySQL等数据库中获取已标注的图像数据
- **本地数据**：处理本地存储的各种格式数据集

## 项目结构
```
Yolo_Utils/
├── README.md                     # 项目说明文档
├── requirements.txt              # 项目依赖
├── config/                       # 配置文件目录
│   ├── config.yaml              # 主配置文件
│   ├── crawler_config.yaml      # 爬虫配置文件
│   ├── dataset_config.yaml      # 数据集配置文件
│   └── model_config.yaml        # 模型训练配置文件
├── src/                         # 源代码目录
│   ├── basic/                    # 核心基础模块
│   │   ├── __init__.py
│   │   ├── config_manager.py    # 配置管理器
│   │   ├── logger.py           # 日志管理
│   │   ├── exceptions.py       # 自定义异常
│   │   └── base_classes.py     # 基础类定义
│   ├── data_acquisition/        # 数据获取模块（优先级1）
│   │   ├── __init__.py
│   │   ├── crawler/            # 图片爬取工具
│   │   │   ├── __init__.py
│   │   │   ├── web_crawler.py  # 基于icrawler的爬虫
│   │   │   ├── batch_crawler.py # 批量爬取管理器
│   │   │   └── crawler_utils.py # 爬虫工具函数
│   │   └── downloader/         # 数据下载工具
│   │       ├── __init__.py
│   │       ├── kaggle_downloader.py
│   │       ├── huggingface_downloader.py
│   │       └── openimages_downloader.py
│   ├── data_processing/         # 数据处理模块（优先级5）
│   │   ├── __init__.py
│   │   ├── format_converters/   # 格式转换器
│   │   │   ├── __init__.py
│   │   │   ├── coco_to_yolo.py
│   │   │   ├── voc_to_yolo.py
│   │   │   ├── mask_to_bbox.py  # 从mask生成bbox
│   │   │   ├── backup_annotation_converter.py  # 备份标注转换器
│   │   │   └── custom_converter.py
│   │   ├── data_standardizer/   # 数据标准化工具
│   │   │   ├── __init__.py
│   │   │   ├── image_processor.py
│   │   │   ├── label_processor.py
│   │   │   └── quality_checker.py
│   │   └── annotation_tools/    # 标注辅助工具
│   │       ├── __init__.py
│   │       ├── annotation_generator.py
│   │       └── annotation_validator.py
│   ├── data_integration/        # 数据整合模块（优先级5）
│   │   ├── __init__.py
│   │   ├── dataset_merger.py    # 数据集合并器
│   │   ├── class_mapper.py      # 类别映射管理
│   │   ├── data_balancer.py     # 数据平衡工具
│   │   └── final_dataset_builder.py # 最终数据集构建器
│   ├── model_training/          # 模型训练模块（优先级4）
│   │   ├── __init__.py
│   │   ├── trainer.py          # 基于ultralytics的训练器主类
│   │   ├── training_strategies/ # 训练策略
│   │   │   ├── __init__.py
│   │   │   ├── ultralytics_trainer.py    # ultralytics训练策略
│   │   │   └── hyperparameter_tuner.py   # 超参数搜索器
│   │   └── training_utils/     # 训练工具
│   │       ├── __init__.py
│   │       ├── config_validator.py      # 配置验证器
│   │       └── model_manager.py         # 模型管理器
│   ├── model_evaluation/        # 模型测试模块（优先级3）
│   │   ├── __init__.py
│   │   ├── tester.py          # 基于ultralytics的测试器
│   │   ├── metrics/           # 评估指标
│   │   │   ├── __init__.py
│   │   │   └── ultralytics_metrics.py   # ultralytics指标包装
│   │   └── visualizer/        # 结果可视化
│   │       ├── __init__.py
│   │       ├── result_plotter.py
│   │       └── test_report_generator.py # 测试报告生成器
│   └── utils/                  # 通用工具模块
│       ├── __init__.py
│       ├── file_utils.py       # 文件操作工具
│       ├── image_utils.py      # 图像处理工具
│       ├── path_utils.py       # 路径管理工具
│       └── system_utils.py     # 系统工具
├── tools/                      # 执行工具目录
│   ├── __init__.py
│   ├── crawler_tool.py         # 爬虫工具入口
│   ├── dataset_processor.py    # 数据处理工具入口
│   ├── dataset_merger.py       # 数据整合工具入口
│   ├── trainer_tool.py         # 模型训练工具入口
│   ├── tester_tool.py          # 模型测试工具入口
│   ├── config_advisor_tool.py  # 模型训练智能配置建议工具
│   ├── image_backup_tool.py    # 数据库图片备份工具
│   ├── backup_annotation_tool.py  # 备份标注转换工具
│   ├── hyperparameter_search_data_extractor.py  # 超参数搜索数据抽取工具
│   └── pipeline_runner.py      # 完整流水线执行器
├── data/                       # 数据目录
│   ├── raw/                   # 原始数据
│   ├── processed/             # 处理后数据
│   ├── integrated/            # 整合后数据
│   ├── final/                 # 最终训练数据
│   └── search/                # 超参数搜索用数据子集
├── models/                     # 模型文件目录
├── runs/                       # 训练运行结果
├── logs/                       # 日志文件
└── tests/                      # 测试代码
    ├── __init__.py
    ├── test_crawler.py
    ├── test_processor.py
    ├── test_integration.py
    └── test_training.py
```

## 核心模块详解

### 1. 配置文件系统

项目采用分层配置文件架构，确保各模块配置的独立性和可维护性：

#### config.yaml（主配置文件）
- **项目全局设置**：日志级别、工作目录、GPU设置等
- **模块开关控制**：控制各功能模块的启用状态
- **公共参数配置**：跨模块共享的参数设置
- **模块间协调配置**：模块间数据传递和协作配置

#### dataset_config.yaml（数据集配置文件）
- **数据源配置**：Kaggle、HuggingFace、OpenImages等数据源配置
- **数据集路径管理**：各类数据集的本地存储路径配置 
- **类别映射关系**：不同数据集间的类别对应关系
- **数据预处理参数**：图像尺寸、质量要求等预处理参数

#### model_config.yaml（模型训练配置文件）
- **训练模式配置**：固定参数训练和超参数搜索两种模式
- **ultralytics参数**：直接适配ultralytics YOLO API的参数格式
- **模型配置**：模型架构、预训练权重、输入尺寸等
- **测试配置**：模型测试和评估相关参数

#### crawler_config.yaml（爬虫配置文件）
- **搜索关键词配置**：爬取目标的关键词列表
- **爬取参数设置**：爬取数量、过滤条件、下载间隔等
- **过滤条件配置**：图像尺寸、格式、质量过滤条件
- **存储路径设置**：爬取结果的本地存储路径

### 2. 基础支撑模块（src/basic/）

#### base_classes.py - 基础类定义
提供项目中所有模块继承的基础抽象类：

**BaseProcessor类**：
- `process(input_data)` - 数据处理抽象方法
- `validate_input(input_data)` - 输入数据验证方法

**BaseConverter类**：
- `convert(source_path, target_path)` - 格式转换抽象方法
- `get_supported_formats()` - 获取支持的格式列表

**BaseTrainer类**：
- `train(config)` - 训练执行抽象方法
- `validate(model_path)` - 模型验证抽象方法

**BaseEvaluator类**：
- `evaluate(model_path, dataset_path)` - 评估执行抽象方法

**BaseDatasetHandler类**：
- `load_dataset(path)` - 数据集加载抽象方法
- `save_dataset(dataset, path)` - 数据集保存抽象方法

#### config_manager.py - 配置管理器
- 统一配置文件加载与解析
- 配置参数验证与默认值处理
- 运行时配置动态更新

#### logger.py - 日志管理
- 分级日志记录（DEBUG、INFO、WARNING、ERROR）
- 多输出目标支持（控制台、文件、远程）
- 模块化日志配置

#### exceptions.py - 自定义异常
- 数据处理异常类
- 训练过程异常类
- 配置错误异常类

### 3. 数据处理模块（src/data_processing/）

#### format_converters/ - 格式转换器

**COCOToYOLOConverter类**：
- `convert(coco_json_path, output_dir)` - 执行COCO到YOLO转换
- `_parse_coco_annotations(coco_data)` - 解析COCO标注数据
- `_convert_bbox_format(bbox, img_width, img_height)` - 转换边界框格式
- `_generate_yolo_labels(annotations, output_dir)` - 生成YOLO标签文件

**VOCToYOLOConverter类**：
- `convert(voc_dir, output_dir)` - 执行VOC到YOLO转换
- `_parse_xml_annotation(xml_path)` - 解析XML标注文件
- `_extract_objects(xml_root)` - 提取目标对象信息
- `_convert_coordinates(obj_info, img_size)` - 转换坐标格式

**MaskToBBoxConverter类**：
- `convert(mask_dir, output_dir)` - 执行掩码到边界框转换
- `_process_mask_image(mask_path)` - 处理单个掩码图像
- `_extract_contours(mask)` - 提取轮廓信息
- `_contour_to_bbox(contour)` - 轮廓转边界框
- `_filter_small_objects(bboxes)` - 过滤小目标

**CustomFormatConverter类**：
- `convert(input_path, output_path)` - 执行自定义格式转换
- `register_conversion_rule(source_format, target_format, rule_func)` - 注册转换规则
- `_apply_conversion_rule(data, rule_name)` - 应用转换规则

#### data_standardizer/ - 数据标准化工具

**ImageProcessor类**：
- `process(image_path)` - 处理单张图像
- `batch_process(image_dir, output_dir)` - 批量处理图像
- `_resize_image(image)` - 调整图像尺寸
- `_enhance_image_quality(image)` - 增强图像质量
- `_validate_image_quality(image)` - 验证图像质量
- `_remove_corrupted_images(image_paths)` - 移除损坏图像

**LabelProcessor类**：
- `process(label_path)` - 处理单个标签文件
- `batch_process(label_dir, output_dir)` - 批量处理标签文件
- `_standardize_class_names(labels)` - 标准化类别名称
- `_validate_bbox_coordinates(bbox)` - 验证边界框坐标
- `_filter_invalid_labels(labels)` - 过滤无效标签
- `_normalize_coordinates(labels, img_size)` - 归一化坐标

**QualityChecker类**：
- `check_image_quality(image_path)` - 检查图像质量
- `check_label_quality(label_path, image_path)` - 检查标签质量
- `generate_quality_report(dataset_dir)` - 生成质量报告
- `_check_image_corruption(image_path)` - 检查图像是否损坏
- `_check_label_consistency(label_data, image_size)` - 检查标签一致性
- `_calculate_dataset_statistics(dataset_dir)` - 计算数据集统计信息

#### annotation_tools/ - 标注辅助工具

**AnnotationGenerator类**：
- `generate_pseudo_labels(image_dir, output_dir)` - 生成伪标签
- `auto_annotate_batch(image_paths, confidence_threshold)` - 批量自动标注
- `_load_pretrained_model()` - 加载预训练模型
- `_predict_single_image(image_path)` - 预测单张图像
- `_filter_low_confidence_predictions(predictions, threshold)` - 过滤低置信度预测
- `_save_yolo_annotations(annotations, output_path)` - 保存YOLO格式标注

**AnnotationValidator类**：
- `validate_annotations(annotation_dir, image_dir)` - 验证标注文件
- `fix_common_errors(annotation_dir)` - 修复常见错误
- `_check_file_correspondence(annotation_dir, image_dir)` - 检查文件对应关系
- `_validate_yolo_format(annotation_path)` - 验证YOLO格式
- `_check_coordinate_bounds(annotations)` - 检查坐标边界
- `_detect_duplicate_annotations(annotations)` - 检测重复标注

### 4. 数据整合模块（src/data_integration/）

#### DatasetMerger类
**核心功能**：将多个不同来源的数据集合并为统一的训练数据集

**主要方法**：
- `merge_datasets(dataset_configs, output_dir)` - 合并多个数据集的主方法
- `_prepare_merge_plan(dataset_configs)` - 分析数据集并制定合并计划
- `_merge_images(datasets, output_dir)` - 合并所有图像文件到目标目录
- `_merge_labels(datasets, output_dir)` - 合并所有标签文件并处理类别映射
- `_handle_class_conflicts(datasets)` - 处理不同数据集间的类别冲突
- `_generate_merged_dataset_yaml(merge_info, output_dir)` - 生成合并后的数据集配置文件
- `_validate_merge_result(output_dir)` - 验证合并结果的完整性和正确性

#### ClassMapper类
**核心功能**：管理多数据集间的类别映射关系，解决类别命名不一致问题

**主要方法**：
- `build_unified_class_mapping(datasets)` - 分析所有数据集构建统一类别映射
- `map_class_labels(labels, source_dataset)` - 将标签从源数据集类别映射到统一类别
- `_analyze_class_overlap(datasets)` - 分析不同数据集间的类别重叠情况
- `_resolve_class_conflicts(conflicts)` - 自动或手动解决类别冲突
- `_create_mapping_rules(class_analysis)` - 基于分析结果创建映射规则
- `get_final_class_list()` - 获取最终统一的类别列表
- `save_mapping_config(output_path)` - 保存映射配置到文件
- `load_mapping_config(config_path)` - 从文件加载映射配置

#### DataBalancer类
**核心功能**：处理数据集中的类别不平衡问题，提高训练效果

**主要方法**：
- `balance_dataset(dataset_dir, output_dir)` - 执行数据集平衡处理
- `_analyze_class_distribution(dataset_dir)` - 分析当前数据集的类别分布
- `_oversample_minority_classes(class_stats, dataset_dir, output_dir)` - 对少数类进行过采样
- `_undersample_majority_classes(class_stats, dataset_dir, output_dir)` - 对多数类进行欠采样
- `_apply_data_augmentation(image_paths, target_count)` - 通过数据增强增加样本数量
- `_generate_balance_report(before_stats, after_stats)` - 生成平衡前后的对比报告

#### FinalDatasetBuilder类
**核心功能**：构建最终可用于YOLO训练的标准数据集

**主要方法**：
- `build_final_dataset(merged_dataset_dir, output_dir)` - 构建最终数据集的主方法
- `_split_dataset(dataset_info)` - 将数据集分割为训练/验证/测试集
- `_stratified_split(samples, ratios)` - 进行分层采样确保各集合类别分布一致
- `_copy_files_to_splits(split_info, output_dir)` - 将文件复制到对应的分割目录
- `_generate_dataset_yaml(class_info, output_dir)` - 生成YOLO训练需要的dataset.yaml文件
- `_validate_final_dataset(output_dir)` - 验证最终数据集的结构和内容
- `_generate_dataset_report(dataset_dir)` - 生成详细的数据集报告

### 5. 模型训练模块（src/model_training/）

#### trainer.py - 基于ultralytics的训练器主类
**核心功能**：封装ultralytics YOLO的训练功能，提供统一的训练接口

**主要方法**：
- `train_with_fixed_params(config)` - 使用固定超参数进行训练
- `train_with_hyperparameter_search(config)` - 使用超参数搜索进行训练  
- `_load_config(config_path)` - 加载训练配置
- `_validate_dataset(dataset_path)` - 验证数据集有效性
- `_setup_training_environment()` - 设置训练环境
- `_save_training_results(results, output_dir)` - 保存训练结果

#### training_strategies/ultralytics_trainer.py - ultralytics训练策略
**核心功能**：实现基于ultralytics框架的具体训练策略

**主要方法**：
- `execute_training(model_config, dataset_config)` - 执行训练的主方法
- `_prepare_model(model_name, pretrained_weights)` - 准备训练模型
- `_configure_training_params(config)` - 配置训练参数
- `_monitor_training_progress()` - 监控训练进度
- `_handle_training_errors(error)` - 处理训练错误

#### training_strategies/hyperparameter_tuner.py - 超参数搜索器
**核心功能**：使用ultralytics的tune功能进行超参数优化

**主要方法**：
- `tune_hyperparameters(base_config, tune_config)` - 执行超参数搜索
- `_define_search_space(tune_config)` - 定义搜索空间
- `_execute_tuning_process(model, tune_params)` - 执行调优过程
- `_analyze_tuning_results(results)` - 分析调优结果
- `_select_best_config(results)` - 选择最佳配置

### 6. 模型测试模块（src/model_evaluation/）

#### tester.py - 基于ultralytics的测试器
**核心功能**：使用训练好的模型在测试集上进行评估

**主要方法**：
- `test_model(model_path, test_config)` - 测试模型的主方法
- `_load_trained_model(model_path)` - 加载训练好的模型
- `_run_evaluation(model, dataset_path)` - 运行评估过程
- `_generate_test_report(results, output_dir)` - 生成测试报告
- `_visualize_results(results, output_dir)` - 可视化测试结果

#### metrics/ultralytics_metrics.py - ultralytics指标包装
**核心功能**：封装ultralytics的评估指标，提供统一的指标接口

**主要方法**：
- `calculate_metrics(predictions, ground_truth)` - 计算评估指标
- `get_detailed_metrics()` - 获取详细指标
- `export_metrics_report(output_path)` - 导出指标报告

### 7. 工具入口模块（tools/）

#### trainer_tool.py - 模型训练工具入口
**核心功能**：提供模型训练的IDE运行入口

**主要功能**：
- `train_with_fixed_params()` - 使用固定超参数训练（用于测试代码可行性）
- `train_with_hyperparameter_search()` - 使用超参数搜索训练（用于最终模型）
- 支持IDE直接运行，无需命令行参数

#### tester_tool.py - 模型测试工具入口  
**核心功能**：提供模型测试的IDE运行入口

**主要功能**：
- `test_latest_model()` - 测试最新训练的模型
- `test_specific_model(model_path)` - 测试指定路径的模型
- 自动生成详细的测试报告和可视化结果

#### config_advisor_tool.py - 智能配置建议工具
**核心功能**：根据数据集统计信息和硬件资源自动推荐最优配置

**主要功能**：
- `analyze_system_and_recommend()` - 分析系统并生成配置建议
- `update_config_with_recommendations()` - 根据建议自动更新配置文件
- 智能检测GPU内存、CPU核心数、系统内存等硬件资源
- 根据数据集规模推荐epochs、学习率等参数
- 生成详细的配置建议报告

#### hyperparameter_search_data_extractor.py - 超参数搜索数据抽取工具
**核心功能**：从完整数据集中抽取部分数据用于超参数搜索，避免数据集过大影响搜索效果

**主要功能**：
- `extract_data_for_search()` - 执行数据抽取的主方法
- 从每个类别的数据中按比例抽取指定数量的样本
- 支持配置抽取比例（默认20%）和分割比例（默认train:val=8:2）
- 保持类别分布的均衡性，避免某些类别样本过少
- 自动生成适用于超参数搜索的dataset.yaml文件
- 生成详细的抽取统计报告和数据分析

## 使用说明

### 模型训练

项目提供两种训练模式：

1. **固定参数训练**（用于测试代码可行性）：
   ```python
   # 运行 tools/trainer_tool.py
   # 选择 train_with_fixed_params() 函数
   ```

2. **超参数搜索训练**（用于最终模型）：
   ```python
   # 运行 tools/trainer_tool.py  
   # 选择 train_with_hyperparameter_search() 函数
   ```

### 模型测试

测试训练好的模型效果：
```python
# 运行 tools/tester_tool.py
# 自动使用最新或最佳模型在测试集上评估
```

### 数据集要求

- 使用`data/final/`目录下的数据
- 支持的类别：boat, person, excavator, algae, hpipe, aeration
- 自动适配实际存在的类别（数据集可能不包含所有类别）

### 智能配置建议

在开始训练前，建议使用智能配置工具优化参数：
```python
# 运行 tools/config_advisor_tool.py
# 分析系统硬件和数据集，生成配置建议
analyze_system_and_recommend()

# 自动更新配置文件
update_config_with_recommendations()
```

### 超参数搜索数据准备

在进行超参数搜索前，建议先抽取部分数据以提高搜索效率：
```python
# 运行 tools/hyperparameter_search_data_extractor.py
# 从完整数据集中抽取20%的数据用于超参数搜索
extract_data_for_search()
```

抽取的数据将保存在`data/search/`目录中，包含：
- 按类别均衡抽取的样本数据
- 重新分割的train/val数据（默认8:2比例）
- 适用于超参数搜索的dataset.yaml文件
- 详细的抽取统计报告

### 配置文件

- `config/model_config.yaml`：训练和测试相关配置
- `data/final/dataset.yaml`：数据集类别和路径配置

## 项目特点

1. **基于ultralytics框架**：充分利用成熟的YOLO训练框架
2. **多源数据整合**：支持多种数据源的统一处理
3. **智能类别映射**：自动处理不同数据集间的类别差异
4. **灵活的训练策略**：支持固定参数和超参数搜索两种模式
5. **完整的评估体系**：提供详细的模型测试和可视化功能
6. **IDE友好**：所有工具支持IDE直接运行，无需命令行操作

class_mapping:
  aeration: 5
  algae: 3
  boat: 0
  excavator: 2
  hpipe: 4
  person: 1
execution_results:
  final_statistics:
    class_mapping:
      aeration: 5
      algae: 3
      boat: 0
      excavator: 2
      hpipe: 4
      person: 1
    class_statistics:
      '0':
        test: 5012
        train: 30866
        val: 9129
      '1':
        test: 10129
        train: 76301
        val: 22757
      '2':
        test: 999
        train: 6951
        val: 2016
      '3':
        test: 186
        train: 1392
        val: 387
      '4':
        test: 64
        train: 426
        val: 125
    created_time: '2025-07-16T17:30:37.732577'
    dataset_info:
      authors:
      - Max
      description: 多源数据集整合的目标检测数据集
      license: MIT
      name: Custom Detection Dataset
      version: 1.0.0
    dataset_sources:
      MASATI-V2:
        class_statistics:
          boat:
            samples:
              test: 460
              train: 2879
              val: 715
            target_id: 0
            total_samples: 4054
        format: yolo
        mapped_classes:
          '0':
            source_name: boat
            target_id: 0
            target_name: boat
        path: data/processed/kaggle/MASATI-V2
        processed_time: '2025-07-09T14:30:17.939966'
        selected_classes:
        - boat
      Ship-Dataset:
        class_statistics:
          boat:
            samples:
              test: 240
              train: 1180
              val: 531
            target_id: 0
            total_samples: 1951
        format: yolo
        mapped_classes:
          '0':
            source_name: boat
            target_id: 0
            target_name: boat
        path: data/processed/kaggle/Ship-Dataset
        processed_time: '2025-07-09T14:31:45.320605'
        selected_classes:
        - boat
      Ships-in-aerial-images:
        class_statistics:
          boat:
            samples:
              test: 2998
              train: 17708
              val: 5342
            target_id: 0
            total_samples: 26048
        format: yolo
        mapped_classes:
          '0':
            source_name: boat
            target_id: 0
            target_name: boat
        path: data/processed/kaggle/Ships-in-aerial-images
        processed_time: '2025-07-09T14:31:45.325954'
        selected_classes:
        - boat
      VisDrone2019:
        class_statistics:
          person:
            samples:
              test: 10129
              train: 76301
              val: 22757
            target_id: 1
            total_samples: 109187
        format: yolo
        mapped_classes:
          0:
            source_name: person
            target_id: 1
            target_name: person
        path: data/processed/VisDrone2019
        processed_time: '2025-07-16T17:30:37.723676'
        selected_classes:
        - person
      backup_annotation:
        class_statistics:
          boat:
            samples:
              test: 1314
              train: 9099
              val: 2541
            target_id: 0
            total_samples: 12954
          excavator:
            samples:
              test: 159
              train: 1177
              val: 347
            target_id: 2
            total_samples: 1683
          hpipe:
            samples:
              test: 64
              train: 426
              val: 125
            target_id: 4
            total_samples: 615
        format: yolo
        mapped_classes:
          '0':
            source_name: 施工
            target_id: 2
            target_name: excavator
          '1':
            source_name: 管路异常
            target_id: 4
            target_name: hpipe
          '2':
            source_name: 船只
            target_id: 0
            target_name: boat
        path: data/processed/backup_annotation
        processed_time: '2025-07-09T14:40:16.786218'
        selected_classes:
        - 施工
        - 管路异常
        - 船只
      bluegreen-algae-dataset:
        class_statistics:
          algae:
            samples:
              test: 186
              train: 1392
              val: 387
            target_id: 3
            total_samples: 1965
        format: yolo
        mapped_classes:
          '0':
            source_name: Algae
            target_id: 3
            target_name: algae
        path: data/processed/kaggle/bluegreen-algae-dataset
        processed_time: '2025-07-09T14:29:21.356355'
        selected_classes:
        - Algae
        - BlueGreen_Algae
      construction-equipment:
        class_statistics:
          excavator:
            samples:
              test: 840
              train: 5774
              val: 1669
            target_id: 2
            total_samples: 8283
        format: yolo
        mapped_classes:
          '1':
            source_name: Excavator
            target_id: 2
            target_name: excavator
        path: data/processed/kaggle/construction-equipment
        processed_time: '2025-07-09T14:29:21.355884'
        selected_classes:
        - Excavator
    output_directory: data/final
    split_statistics:
      test: 4409
      train: 30834
      val: 8808
    total_samples: 44051
  merge_statistics:
    class_sample_tracking_summary:
      total_classes_tracked: 1
      total_samples_tracked: 109187
    dataset_class_stats_summary:
      datasets_with_stats:
      - VisDrone2019
      total_datasets_processed: 1
    empty_label_stats:
      keep_ratio: 0.1
      kept_empty: 154
      removed_empty: 1392
      total_empty: 1546
    file_mapping_count: 14474
    global_classes:
      aeration: 5
      algae: 3
      boat: 0
      excavator: 2
      hpipe: 4
      person: 1
    label_processing_stats:
      deleted: 0
      modified: 109187
    output_directory: data/integrated
    total_images: 7237
    total_labels: 7237
pipeline_config:
  class_mapping:
    auto_mapping:
      enabled: true
      manual_review_required: true
      similarity_threshold: 0.8
    global_classes:
    - boat
    - person
    - excavator
    - algae
    - hpipe
    - aeration
    mapping_rules:
      Algae: algae
      BlueGreen_Algae: algae
      Excavator: excavator
      aeration: aeration
      boat: boat
      hpipe: hpipe
      person: person
      ship: boat
      施工: excavator
      管路异常: hpipe
      船只: boat
    validation:
      allow_unknown_classes: false
      log_unmapped_classes: true
      strict_mode: true
  cleanup:
    backup_before_cleanup: false
    backup_dir: data/backup/cleanup
    cleanup_intermediate: true
    enabled: true
  data_balancing:
    custom_target_count: 5000
    enabled: false
    oversample:
      max_duplicates: 1
      method: duplicate
    strategy: mixed
    target_type: median
    undersample:
      method: random
      preserve_important: true
  data_split:
    split_ratios:
      test: 0.1
      train: 0.7
      val: 0.2
  dataset_merger:
    backup:
      backup_dir: data/backup/integration
      enabled: true
    conflict_resolution:
      class_conflicts: strict
      duplicate_files: rename
    empty_label_keep_ratio: 0.1
    file_naming:
      include_original_name: true
      prefix_separator: _
      strategy: dataset_prefix
    output_dir: data/integrated
    progress_bar: true
    verbose: true
  datasets:
  - enabled: true
    format: yolo
    name: construction-equipment
    path: data/processed/kaggle/construction-equipment
    selected_classes:
    - Excavator
  - enabled: true
    format: yolo
    name: bluegreen-algae-dataset
    path: data/processed/kaggle/bluegreen-algae-dataset
    selected_classes:
    - Algae
    - BlueGreen_Algae
  - enabled: true
    format: yolo
    name: MASATI-V2
    path: data/processed/kaggle/MASATI-V2
    selected_classes:
    - boat
  - enabled: true
    format: yolo
    name: Ship-Dataset
    path: data/processed/kaggle/Ship-Dataset
    selected_classes:
    - boat
  - enabled: true
    format: yolo
    name: Ships-in-aerial-images
    path: data/processed/kaggle/Ships-in-aerial-images
    selected_classes:
    - boat
  - enabled: true
    format: yolo
    name: backup_annotation
    path: data/processed/backup_annotation
    selected_classes:
    - 施工
    - 管路异常
    - 船只
  - enabled: true
    format: yolo
    name: VisDrone2019
    path: data/processed/VisDrone2019
    selected_classes:
    - person
  final_dataset:
    dataset_info:
      authors:
      - Max
      description: 多源数据集整合的目标检测数据集
      license: MIT
      name: Custom Detection Dataset
      version: 1.0.0
    generate_statistics: true
    output_dir: data/final
    statistics_format:
    - json
    - txt
    validation:
      check_file_integrity: true
      enabled: true
      generate_sample_images: true
      sample_count: 10
      verify_class_distribution: true
    yolo_config:
      generate_yaml: true
      include_class_colors: false
      yaml_filename: dataset.yaml
summary:
  balance_applied: false
  final_dataset_created: true
  merge_success: true
  total_global_classes: 6
updated_time: '2025-07-16T17:30:40.341421'

# 多用户并发标注功能

## 🎯 功能概述

本文档介绍图像标注工具的多用户并发标注功能，该功能允许最多10个用户同时进行图像标注工作，通过智能分片机制避免重复标注，提高标注效率。

## ✨ 核心特性

### 🔄 智能分片机制
- **小粒度分片**: 每个分片包含200张图片（10页 × 20张/页）
- **动态分配**: 用户完成当前分片后自动分配下一个可用分片
- **负载均衡**: 避免用户空闲等待，确保工作连续性
- **防重复**: 确保每张图片只被一个用户处理

### 👥 用户管理
- **IP识别**: 基于客户端IP地址自动识别用户
- **会话管理**: 自动维护用户会话状态和活跃时间
- **权限控制**: 管理员权限基于IP地址（可配置，默认*************）
- **心跳机制**: 定期检测用户在线状态

### 🛡️ 并发控制
- **文件锁**: 使用文件锁防止CSV数据并发写入冲突
- **冲突检测**: 检测并防止多用户同时操作同一图片
- **操作日志**: 记录所有标注操作，便于审计和统计
- **原子操作**: 确保数据操作的原子性和一致性

### 📊 监控管理
- **实时监控**: 管理员可实时查看所有用户状态
- **进度统计**: 显示每个用户的标注进度和统计信息
- **分片管理**: 管理员可强制释放用户分片
- **操作统计**: 提供详细的操作统计和分析

## 🚀 快速开始

### 1. 启动多用户系统

```bash
# 使用专用启动脚本
python start_multiuser_demo.py

# 或使用标准启动脚本
python tools/start_image_manager.py --host 0.0.0.0 --port 8080

# 或使用远程访问脚本
python start_remote_access.py
```

### 2. 访问系统

- **本机访问**: http://127.0.0.1:8080
- **局域网访问**: http://[服务器IP]:8080
- **管理员页面**: http://[服务器IP]:8080/admin

### 3. 用户操作流程

1. **自动分配**: 用户首次访问时自动分配200张图片的分片
2. **默认筛选**: 页面默认显示"未标记"的图片
3. **自动全选**: 进入页面后自动选中当前页面所有图片
4. **批量标注**: 点击"标记有效"完成当前页面标注
5. **自动分页**: 完成后自动跳转到下一页继续标注
6. **分片完成**: 完成200张图片后自动分配下一个分片

## 📋 系统架构

### 核心模块

#### 1. UserManager (用户管理器)
```python
# 主要功能
- get_user_id(): 获取用户IP标识
- get_user_chunk_range(): 获取用户分片范围
- complete_current_chunk(): 完成当前分片，分配下一个
- update_user_activity(): 更新用户活跃状态
- get_all_users_status(): 获取所有用户状态（管理员）
```

#### 2. ConcurrentManager (并发控制器)
```python
# 主要功能
- file_lock(): 文件锁上下文管理器
- check_image_conflicts(): 检查图片操作冲突
- log_annotation_operation(): 记录标注操作
- safe_csv_operation(): 安全的CSV操作
```

#### 3. 数据分片算法
```python
# 分片计算
chunk_id = user_index  # 用户分配的分片ID
start_index = chunk_id * CHUNK_SIZE  # 分片起始位置
end_index = min(start_index + CHUNK_SIZE, total_images)  # 分片结束位置
```

### API接口扩展

#### 用户管理API
- `GET /api/user/info` - 获取当前用户信息
- `POST /api/user/heartbeat` - 用户心跳维持会话
- `POST /api/user/complete_chunk` - 完成当前分片

#### 管理员API
- `GET /api/admin/users` - 获取所有用户状态
- `POST /api/admin/force_release` - 强制释放用户分片

#### 修改的现有API
- `GET /api/images` - 添加用户分片过滤
- `POST /api/images/mark_valid` - 添加并发控制
- `POST /api/images/soft_delete` - 添加冲突检测

## 🔧 配置说明

### 多用户配置参数
```python
# src/image_manager/config.py
CHUNK_SIZE = 200  # 每个分片的图片数量
SESSION_TIMEOUT = 300  # 用户会话超时时间（秒）
ADMIN_IP = os.environ.get('ADMIN_IP', '*************')  # 管理员IP地址（可配置）
MAX_CONCURRENT_USERS = 10  # 最大并发用户数
HOST = '0.0.0.0'  # 支持局域网访问
```

### 数据文件扩展
```csv
# query_results.csv 新增字段
assigned_user_ip,annotation_user_ip,annotation_timestamp,user_session_id
```

## 📊 监控和统计

### 用户状态监控
- **在线状态**: 实时显示用户在线/离线状态
- **分片分配**: 显示每个用户当前分配的分片
- **标注进度**: 统计每个用户已标注的图片数量
- **活跃时间**: 记录用户上线时间和最后活跃时间

### 操作统计
- **总操作数**: 统计所有用户的操作次数
- **标注效率**: 计算每个用户的标注速度
- **操作类型**: 统计不同操作类型的分布
- **时间分析**: 按时间段分析标注活动

## 🛠️ 故障排除

### 常见问题

#### 1. 用户无法获取分片
**原因**: 所有分片已被分配或用户会话过期
**解决**: 管理员可强制释放空闲用户的分片

#### 2. 操作冲突错误
**原因**: 多个用户同时操作同一图片
**解决**: 系统自动检测并阻止冲突操作

#### 3. 分片分配不均
**原因**: 用户加入时间不同导致分配不均
**解决**: 系统会在后续分配中自动平衡

#### 4. 管理员页面无法访问
**原因**: 当前IP不是管理员IP
**解决**: 确认访问IP为配置的管理员IP地址

### 日志文件
- `logs/image_manager.log` - 系统运行日志
- `data/raw/backup/logs/annotation_log.csv` - 标注操作日志
- `data/raw/backup/logs/user_sessions.csv` - 用户会话日志

## 🔒 安全考虑

### 访问控制
- 基于IP的简单权限控制
- 无需用户名密码认证
- 适用于内部局域网环境

### 数据保护
- 文件锁防止数据损坏
- 操作日志记录所有变更
- 自动备份关键数据文件

### 并发安全
- 原子操作确保数据一致性
- 冲突检测防止重复处理
- 会话超时自动清理资源

## 📈 性能优化

### 分片策略
- 小粒度分片减少用户等待时间
- 动态分配提高资源利用率
- 负载均衡避免热点问题

### 缓存机制
- 用户会话信息内存缓存
- 分片分配状态缓存
- 定期同步到持久化存储

### 网络优化
- 心跳机制减少网络开销
- 批量操作减少请求次数
- 压缩传输减少带宽占用

## 🧪 测试验证

### 功能测试
```bash
# 运行多用户功能测试
python tests/test_multiuser_functionality.py
```

### 压力测试
- 模拟多用户并发访问
- 测试分片分配性能
- 验证并发控制效果

### 集成测试
- 端到端用户流程测试
- API接口兼容性测试
- 数据一致性验证

## 📝 更新日志

### v2.0.0 - 多用户并发支持
- ✅ 实现200张图片小粒度分片机制
- ✅ 添加基于IP的用户识别和管理
- ✅ 实现并发控制和冲突检测
- ✅ 添加管理员监控功能
- ✅ 优化用户界面和交互体验
- ✅ 支持局域网多设备访问

### 后续计划
- 🔄 添加用户标注质量评估
- 🔄 实现更智能的负载均衡算法
- 🔄 添加实时协作状态显示
- 🔄 支持标注任务的优先级设置

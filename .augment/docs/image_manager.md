# 图片质量管理系统

## 🎯 概述

图片质量管理系统是一个基于Flask的Web应用，专门用于检查和管理标注图片的质量。系统提供了直观的Web界面，支持图片质量检查、批量标记、软删除和回收站管理等功能。

## ✨ 功能特性

### 🖼️ 原始图片管理
- **网格布局展示**: 每行5张图片的响应式网格布局
- **分页浏览**: 支持20/30/50张图片每页的分页选项
- **图片预览**: 点击图片查看完整尺寸预览
- **批量操作**: 支持批量选择、标记和删除图片
- **状态筛选**: 基于有效性状态筛选图片（全部/有效/未标记）
- **质量标记**: 将图片标记为"有效"状态

### 🗑️ 回收站管理
- **软删除机制**: 将不合格图片移动到回收站，避免误删
- **永久删除**: 从回收站永久删除图片文件和相关记录
- **安全确认**: 永久删除操作需要用户确认

### 📊 数据管理
- **CSV状态管理**: 基于`query_results.csv`文件管理图片状态
- **实时统计**: 显示图片数量统计信息
- **防重复机制**: 防止已删除图片被重复拷贝

### 🔧 技术特性
- **图片质量检查**: 支持对原始图片进行质量评估和标记
- **状态管理**: 基于CSV文件管理图片的有效性状态
- **缩略图生成**: 自动生成和缓存图片缩略图，提升加载性能
- **响应式设计**: 支持不同屏幕尺寸的设备访问

## 🚀 快速开始

### 1. 环境检查
```bash
python tools/start_image_manager.py --check-only
```

### 2. 启动服务
```bash
# 基本启动
python tools/start_image_manager.py

# 自定义配置启动
python tools/start_image_manager.py --host 0.0.0.0 --port 8080 --debug

# 演示启动（会自动打开浏览器）
python demo_start.py

# 远程访问启动（局域网环境）
python start_remote_access.py
```

### 3. 访问系统
打开浏览器访问: http://127.0.0.1:5000

## 📁 系统架构

### 目录结构
```
src/image_manager/           # 图片管理模块
├── __init__.py             # 模块初始化
├── config.py               # 配置管理
├── data_manager.py         # CSV数据管理
├── image_manager.py        # 图片文件管理
├── app.py                  # Flask应用核心
└── templates/              # HTML模板
    ├── base.html           # 基础模板
    ├── index.html          # 首页
    ├── images.html         # 原始图片页面
    ├── trash.html          # 回收站页面
    └── error.html          # 错误页面

tools/
└── start_image_manager.py  # 启动脚本

tests/
└── test_image_manager.py   # 单元测试

.augment/docs/
└── image_manager.md        # 详细文档
```

### 核心模块

#### 1. 配置管理 (config.py)
- 定义系统配置参数和常量
- 管理目录路径和文件路径
- 提供配置验证功能

#### 2. 数据管理 (data_manager.py)
- 管理`query_results.csv`文件的读写操作
- 提供图片状态更新和查询功能
- 支持数据统计和备份功能

#### 3. 图片管理 (image_manager.py)
- 处理图片文件的读取和操作
- 生成和缓存图片缩略图
- 实现软删除和永久删除功能

#### 4. Web应用 (app.py)
- Flask应用的创建和配置
- 定义路由和API接口
- 处理HTTP请求和响应

## 数据管理

### CSV文件结构
系统使用`data/raw/backup/query_results.csv`文件管理图片记录，包含以下字段：

- `initfile_name`: 原始图片文件名
- `intefile_name`: 标注图片文件名
- `business_type`: 业务类型
- `event_content_pro`: 事件内容
- `valid`: 图片有效性状态（新增字段）

### 状态定义
- `valid`: 图片质量合格
- `invalid`: 图片已被删除
- 空值: 未进行质量检查

### 目录结构
- `data/raw/backup/initfile/`: 原始图片目录
- `data/raw/backup/intefile/`: 标注图片目录
- `data/raw/backup/trash/`: 回收站目录
- `data/raw/backup/thumbnails/`: 缩略图缓存目录

## 安装和部署

### 环境要求
- Python 3.13+
- Flask 2.0+
- pandas 2.3+
- Pillow 11.3+
- Bootstrap 5.1+ (CDN)

### 安装步骤

1. **检查环境**
   ```bash
   python tools/start_image_manager.py --check-only
   ```

2. **启动服务**
   ```bash
   python tools/start_image_manager.py
   ```

3. **自定义配置**
   ```bash
   python tools/start_image_manager.py --host 0.0.0.0 --port 8080 --debug
   ```

### 启动参数
- `--host`: 服务器主机地址（默认: 127.0.0.1）
- `--port`: 服务器端口（默认: 5000）
- `--debug`: 启用调试模式
- `--log-level`: 日志级别（DEBUG/INFO/WARNING/ERROR）
- `--check-only`: 仅检查环境，不启动服务

## 🎮 使用指南

### 原始图片管理页面

1. **查看图片**: 系统以网格布局展示原始图片
2. **筛选图片**: 使用顶部的状态筛选器
   - **全部**: 显示所有图片
   - **有效**: 显示已标记为有效的图片
   - **未标记**: 显示尚未进行质量检查的图片

3. **选择图片**:
   - 单击复选框选择单张图片
   - 点击"全选"选择当前页面所有图片
   - 点击"清除选择"取消所有选择

4. **批量操作**:
   - **标记有效**: 将选中图片标记为质量合格
   - **移到回收站**: 将选中图片进行软删除

5. **图片预览**: 点击图片查看完整尺寸预览
6. **分页浏览**: 使用页面底部的分页控件

### 回收站管理页面

1. **查看回收站**: 显示已被软删除的图片
2. **永久删除**: 选择图片后点击"永久删除"
3. **安全确认**: 系统会显示警告对话框，确保操作安全

### 🛡️ 安全机制

- **软删除机制**: 首次删除只移动到回收站，不立即删除文件
- **确认对话框**: 永久删除操作需要用户明确确认
- **操作日志**: 所有重要操作都记录到日志文件
- **数据备份**: 支持CSV文件的自动备份功能

## 📋 API接口

### 图片管理API
- `GET /api/images` - 获取图片列表
- `POST /api/images/mark_valid` - 标记图片有效
- `POST /api/images/soft_delete` - 软删除图片

### 回收站API
- `GET /api/trash` - 获取回收站列表
- `POST /api/trash/permanent_delete` - 永久删除图片

### 统计API
- `GET /api/statistics` - 获取统计信息

### 详细API文档

#### 获取图片列表
```
GET /api/images?status=&page=1&page_size=20
```

#### 标记图片有效
```
POST /api/images/mark_valid
Content-Type: application/json
{
  "initfile_names": ["image1.jpg", "image2.jpg"]
}
```

#### 软删除图片
```
POST /api/images/soft_delete
Content-Type: application/json
{
  "initfile_names": ["image1.jpg", "image2.jpg"]
}
```

#### 获取回收站列表
```
GET /api/trash?page=1&page_size=20
```

#### 永久删除图片
```
POST /api/trash/permanent_delete
Content-Type: application/json
{
  "initfile_names": ["image1.jpg", "image2.jpg"]
}
```

#### 获取统计信息
```
GET /api/statistics
```

## 🧪 测试

### 运行单元测试
```bash
python tests/test_image_manager.py
```

或使用pytest（如果已安装）：
```bash
python -m pytest tests/test_image_manager.py -v
```

### 测试覆盖范围
- ✅ 配置管理功能
- ✅ CSV数据管理功能
- ✅ 图片文件管理功能
- ✅ Flask应用路由
- ✅ API接口功能

## 🚨 故障排除

### 常见问题

1. **目录不存在错误**
   ```bash
   python tools/start_image_manager.py --check-only
   ```
   - 检查`data/raw/backup/`目录是否存在
   - 确保`initfile`和`intefile`子目录存在

2. **CSV文件错误**
   - 检查`query_results.csv`文件是否存在
   - 确保CSV文件格式正确

3. **图片加载失败**
   - 检查图片文件是否存在
   - 确保图片文件格式支持

4. **权限错误**
   - 确保应用有读写目录的权限
   - 检查日志文件的写入权限

### 日志文件
- 应用日志: `logs/image_manager.log`
- 启动日志: 控制台输出
- 系统会记录所有重要操作和错误信息

## 📈 性能优化

### 缩略图缓存
- **自动生成和缓存图片缩略图**
- **缓存文件存储在`data/raw/backup/thumbnails/`目录**
- **支持缓存失效和重新生成**

### 分页加载
- **避免一次加载过多图片**
- **可配置每页显示数量（20/30/50）**

### 响应式设计
- **适配不同屏幕尺寸**

### 异步处理
- **图片缩略图生成采用异步处理**
- **大批量操作支持后台处理**

## 扩展开发

### 添加新功能
1. 在相应模块中添加功能代码
2. 更新API接口和路由
3. 修改前端模板和JavaScript
4. 添加相应的单元测试

### 自定义配置
- 修改`config.py`中的配置参数
- 支持环境变量配置
- 可扩展配置文件支持

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📚 相关资源

### 启动脚本说明

#### 1. `tools/start_image_manager.py` - 主启动脚本
- **用途**: 生产环境和开发环境的主要启动脚本
- **特点**:
  - 支持完整的命令行参数配置
  - 包含环境检查和验证功能
  - 支持调试模式和日志级别设置
  - 适合服务器部署和开发调试
- **使用场景**: 日常开发、服务器部署、自定义配置

#### 2. `demo_start.py` - 演示启动脚本
- **用途**: 快速演示和体验系统功能
- **特点**:
  - 自动打开浏览器
  - 使用默认配置
  - 用户友好的启动体验
- **使用场景**: 首次体验、功能演示、快速测试

#### 3. `start_remote_access.py` - 远程访问启动脚本
- **用途**: 专门为局域网远程访问设计的便捷启动脚本
- **特点**:
  - 自动检测和显示服务器IP地址
  - 自动绑定到所有网络接口（0.0.0.0）
  - 显示远程访问地址
  - 适合多设备访问场景
- **使用场景**: 局域网内多设备访问、远程开发、团队协作

### 其他资源
- 项目源码: 位于 `src/image_manager/` 目录
- 单元测试: `tests/test_image_manager.py`

---

## 版本信息

- **当前版本**: 1.0.0
- **开发团队**: Yolo_Utils Team
- **最后更新**: 2025-01-18

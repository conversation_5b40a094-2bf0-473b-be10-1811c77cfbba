"""
测试工具入口

基于配置文件的YOLO模型测试工具，适合在IDE中运行。
"""

import sys
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.model_evaluation.tester import YOLOTester
from src.basic.logger import get_logger, suppress_pil_debug_logs

# 抑制PIL等第三方库的调试日志输出，避免测试时产生大量噪音信息
suppress_pil_debug_logs()

logger = get_logger(__name__)


def auto_test(config_path: str = "config/model_config.yaml") -> None:
    """自动测试最佳可用模型
    
    Args:
        config_path: 配置文件路径
    """
    try:
        logger.info("开始自动模型测试")
        
        # 创建测试器
        tester = YOLOTester(config_path)
        
        # 执行自动测试（会自动查找最佳模型）
        results = tester.test_model()
        
        logger.info("自动测试完成")
        logger.info(f"测试结果: {results}")
        
    except Exception as e:
        logger.error(f"自动测试失败: {e}")
        raise


def test_latest_model(mode: str = 'fixed_params', config_path: str = "config/model_config.yaml") -> None:
    """测试最新训练的模型
    
    Args:
        mode: 训练模式
        config_path: 配置文件路径
    """
    try:
        logger.info(f"开始测试最新{mode}模型")
        
        # 创建测试器
        tester = YOLOTester(config_path)
        
        # 测试最新模型
        results = tester.test_latest_model(mode)
        
        logger.info("最新模型测试完成")
        logger.info(f"测试结果: {results}")
        
    except Exception as e:
        logger.error(f"测试最新模型失败: {e}")
        raise


def test_best_model(mode: str = 'fixed_params', config_path: str = "config/model_config.yaml") -> None:
    """测试最佳训练的模型
    
    Args:
        mode: 训练模式
        config_path: 配置文件路径
    """
    try:
        logger.info(f"开始测试最佳{mode}模型")
        
        # 创建测试器
        tester = YOLOTester(config_path)
        
        # 测试最佳模型
        results = tester.test_best_model(mode)
        
        logger.info("最佳模型测试完成")
        logger.info(f"测试结果: {results}")
        
    except Exception as e:
        logger.error(f"测试最佳模型失败: {e}")
        raise


def test_specific_model(model_path: str, config_path: str = "config/model_config.yaml") -> None:
    """测试指定的模型
    
    Args:
        model_path: 模型文件路径
        config_path: 配置文件路径
    """
    try:
        logger.info(f"开始测试指定模型: {model_path}")
        
        # 创建测试器
        tester = YOLOTester(config_path)
        
        # 测试指定模型
        results = tester.test_model(model_path)
        
        logger.info("指定模型测试完成")
        logger.info(f"测试结果: {results}")
        
    except Exception as e:
        logger.error(f"测试指定模型失败: {e}")
        raise


def batch_test_models(model_paths: List[str], config_path: str = "config/model_config.yaml") -> None:
    """批量测试多个模型
    
    Args:
        model_paths: 模型文件路径列表
        config_path: 配置文件路径
    """
    try:
        logger.info(f"开始批量测试{len(model_paths)}个模型")
        
        # 创建测试器
        tester = YOLOTester(config_path)
        
        # 批量测试
        results = tester.batch_test_models(model_paths)
        
        logger.info("批量测试完成")
        logger.info(f"测试结果: {results}")
        
        # 输出比较结果
        if 'comparison' in results:
            comparison = results['comparison']
            logger.info("模型比较结果:")
            logger.info(f"  总测试数: {comparison.get('total_tests', 0)}")
            logger.info(f"  成功测试: {comparison.get('successful_tests', 0)}")
            logger.info(f"  失败测试: {comparison.get('failed_tests', 0)}")
            
            if 'metric_comparison' in comparison:
                logger.info("  性能比较:")
                for metric, data in comparison['metric_comparison'].items():
                    logger.info(f"    {metric}: 最佳={data.get('best_value', 'N/A'):.4f} "
                              f"(模型: {Path(data.get('best_model', 'N/A')).name})")
        
    except Exception as e:
        logger.error(f"批量测试失败: {e}")
        raise


def compare_all_models(config_path: str = "config/model_config.yaml") -> None:
    """比较所有可用模型的性能
    
    Args:
        config_path: 配置文件路径
    """
    try:
        logger.info("开始比较所有可用模型")
        
        # 创建测试器
        tester = YOLOTester(config_path)
        
        # 获取所有模型
        all_models = tester.model_manager.list_all_models()
        
        if not all_models:
            logger.warning("未找到任何模型文件")
            return
        
        # 提取模型路径
        model_paths = [model['model_path'] for model in all_models]
        
        # 批量测试
        batch_test_models(model_paths, config_path)
        
    except Exception as e:
        logger.error(f"比较所有模型失败: {e}")
        raise


def quick_model_comparison(config_path: str = "config/model_config.yaml") -> None:
    """快速模型比较（只比较最新的几个模型）
    
    Args:
        config_path: 配置文件路径
    """
    try:
        logger.info("开始快速模型比较")
        
        # 创建测试器
        tester = YOLOTester(config_path)
        
        # 获取最新的几个模型
        all_models = tester.model_manager.list_all_models()
        
        if not all_models:
            logger.warning("未找到任何模型文件")
            return
        
        # 取前5个最新模型
        recent_models = all_models[:5]
        model_paths = [model['model_path'] for model in recent_models]
        
        logger.info(f"比较最新的{len(model_paths)}个模型")
        
        # 批量测试
        batch_test_models(model_paths, config_path)
        
    except Exception as e:
        logger.error(f"快速模型比较失败: {e}")
        raise


# IDE中直接运行的示例
if __name__ == "__main__":
    # 示例1: 自动测试（测试最佳可用模型）
    auto_test()
    
    # 示例2: 测试最新模型
    # test_latest_model('fixed_params')
    
    # 示例3: 测试最佳模型
    # test_best_model('fixed_params')
    
    # 示例4: 测试指定模型
    # test_specific_model('path/to/your/model.pt')
    
    # 示例5: 快速模型比较
    # quick_model_comparison()
    
    # 示例6: 比较所有模型
    # compare_all_models()
    
    print("测试工具准备就绪。请在IDE中调用相应的函数来执行测试任务。") 
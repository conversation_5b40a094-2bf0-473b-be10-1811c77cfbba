"""
备份标注工具
将数据库备份的图像数据转换为YOLO训练格式的独立执行工具
专为IDE运行设计，无需命令行参数

启用DEBUG日志的方法：
1. 在IDE中调试运行（会自动检测debugpy模块）
2. 设置环境变量：YOLO_DEBUG=true 或 YOLO_LOG_LEVEL=DEBUG
3. 运行时添加参数：python backup_annotation_tool.py --debug
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src.basic.config_manager import ConfigManager
from src.data_processing.format_converters.backup_annotation_converter import BackupAnnotationConverter
import logging
from src.basic.logger import get_logger

def main():
    """主函数"""

    # 设置日志 - 使用项目统一的日志管理系统
    logger = get_logger(__name__)

    logger.info("="*50)
    logger.info("备份标注工具启动")
    logger.info("="*50)
    
    try:
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.get_backup_annotation_config()
        
        if not config:
            logger.error("❌ 错误：未找到backup_annotation配置节")
            return False
            
        logger.info("✅ 配置加载成功")
        
        # 显示配置信息
        data_source = config.get('data_source', {})
        processing = config.get('processing', {})
        output = config.get('output', {})
        
        logger.info(f"   CSV文件: {data_source.get('csv_file', '')}")
        logger.info(f"   输出目录: {output.get('base_dir', '')}")
        logger.info(f"   目标类别: {processing.get('target_classes', []) or '全部'}")
        logger.info(f"   最大处理数量: {processing.get('max_process_count', -1) or '无限制'}")
        logger.debug(f"   图像比较: {'开启' if processing.get('image_comparison', {}).get('enabled', True) else '关闭'}")
        logger.debug(f"   颜色检测: {'开启' if processing.get('color_detection', {}).get('enabled', True) else '关闭'}")
        logger.debug(f"   轮廓合并策略: {processing.get('contour_merge_strategy', 'union')}")
        logger.debug(f"   调试模式: {'开启' if config.get('debug_mode', False) else '关闭'}")
        
        # 验证输入文件
        csv_file = data_source.get('csv_file', '')
        if not os.path.exists(csv_file):
            logger.error(f"❌ 错误：CSV文件不存在 - {csv_file}")
            return False
        
        # 初始化转换器
        print("\n初始化转换器...")
        converter = BackupAnnotationConverter(config)
        
        # 执行转换
        print("开始执行备份标注转换...")
        output_dir = output.get('base_dir', 'data/processed/backup_annotation')
        
        success = converter.convert(csv_file, output_dir)
        
        if success:
            logger.info("\n" + "="*50)
            logger.info("✅ 备份标注工具执行完成！")
            logger.info(f"📂 输出目录: {output_dir}")
            logger.info(f"📊 处理统计:")
            logger.info(f"   总处理数量: {converter.processed_count}")
            logger.info(f"   成功数量: {converter.success_count}")
            logger.info(f"   跳过数量: {converter.skipped_count}")
            logger.info(f"   错误数量: {converter.error_count}")
            if converter.processed_count > 0:
                success_rate = converter.success_count / converter.processed_count * 100
                logger.info(f"   成功率: {success_rate:.2f}%")
            logger.debug("="*50)
            return True
        else:
            logger.error("\n❌ 备份标注转换失败！")
            return False
            
    except KeyboardInterrupt:
        logger.error("\n⚠️ 用户中断执行")
        return False
    except Exception as e:
        logger.error(f"💥 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    try:
        success = main()
        if success:
            logging.info("\n🎉 程序执行成功！")
        else:
            logging.error("\n❌ 程序执行失败！")
    except Exception as e:
        logging.error(f"\n💥 程序执行异常: {e}")
        import traceback
        traceback.print_exc() 
#!/usr/bin/env python3
"""
训练工具入口

基于配置文件的YOLO模型训练工具，适合在IDE中运行。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.model_training.trainer import YOLOTrainer
from src.basic.logger import get_logger, suppress_pil_debug_logs

# 抑制PIL等第三方库的调试日志输出，避免训练时产生大量噪音信息
suppress_pil_debug_logs()

logger = get_logger(__name__)


def train_with_fixed_params(config_path: str = "config/model_config.yaml") -> None:
    """使用固定参数进行训练
    
    使用data/final目录的完整数据集进行训练，适合测试代码可行性。
    
    Args:
        config_path: 配置文件路径
    """
    try:
        logger.info("开始固定参数训练")
        
        # 创建训练器
        trainer = YOLOTrainer(config_path)
        
        # 执行训练
        results = trainer.train_with_fixed_params()
        
        logger.info("固定参数训练完成")
        logger.info(f"训练结果: {results}")
        
    except Exception as e:
        logger.error(f"固定参数训练失败: {e}")
        raise


def train_with_hyperparameter_search(config_path: str = "config/model_config.yaml") -> None:
    """使用超参数搜索进行训练
    
    使用data/search目录的数据子集进行超参数搜索，提高搜索效率。
    支持单线程和并行搜索，通过配置文件控制。
    
    Args:
        config_path: 配置文件路径
    """
    try:
        logger.info("开始超参数搜索训练")
        
        # 创建训练器
        trainer = YOLOTrainer(config_path)
        
        # 执行训练（并行配置在配置文件中控制）
        results = trainer.train_with_hyperparameter_search()
        
        logger.info("超参数搜索训练完成")
        logger.info(f"训练结果: {results}")
        
        # 显示最佳结果信息
        if 'best_fitness' in results:
            logger.info(f"最佳适应度: {results['best_fitness']:.4f}")
        if 'search_strategy' in results:
            logger.info(f"搜索策略: {results['search_strategy']}")
        
    except Exception as e:
        logger.error(f"超参数搜索训练失败: {e}")
        raise


def resume_training(checkpoint_path: str, config_path: str = "config/model_config.yaml") -> None:
    """从检查点恢复训练
    
    Args:
        checkpoint_path: 检查点文件路径
        config_path: 配置文件路径
    """
    try:
        logger.info(f"从检查点恢复训练: {checkpoint_path}")
        
        # 创建训练器
        trainer = YOLOTrainer(config_path)
        
        # 执行恢复训练
        results = trainer.resume_training(checkpoint_path)
        
        logger.info("恢复训练完成")
        logger.info(f"训练结果: {results}")
        
    except Exception as e:
        logger.error(f"恢复训练失败: {e}")
        raise


def get_latest_model_info(mode: str = 'fixed_params', config_path: str = "config/model_config.yaml") -> None:
    """获取最新模型信息
    
    Args:
        mode: 训练模式
        config_path: 配置文件路径
    """
    try:
        trainer = YOLOTrainer(config_path)
        latest_model = trainer.get_latest_model_path(mode)
        
        if latest_model:
            logger.info(f"最新模型路径: {latest_model}")
        else:
            logger.warning(f"未找到{mode}模式的最新模型")
            
    except Exception as e:
        logger.error(f"获取最新模型信息失败: {e}")


def get_best_model_info(mode: str = 'fixed_params', config_path: str = "config/model_config.yaml") -> None:
    """获取最佳模型信息
    
    Args:
        mode: 训练模式
        config_path: 配置文件路径
    """
    try:
        trainer = YOLOTrainer(config_path)
        best_model = trainer.get_best_model_path(mode)
        
        if best_model:
            logger.info(f"最佳模型路径: {best_model}")
        else:
            logger.warning(f"未找到{mode}模式的最佳模型")
            
    except Exception as e:
        logger.error(f"获取最佳模型信息失败: {e}")


def list_all_models(config_path: str = "config/model_config.yaml") -> None:
    """列出所有可用模型
    
    Args:
        config_path: 配置文件路径
    """
    try:
        trainer = YOLOTrainer(config_path)
        models = trainer.model_manager.list_all_models()
        
        logger.info(f"找到{len(models)}个模型:")
        for i, model in enumerate(models, 1):
            logger.info(f"{i}. {model['model_name']} - {model['directory']} ({model['size_mb']:.2f}MB)")
            
    except Exception as e:
        logger.error(f"列出模型失败: {e}")


# IDE中直接运行的示例
if __name__ == "__main__":
    # 示例1: 固定参数训练
    # train_with_fixed_params()
    
    # 示例2: 超参数搜索训练（并行配置在config/model_config.yaml中控制）
    train_with_hyperparameter_search()
    
    # 示例3: 查看所有模型
    # list_all_models()
    
    # 示例4: 查看最新模型信息
    # get_latest_model_info('fixed_params')
    
    # 示例5: 查看最佳模型信息
    # get_best_model_info('fixed_params')
    
    print("训练工具准备就绪。请在IDE中调用相应的函数来执行训练任务。") 
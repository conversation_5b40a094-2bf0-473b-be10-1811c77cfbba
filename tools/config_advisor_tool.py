#!/usr/bin/env python3
"""
智能配置建议工具
根据数据集统计信息和硬件资源，自动推荐最优的训练配置参数

主要功能：
1. 分析数据集规模和特征
2. 检测硬件资源（GPU、CPU、内存）
3. 推荐最优的batch size、workers等参数
4. 生成配置建议报告

使用方法：
    python tools/config_advisor_tool.py
    或在IDE中直接运行相应函数
"""

import json
import os
import sys
import psutil
import yaml
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.basic.logger import get_logger

class HardwareDetector:
    """硬件资源检测器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def detect_gpu_info(self) -> Dict[str, Any]:
        """检测GPU信息"""
        gpu_info = {
            "available": False,
            "count": 0,
            "memory_total": 0,
            "memory_free": 0,
            "name": "Unknown"
        }
        
        try:
            import torch
            if torch.cuda.is_available():
                gpu_info["available"] = True
                gpu_info["count"] = torch.cuda.device_count()
                
                # 获取第一个GPU的信息
                device = torch.cuda.get_device_properties(0)
                gpu_info["name"] = device.name
                gpu_info["memory_total"] = device.total_memory / (1024**3)  # GB
                
                # 获取当前可用内存
                torch.cuda.empty_cache()
                memory_free = torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)
                gpu_info["memory_free"] = memory_free / (1024**3)  # GB
                
                self.logger.info(f"检测到GPU: {gpu_info['name']}, "
                               f"总内存: {gpu_info['memory_total']:.1f}GB, "
                               f"可用内存: {gpu_info['memory_free']:.1f}GB")
            else:
                self.logger.warning("未检测到可用的GPU")
                
        except ImportError:
            self.logger.warning("未安装PyTorch，无法检测GPU信息")
        except Exception as e:
            self.logger.error(f"GPU检测出错: {e}")
            
        return gpu_info
    
    def detect_cpu_info(self) -> Dict[str, Any]:
        """检测CPU信息"""
        cpu_info = {
            "physical_cores": psutil.cpu_count(logical=False),
            "logical_cores": psutil.cpu_count(logical=True),
            "frequency": psutil.cpu_freq().current if psutil.cpu_freq() else 0,
            "usage_percent": psutil.cpu_percent(interval=1)
        }
        
        self.logger.info(f"检测到CPU: {cpu_info['physical_cores']}物理核心, "
                        f"{cpu_info['logical_cores']}逻辑核心, "
                        f"当前使用率: {cpu_info['usage_percent']}%")
        
        return cpu_info
    
    def detect_memory_info(self) -> Dict[str, Any]:
        """检测内存信息"""
        memory = psutil.virtual_memory()
        memory_info = {
            "total": memory.total / (1024**3),  # GB
            "available": memory.available / (1024**3),  # GB
            "used": memory.used / (1024**3),  # GB
            "percent": memory.percent
        }
        
        self.logger.info(f"系统内存: 总计{memory_info['total']:.1f}GB, "
                        f"可用{memory_info['available']:.1f}GB, "
                        f"使用率{memory_info['percent']:.1f}%")
        
        return memory_info
    
    def get_all_hardware_info(self) -> Dict[str, Any]:
        """获取所有硬件信息"""
        return {
            "gpu": self.detect_gpu_info(),
            "cpu": self.detect_cpu_info(),
            "memory": self.detect_memory_info()
        }


class DatasetAnalyzer:
    """数据集分析器"""
    
    def __init__(self, statistics_path: str):
        self.statistics_path = statistics_path
        self.logger = get_logger(__name__)
    
    def load_dataset_statistics(self) -> Optional[Dict[str, Any]]:
        """加载数据集统计信息"""
        try:
            if not os.path.exists(self.statistics_path):
                self.logger.warning(f"统计文件不存在: {self.statistics_path}")
                return None
                
            with open(self.statistics_path, 'r', encoding='utf-8') as f:
                stats = json.load(f)
                
            self.logger.info(f"加载数据集统计信息: {self.statistics_path}")
            return stats
            
        except Exception as e:
            self.logger.error(f"加载统计信息失败: {e}")
            return None
    
    def analyze_dataset_size(self, stats: Optional[Dict[str, Any]]) -> str:
        """分析数据集大小等级"""
        if not stats:
            return "unknown"
            
        # 尝试获取总样本数
        total_samples = 0
        
        # 尝试不同的统计信息结构
        if "total_samples" in stats:
            total_samples = stats["total_samples"]
        elif "summary" in stats and "total_images" in stats["summary"]:
            total_samples = stats["summary"]["total_images"]
        elif "splits" in stats:
            # 计算所有分割的总和
            for split_name, split_info in stats["splits"].items():
                if isinstance(split_info, dict) and "image_count" in split_info:
                    total_samples += split_info["image_count"]
        
        self.logger.info(f"数据集总样本数: {total_samples}")
        
        # 根据样本数分类
        if total_samples < 1000:
            return "small"
        elif total_samples < 10000:
            return "medium"
        else:
            return "large"
    
    def get_class_count(self, stats: Optional[Dict[str, Any]]) -> int:
        """获取类别数量"""
        if not stats:
            return 0
            
        # 尝试不同的统计信息结构
        if "class_count" in stats:
            return stats["class_count"]
        elif "class_mapping" in stats:
            return len(stats["class_mapping"])
        elif "summary" in stats and "num_classes" in stats["summary"]:
            return stats["summary"]["num_classes"]
        
        return 0


class ConfigAdvisor:
    """配置建议器"""
    
    def __init__(self, config_path: str = "config/model_config.yaml"):
        self.config_path = config_path
        self.logger = get_logger(__name__)
        self.hardware_detector = HardwareDetector()
        
        # 加载配置文件
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def recommend_batch_size(self, gpu_memory_gb: float) -> int:
        """根据GPU内存推荐batch size"""
        rules = self.config.get("auto_config", {}).get("recommendation_rules", {}).get("batch_size_rules", {})
        
        # 默认规则
        default_rules = {4: 8, 6: 12, 8: 16, 11: 20, 24: 32}
        rules = rules or default_rules
        
        # 找到最适合的batch size
        for memory_threshold in sorted(rules.keys()):
            if gpu_memory_gb <= memory_threshold:
                return rules[memory_threshold]
        
        # 如果GPU内存很大，返回最大的batch size
        return max(rules.values())
    
    def recommend_workers(self, cpu_cores: int) -> int:
        """根据CPU核心数推荐workers数量"""
        rules = self.config.get("auto_config", {}).get("recommendation_rules", {}).get("workers_rules", {})
        
        # 默认规则
        min_workers = rules.get("min_workers", 2)
        max_workers = rules.get("max_workers", 16)
        ratio = rules.get("ratio", 0.5)
        
        # 计算推荐值
        recommended = int(cpu_cores * ratio)
        
        # 限制在合理范围内
        return max(min_workers, min(recommended, max_workers))
    
    def recommend_epochs(self, dataset_size: str) -> int:
        """根据数据集大小推荐epochs"""
        rules = self.config.get("auto_config", {}).get("recommendation_rules", {}).get("other_params", {}).get("epochs_by_dataset_size", {})
        
        # 默认规则
        default_rules = {"small": 150, "medium": 100, "large": 50}
        rules = rules or default_rules
        
        return rules.get(dataset_size, 100)
    
    def recommend_learning_rate(self, batch_size: int, dataset_size: str) -> float:
        """根据batch size和数据集大小推荐学习率"""
        # 基础学习率
        base_lr = 0.01
        
        # 根据batch size调整（线性缩放规则）
        lr_scale = batch_size / 16  # 基准batch size为16
        adjusted_lr = base_lr * lr_scale
        
        # 根据数据集大小微调
        if dataset_size == "small":
            adjusted_lr *= 0.8  # 小数据集用较小学习率
        elif dataset_size == "large":
            adjusted_lr *= 1.2  # 大数据集可以用较大学习率
        
        # 限制在合理范围内
        return max(0.001, min(adjusted_lr, 0.1))
    
    def generate_recommendations(self) -> Dict[str, Any]:
        """生成完整的配置建议"""
        self.logger.info("开始生成配置建议...")
        
        # 检测硬件信息
        hardware_info = self.hardware_detector.get_all_hardware_info()
        
        # 分析数据集
        dataset_config = self.config.get("dataset", {})
        statistics_path = dataset_config.get("statistics_path", "data/final/statistics.json")
        
        analyzer = DatasetAnalyzer(statistics_path)
        dataset_stats = analyzer.load_dataset_statistics()
        dataset_size = analyzer.analyze_dataset_size(dataset_stats)
        class_count = analyzer.get_class_count(dataset_stats)
        
        # 生成建议
        recommendations = {
            "hardware_info": hardware_info,
            "dataset_info": {
                "size_category": dataset_size,
                "class_count": class_count,
                "statistics": dataset_stats
            },
            "recommended_config": {}
        }
        
        # 推荐batch size
        if hardware_info["gpu"]["available"]:
            batch_size = self.recommend_batch_size(hardware_info["gpu"]["memory_total"])
            device = "cuda"
        else:
            batch_size = 4  # CPU训练用较小的batch size
            device = "cpu"
        
        # 推荐workers
        workers = self.recommend_workers(hardware_info["cpu"]["logical_cores"])
        
        # 推荐epochs
        epochs = self.recommend_epochs(dataset_size)
        
        # 推荐学习率
        lr0 = self.recommend_learning_rate(batch_size, dataset_size)
        
        # 推荐图像尺寸（根据硬件性能）
        if hardware_info["gpu"]["memory_total"] >= 8:
            imgsz = 640
        elif hardware_info["gpu"]["memory_total"] >= 4:
            imgsz = 512
        else:
            imgsz = 416
        
        recommendations["recommended_config"] = {
            "device": device,
            "batch": batch_size,
            "workers": workers,
            "epochs": epochs,
            "lr0": lr0,
            "imgsz": imgsz,
            "patience": max(10, epochs // 10)  # 耐心值为epochs的10%，最少10
        }
        
        # 添加建议说明
        recommendations["explanations"] = {
            "batch_size": f"根据GPU内存{hardware_info['gpu']['memory_total']:.1f}GB推荐batch size为{batch_size}",
            "workers": f"根据CPU核心数{hardware_info['cpu']['logical_cores']}推荐workers为{workers}",
            "epochs": f"根据数据集大小({dataset_size})推荐epochs为{epochs}",
            "learning_rate": f"根据batch size和数据集大小推荐学习率为{lr0:.4f}",
            "image_size": f"根据GPU内存推荐图像尺寸为{imgsz}"
        }
        
        return recommendations
    
    def save_recommendations(self, recommendations: Dict[str, Any], output_path: str = "config_recommendations.json"):
        """保存建议到文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(recommendations, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置建议已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存建议失败: {e}")
    
    def print_recommendations(self, recommendations: Dict[str, Any]):
        """打印建议信息"""
        print("\n" + "="*60)
        print("🤖 智能配置建议报告")
        print("="*60)
        
        # 硬件信息
        hw = recommendations["hardware_info"]
        print(f"\n📊 硬件信息:")
        if hw["gpu"]["available"]:
            print(f"  GPU: {hw['gpu']['name']}")
            print(f"  GPU内存: {hw['gpu']['memory_total']:.1f}GB (可用: {hw['gpu']['memory_free']:.1f}GB)")
        else:
            print(f"  GPU: 不可用，将使用CPU训练")
        
        print(f"  CPU: {hw['cpu']['physical_cores']}物理核心, {hw['cpu']['logical_cores']}逻辑核心")
        print(f"  系统内存: {hw['memory']['total']:.1f}GB (可用: {hw['memory']['available']:.1f}GB)")
        
        # 数据集信息
        ds = recommendations["dataset_info"]
        print(f"\n📁 数据集信息:")
        print(f"  大小等级: {ds['size_category']}")
        print(f"  类别数量: {ds['class_count']}")
        
        # 推荐配置
        config = recommendations["recommended_config"]
        print(f"\n🎯 推荐配置:")
        print(f"  device: {config['device']}")
        print(f"  batch: {config['batch']}")
        print(f"  workers: {config['workers']}")
        print(f"  epochs: {config['epochs']}")
        print(f"  lr0: {config['lr0']:.4f}")
        print(f"  imgsz: {config['imgsz']}")
        print(f"  patience: {config['patience']}")
        
        # 说明
        explanations = recommendations["explanations"]
        print(f"\n💡 建议说明:")
        for key, explanation in explanations.items():
            print(f"  • {explanation}")
        
        print("\n" + "="*60)


def analyze_system_and_recommend():
    """分析系统并生成配置建议"""
    advisor = ConfigAdvisor()
    recommendations = advisor.generate_recommendations()
    
    # 打印建议
    advisor.print_recommendations(recommendations)
    
    # 保存建议
    advisor.save_recommendations(recommendations)
    
    return recommendations


def update_config_with_recommendations():
    """根据建议更新配置文件"""
    advisor = ConfigAdvisor()
    recommendations = advisor.generate_recommendations()
    
    # 读取当前配置
    with open(advisor.config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    recommended_config = recommendations["recommended_config"]
    
    # 更新固定参数训练配置
    fixed_params = config.setdefault("training_modes", {}).setdefault("fixed_params", {})
    fixed_params.update({
        "batch": recommended_config["batch"],
        "workers": recommended_config["workers"],
        "epochs": recommended_config["epochs"],
        "lr0": recommended_config["lr0"],
        "imgsz": recommended_config["imgsz"],
        "patience": recommended_config["patience"]
    })
    
    # 更新超参数搜索配置
    hyper_search = config.setdefault("training_modes", {}).setdefault("hyperparameter_search", {})
    hyper_search.update({
        "batch": recommended_config["batch"],
        "workers": recommended_config["workers"],
        "epochs": recommended_config["epochs"],
        "imgsz": recommended_config["imgsz"]
    })
    
    # 更新测试配置
    testing = config.setdefault("testing", {})
    testing.update({
        "batch": min(recommended_config["batch"] * 2, 32),  # 测试可以用更大的batch
        "workers": recommended_config["workers"],
        "imgsz": recommended_config["imgsz"]
    })
    
    # 更新环境配置
    environment = config.setdefault("environment", {})
    environment["device"] = recommended_config["device"]
    
    # 备份原配置
    backup_path = advisor.config_path + ".backup"
    with open(backup_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    # 保存更新后的配置
    with open(advisor.config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"\n✅ 配置文件已更新: {advisor.config_path}")
    print(f"📁 原配置已备份: {backup_path}")
    
    return config


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="智能配置建议工具")
    parser.add_argument("--analyze", action="store_true", help="分析系统并生成建议")
    parser.add_argument("--update", action="store_true", help="根据建议自动更新配置文件")
    
    args = parser.parse_args()
    
    if args.update:
        update_config_with_recommendations()
    else:
        analyze_system_and_recommend() 
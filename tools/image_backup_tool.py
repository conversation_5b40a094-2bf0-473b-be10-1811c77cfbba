#!/usr/bin/env python3
"""
数据库图片备份工具

从MySQL数据库查询图片数据，复制图片文件，进行去重和相似度检测。
"""

import os
import sys
import shutil
import mysql.connector
from pathlib import Path
import pandas as pd
from PIL import Image
import imagehash
from datetime import datetime
from tqdm import tqdm
from typing import Dict, List, Any, Optional, Union

# 添加项目根目录到 Python 路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# 导入配置管理模块
from src.basic.config_manager import config_manager


class ImageBackupTool:
    """数据库图片备份工具类"""
    
    def __init__(self):
        """初始化工具"""
        self.db_config = config_manager.get_database_config()
        self.backup_config = config_manager.get_image_backup_config()
        
    def connect_to_database(self) -> Optional[Any]:
        """连接到数据库"""
        try:
            connection = mysql.connector.connect(**self.db_config)
            print(f"成功连接到数据库: {self.db_config['host']}")
            return connection
        except mysql.connector.Error as err:
            print(f"数据库连接错误: {err}")
            return None
    
    def get_image_data(self, connection: Any,
                      conditions: Optional[List[str]] = None,
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None) -> List[Any]:
        """从数据库获取图片数据"""
        if conditions is None:
            conditions = self.backup_config.get('search_conditions', ['5', '7', '8'])
        
        # 确保conditions不为None
        if not conditions:
            conditions = ['5', '7', '8']
        
        # 从配置文件读取时间范围
        if start_date is None or end_date is None:
            date_range = self.backup_config.get('date_range', {})
            start_date = date_range.get('start_date', '2025-06-30 00:00:00')
            end_date = date_range.get('end_date', '2025-08-11 14:00:00')
        
        try:
            cursor = connection.cursor(dictionary=True)
            # 构建条件SQL
            conditions_sql = " OR ".join([
                f"event_content_pro REGEXP '\\\\b{condition}\\\\b'" 
                for condition in conditions
            ])
            
            # SQL查询
            query = f"""
                SELECT initfile_name, intefile_name, business_type, event_content_pro
                FROM event_data
                WHERE ({conditions_sql})
                AND event_result = 1 
                AND business_type IS NOT NULL
                AND save_date >= %s 
                AND save_date < %s
            """
            
            print("\n执行的 SQL 语句:")
            print(query)
            print(f"参数: start_date={start_date}, end_date={end_date}")
            
            cursor.execute(query, (start_date, end_date))
            return cursor.fetchall()
        except mysql.connector.Error as err:
            print(f"查询执行错误: {err}")
            return []
        finally:
            cursor.close()
    
    def save_image_data(self, image_data: List[Any]) -> None:
        """保存查询结果到CSV文件"""
        csv_path = Path("data/raw/backup/query_results.csv")
        
        # 检查现有数据一致性
        if self._check_csv_data_consistency(csv_path, image_data):
            print(f"CSV数据已是最新，无需更新。文件: {csv_path}")
            return
        
        # 数据不一致，进行更新
        df = pd.DataFrame(image_data)
        # 确保目录存在
        csv_path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(csv_path, index=False)
        print(f"查询结果已更新到: {csv_path}")
        print(f"共找到 {len(image_data)} 个不同的业务类型和事件内容组合")
    
    def _check_csv_data_consistency(self, csv_path: Path, new_data: List[Any]) -> bool:
        """检查CSV数据一致性（增强版）"""
        if not csv_path.exists():
            return False
        
        try:
            existing_df = pd.read_csv(csv_path)
            new_df = pd.DataFrame(new_data)
            
            # 比较行数
            if len(existing_df) != len(new_df):
                print(f"数据行数不一致: 现有 {len(existing_df)} 行，新数据 {len(new_df)} 行")
                return False
            
            # 比较关键列内容（如果列结构相同）
            if set(existing_df.columns) == set(new_df.columns):
                # 按列排序确保比较准确性
                existing_sorted = existing_df.sort_values(by=list(existing_df.columns)).reset_index(drop=True)
                new_sorted = new_df.sort_values(by=list(new_df.columns)).reset_index(drop=True)
                
                if existing_sorted.equals(new_sorted):
                    print(f"CSV数据完全一致: {len(new_data)} 行")
                    return True
                else:
                    print("CSV数据内容不一致，需要更新")
                    return False
            
            print(f"CSV数据行数一致: {len(new_data)} 行")
            return True
            
        except Exception as e:
            print(f"读取现有CSV文件时出错: {e}")
            return False
    
    def copy_images(self, image_data: List[Any]) -> bool:
        """复制图片到目标目录（增量更新）"""
        source_dir = Path(self.backup_config.get('source_dir', '/path/to/source/images'))
        
        # 定义两种文件的目标目录
        output_dirs = self.backup_config.get('output_dirs', {})
        target_dirs = {
            'initfile_name': Path(output_dirs.get('initfile', 'data/raw/backup/initfile')),
            'intefile_name': Path(output_dirs.get('intefile', 'data/raw/backup/intefile'))
        }
        
        # 确保目标目录存在
        for dir_path in target_dirs.values():
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 检查是否需要复制
        expected_files = self._get_expected_files(image_data)
        if self._check_images_consistency(target_dirs, expected_files):
            print("图片文件已是最新，无需复制")
            return False
        
        # 获取现有文件列表进行增量复制
        existing_files = self._get_existing_files(target_dirs)
        
        total_copied = 0
        failed_files = []
        files_to_copy = []
        
        # 使用集合运算收集需要复制的文件
        for img_field in target_dirs.keys():
            expected_set = expected_files[img_field]
            existing_set = existing_files[img_field]
            
            # 使用集合运算找出需要复制的文件
            missing_files = expected_set - existing_set
            
            for file_name in missing_files:
                files_to_copy.append((img_field, file_name))
        
        if not files_to_copy:
            print("所有图片文件已存在，无需复制")
            return False
        
        print(f"需要复制 {len(files_to_copy)} 个新文件")
        
        with tqdm(total=len(files_to_copy), desc="复制图片", unit="文件") as pbar:
            for img_field, file_name in files_to_copy:
                source_path = source_dir / file_name
                target_path = target_dirs[img_field] / file_name
                
                try:
                    if source_path.exists():
                        shutil.copy2(source_path, target_path)
                        total_copied += 1
                    else:
                        failed_files.append(f"文件不存在: {file_name}")
                except Exception as e:
                    failed_files.append(f"复制文件时出错 {file_name}: {e}")
                
                pbar.update(1)
        
        print(f"\n复制完成: 成功 {total_copied} 个文件")
        if failed_files:
            print(f"失败 {len(failed_files)} 个文件:")
            for error in failed_files[:10]:  # 只显示前10个错误
                print(f"  {error}")
            if len(failed_files) > 10:
                print(f"  ... 还有 {len(failed_files) - 10} 个错误")
        
        # 返回是否有新增图片：复制的文件数量大于失败的文件数量
        return len(files_to_copy) > len(failed_files)
    
    def _get_expected_files(self, image_data: List[Any]) -> Dict[str, set]:
        """获取预期的文件列表（使用pandas优化）"""
        df = pd.DataFrame(image_data)
        expected_files = {}
        
        for img_field in ['initfile_name', 'intefile_name']:
            if img_field in df.columns:
                # 使用pandas向量化操作，过滤空值并去重
                files = df[img_field].dropna().str.strip()
                files = files[files != '']  # 过滤空字符串
                expected_files[img_field] = set(files.tolist())
            else:
                expected_files[img_field] = set()
        
        return expected_files
    
    def _get_existing_files(self, target_dirs: Dict[str, Path]) -> Dict[str, set]:
        """获取现有文件列表"""
        existing_files = {}
        
        for img_field, dir_path in target_dirs.items():
            existing_files[img_field] = set()
            if dir_path.exists():
                for file_path in dir_path.glob('*'):
                    if file_path.is_file():
                        existing_files[img_field].add(file_path.name)
        
        return existing_files
    
    def _check_images_consistency(self, target_dirs: Dict[str, Path], expected_files: Dict[str, set]) -> bool:
        """检查图片文件一致性（使用集合运算优化）"""
        existing_files = self._get_existing_files(target_dirs)
        
        for img_field in target_dirs.keys():
            expected_set = expected_files[img_field]
            existing_set = existing_files[img_field]
            
            # 使用集合运算检查差异
            if expected_set != existing_set:
                missing = expected_set - existing_set
                extra = existing_set - expected_set
                
                print(f"{img_field} 目录文件不一致:")
                print(f"  预期: {len(expected_set)} 个，现有: {len(existing_set)} 个")
                if missing:
                    print(f"  缺失: {len(missing)} 个文件")
                if extra:
                    print(f"  多余: {len(extra)} 个文件")
                return False
        
        total_expected = sum(len(files) for files in expected_files.values())
        print(f"图片文件数量一致: 共 {total_expected} 个文件")
        return True
    
    def _sync_directories_with_csv(self, csv_path: Path,
                                  source_dirs: Dict[str, Path],
                                  target_dirs: Dict[str, Path]) -> None:
        """基于CSV同步目录文件，删除多余文件，补充缺失文件"""
        # 1. 读取CSV获取应该存在的文件
        df = pd.read_csv(csv_path)
        expected_files = {
            'initfile_name': set(df['initfile_name'].dropna().str.strip()),
            'intefile_name': set(df['intefile_name'].dropna().str.strip())
        }
        
        # 2. 获取目标目录现有文件
        existing_files = self._get_existing_files(target_dirs)
        
        # 3. 处理每个目录
        for img_field, target_dir in target_dirs.items():
            expected_set = expected_files[img_field]
            existing_set = existing_files[img_field]
            
            # 删除多余文件
            extra_files = existing_set - expected_set
            for file_name in extra_files:
                file_path = target_dir / file_name
                if file_path.exists():
                    file_path.unlink()
                    print(f"删除多余文件: {file_name}")
            
            # 复制缺失文件
            missing_files = expected_set - existing_set
            copied_count = 0
            for file_name in missing_files:
                source_path = source_dirs[img_field] / file_name
                target_path = target_dir / file_name
                if source_path.exists():
                    shutil.copy2(source_path, target_path)
                    copied_count += 1
                else:
                    print(f"警告：源文件不存在: {file_name}")
            
            if extra_files or missing_files:
                print(f"{img_field}: 删除 {len(extra_files)} 个多余文件，补充 {copied_count} 个缺失文件")
        
        print(f"目录同步完成，CSV记录: {len(df)} 条")
    
    def get_image_hash(self, image_path: Path) -> Optional[imagehash.ImageHash]:
        """获取图片的哈希值"""
        try:
            return imagehash.average_hash(Image.open(image_path))
        except Exception as e:
            print(f"处理图片时出错 {image_path}: {e}")
            return None
    
    def find_unique_images(self, directory: Path) -> Dict[str, Any]:
        """查找目录中的唯一图片（优化版）"""
        from collections import defaultdict
        
        # 预先收集所有文件路径
        image_paths = [p for p in directory.glob('*') if p.is_file()]
        
        if not image_paths:
            return {'total_images': 0, 'unique_images': 0, 'unique_paths': [], 'similar_groups': {}}
        
        # 使用字典按哈希值分组，避免重复比较
        hash_groups = defaultdict(list)
        similarity_threshold = self.backup_config.get('similarity_threshold', 90)
        
        with tqdm(total=len(image_paths), desc="查找唯一图片") as pbar:
            for img_path in image_paths:
                img_hash = self.get_image_hash(img_path)
                if img_hash:
                    # 检查是否与现有组相似
                    found_group = False
                    for existing_key in hash_groups.keys():
                        # 从字符串恢复哈希进行比较
                        try:
                            existing_hash = imagehash.hex_to_hash(existing_key)
                            similarity = 100 - (img_hash - existing_hash) * 100 / 64
                            
                            if similarity >= similarity_threshold:
                                hash_groups[existing_key].append(img_path)
                                found_group = True
                                break
                        except:
                            continue
                    
                    if not found_group:
                        hash_groups[str(img_hash)].append(img_path)
                
                pbar.update(1)
        
        # 处理结果
        unique_paths = []
        similar_groups = {}
        
        for hash_key, paths in hash_groups.items():
            if len(paths) == 1:
                unique_paths.append(str(paths[0]))
            else:
                # 多个相似图片，选择第一个作为代表
                representative = paths[0]
                unique_paths.append(str(representative))
                similar_groups[representative] = paths
        
        return {
            'total_images': len(image_paths),
            'unique_images': len(unique_paths),
            'unique_paths': unique_paths,
            'similar_groups': similar_groups
        }
    
    def _get_unique_expected_files(self, init_info: Dict, inte_info: Dict) -> Dict[str, set]:
        """基于唯一图片信息创建预期文件列表"""
        return {
            'initfile_name': {Path(p).name for p in init_info['unique_paths']},
            'intefile_name': {Path(p).name for p in inte_info['unique_paths']}
        }
    
    def _create_unique_dataframe(self, original_df: pd.DataFrame, 
                               unique_files: Dict[str, set]) -> pd.DataFrame:
        """创建包含唯一图片的完整数据DataFrame"""
        filtered_df = original_df[
            (original_df['initfile_name'].isin(list(unique_files['initfile_name']))) & 
            (original_df['intefile_name'].isin(list(unique_files['intefile_name']))) &
            (original_df['initfile_name'].notna()) &
            (original_df['intefile_name'].notna())
        ]
        result_df = filtered_df[['initfile_name', 'intefile_name', 'business_type', 'event_content_pro']].copy()
        return pd.DataFrame(result_df)
    
    def _copy_unique_images(self, unique_df: pd.DataFrame,
                           source_dirs: Dict[str, Path], 
                           target_dirs: Dict[str, Path]) -> None:
        """基于CSV记录复制唯一图片"""
        # 从DataFrame提取需要复制的文件
        files_to_copy = []
        for _, row in unique_df.iterrows():
            for img_field in ['initfile_name', 'intefile_name']:
                try:
                    value = row[img_field]
                    if value is not None and value == value:  # 检查不是None且不是NaN
                        file_name = str(value).strip()
                        if file_name:
                            files_to_copy.append((img_field, file_name))
                except Exception:
                    continue
        
        # 获取现有文件进行增量复制
        existing_files = self._get_existing_files(target_dirs)
        
        # 过滤出需要复制的文件
        files_need_copy = []
        for img_field, file_name in files_to_copy:
            if file_name not in existing_files[img_field]:
                files_need_copy.append((img_field, file_name))
        
        if not files_need_copy:
            print("所有唯一图片已存在，无需复制")
            return
        
        print(f"需要复制 {len(files_need_copy)} 个唯一图片")
        
        total_copied = 0
        failed_files = []
        
        with tqdm(total=len(files_need_copy), desc="复制唯一图片", unit="文件") as pbar:
            for img_field, file_name in files_need_copy:
                source_path = source_dirs[img_field] / file_name
                target_path = target_dirs[img_field] / file_name
                
                try:
                    if source_path.exists():
                        shutil.copy2(source_path, target_path)
                        total_copied += 1
                    else:
                        failed_files.append(f"文件不存在: {file_name}")
                except Exception as e:
                    failed_files.append(f"复制文件时出错 {file_name}: {e}")
                
                pbar.update(1)
        
        print(f"\n唯一图片复制完成: 成功 {total_copied} 个文件")
        if failed_files:
            print(f"失败 {len(failed_files)} 个文件")

    def process_unique_images(self, has_new_images: bool) -> None:
        """处理并重组唯一图片（基于复制状态的智能处理）"""
        # 路径和配置初始化
        csv_path = Path("data/processed/backup/unique_images.csv")
        output_dirs = self.backup_config.get('output_dirs', {})
        
        # 获取两个目录中的唯一图片信息
        source_dirs = {
            'initfile_name': Path(output_dirs.get('initfile', 'data/raw/backup/initfile')),
            'intefile_name': Path(output_dirs.get('intefile', 'data/raw/backup/intefile'))
        }
        
        target_dirs = {
            'initfile_name': Path(output_dirs.get('initfile_unique', 'data/processed/backup/initfile_unique')),
            'intefile_name': Path(output_dirs.get('intefile_unique', 'data/processed/backup/intefile_unique'))
        }
        
        # 确保目标目录存在
        for dir_path in target_dirs.values():
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 核心判断逻辑
        if has_new_images and not csv_path.exists():
            print("检测到新增图片且无现有CSV，执行完整分析流程")
            # 完整分析流程
            df = pd.read_csv("data/raw/backup/query_results.csv")
            
            init_info = self.find_unique_images(source_dirs['initfile_name'])
            inte_info = self.find_unique_images(source_dirs['intefile_name'])
            
            # 打印统计信息
            print("\n初始图片统计:")
            print(f"initfile 目录: 总计 {init_info['total_images']} 张图片，其中唯一图片 {init_info['unique_images']} 张")
            print(f"intefile 目录: 总计 {inte_info['total_images']} 张图片，其中唯一图片 {inte_info['unique_images']} 张")
            
            # 创建唯一文件列表
            unique_files = self._get_unique_expected_files(init_info, inte_info)
            
            # 创建过滤后的DataFrame
            unique_df = self._create_unique_dataframe(df, unique_files)
            print(f"过滤后数据: {len(unique_df)} 条记录包含唯一图片")
            
            # 复制唯一图片（修复：基于CSV记录）
            self._copy_unique_images(unique_df, source_dirs, target_dirs)
            
            # 保存过滤后的CSV
            csv_path.parent.mkdir(parents=True, exist_ok=True)
            unique_df.to_csv(csv_path, index=False)
            print(f"唯一图片数据已保存到: {csv_path}")
            
        else:
            if csv_path.exists():
                print("基于现有CSV进行目录同步检查")
                # 基于CSV的同步模式
                self._sync_directories_with_csv(csv_path, source_dirs, target_dirs)
            else:
                print("无新增图片且无现有CSV，跳过唯一图片处理")
        
        print(f"\n处理完成，唯一图片目录: {target_dirs['initfile_name']} 和 {target_dirs['intefile_name']}")
    
    def run(self) -> None:
        """运行图片备份工具"""
        print("开始执行数据库图片备份...")
        
        # 连接数据库
        connection = self.connect_to_database()
        if not connection:
            return
        
        try:
            # 查询图片数据
            conditions = self.backup_config.get('search_conditions', ['5', '7', '8'])
            image_data = self.get_image_data(connection, conditions)
            print(f"找到 {len(image_data)} 个不同的业务类型和事件内容组合")
            
            if not image_data:
                print("没有找到符合条件的数据")
                return
            
            # 保存查询结果到CSV
            self.save_image_data(image_data)
            
            # 复制图片到对应目录，获取是否有新增图片
            has_new_images = self.copy_images(image_data)
            
            # 基于复制状态处理唯一图片
            self.process_unique_images(has_new_images)
            
            print("\n图片备份完成！")
            
        finally:
            connection.close()


def main():
    """主函数"""
    tool = ImageBackupTool()
    tool.run()


if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
VisDrone2019数据集转换工具
用于将VisDrone2019格式数据转换为YOLO格式
"""

import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data_processing.format_converters.visdrone_to_yolo import VisDroneToYOLOConverter


def convert_visdrone_to_yolo():
    """转换VisDrone2019数据集为YOLO格式"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/visdrone_conversion.log')
        ]
    )
    
    logger = logging.getLogger(__name__)
    
    try:
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        # 定义路径
        source_path = "data/raw/VisDrone2019"
        target_path = "data/processed/VisDrone2019"
        
        # 检查源目录是否存在
        if not Path(source_path).exists():
            logger.error(f"源目录不存在: {source_path}")
            logger.info("请确保已将VisDrone2019数据集放置在data/raw/VisDrone2019目录下")
            return False
        
        logger.info("开始VisDrone2019数据集转换...")
        logger.info(f"源目录: {source_path}")
        logger.info(f"目标目录: {target_path}")
        
        # 创建转换器
        converter = VisDroneToYOLOConverter({})
        
        # 执行转换
        success = converter.convert(source_path, target_path)
        
        if success:
            logger.info("=" * 50)
            logger.info("处理成功完成！")
            logger.info(f"数据保存在: {target_path}")
            logger.info("数据集结构:")
            logger.info("  train/images/ - 训练图像")
            logger.info("  train/labels/ - 训练标签")
            logger.info("  val/images/   - 验证图像")
            logger.info("  val/labels/   - 验证标签")
            logger.info("  test/images/  - 测试图像")
            logger.info("  test/labels/  - 测试标签")
            logger.info("  validation/   - 验证图片（带标注框）")
            logger.info("=" * 50)
            return True
        else:
            logger.error("转换失败！请检查日志信息")
            return False
            
    except Exception as e:
        logger.error(f"转换过程中发生错误: {e}")
        return False


def main():
    """主函数，IDE运行入口"""
    print("VisDrone2019数据集转换工具")
    print("=" * 40)
    
    success = convert_visdrone_to_yolo()
    
    if success:
        print("\n✅ 转换完成！")
    else:
        print("\n❌ 转换失败！")
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main() 
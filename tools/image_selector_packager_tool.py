#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片筛选与打包工具

功能概述（中文）：
- 从 CSV 文件中读取数据（默认 data/raw/backup/query_results.csv），使用 business_type 列进行分类
- 根据指定的业务类别列表与每类所需数量，从图片目录（默认 data/raw/backup/initfile）筛选图片
- 将每个业务类别选出的图片分别打包输出为 zip 文件，输出到指定目录（默认 data/processed/exports）

图片匹配规则（按优先级）：
1) 如果通过 --image-field 指定了列名，并且该列存在：
   - 若值为路径或 URL，则取其 basename 作为文件名进行匹配；
   - 否则将该值视为文件名或 ID；
2) 若未指定 --image-field，则依次尝试以下列名：
   ["image_filename", "image_path", "filename", "file_name", "img_name", "image", "image_url"]
   - 对于 *_path 或 *_url，取 basename 作为候选；
3) 若仍无法得到文件名，则尝试使用 ID 类列：
   ["image_id", "img_id", "id", "uuid", "uid"]，以 id 等于文件名去除扩展名（stem）进行匹配；
4) 兜底：按 business_type 前缀匹配：文件名以 "{business_type}_" 开头视为候选。

健壮性：
- CSV 不存在、图片目录不存在：报错并退出；
- 单类图片不足：记录警告，优先打包已找到的图片，不中断其它类别；
- 日志为中文，便于排查。

用法示例：
python tools/image_selector_packager_tool.py \
  --csv data/raw/backup/query_results.csv \
  --images-dir data/raw/backup/initfile \
  --business-type A --business-type B \
  --count-per-type 50 \
  --output-dir data/processed/exports

进阶：
- 按列名强制匹配： --image-field image_path
- 为不同类别设置不同数量： --count-per-type-map '{"A": 10, "B": 20}'
- 随机抽样： --shuffle
- 严格模式（不足即报错退出非 0）： --strict
"""

from __future__ import annotations

import csv
import logging
import os
import re
import sys
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Set, Tuple
from urllib.parse import urlparse
import zipfile
import random

# ===================== 默认配置（可被命令行覆盖） =====================
DEFAULT_CSV_PATH = "data/raw/backup/query_results.csv"
DEFAULT_IMAGES_DIR = "data/raw/backup/initfile"
DEFAULT_OUTPUT_DIR = "data/exports"
DEFAULT_BUSINESS_COLUMN = "business_type"
DEFAULT_BUSINESS_TYPES: List[str] = ["船只", "施工", "疑似异常藻类", "管路异常", "曝气"]  # 留空表示处理 CSV 中的全部业务类别
DEFAULT_COUNT_PER_TYPE = 10
DEFAULT_IMAGE_EXTS = {".jpg", ".jpeg", ".png", ".bmp", ".gif", ".webp", ".tif", ".tiff"}


# ===================== 脚本配置（硬编码） =====================
CONFIG = {
    "CSV_PATH": DEFAULT_CSV_PATH,
    "IMAGES_DIR": DEFAULT_IMAGES_DIR,
    "OUTPUT_DIR": DEFAULT_OUTPUT_DIR,
    "BUSINESS_COLUMN": DEFAULT_BUSINESS_COLUMN,
    "BUSINESS_TYPES": DEFAULT_BUSINESS_TYPES,  # 留空 [] 则自动从 CSV 中发现
    "COUNT_PER_TYPE": DEFAULT_COUNT_PER_TYPE,
    "COUNT_PER_TYPE_MAP": {},  # 例如：{"船只": 50, "管路异常": 30}
    "IMAGE_FIELD": None,       # 例如："image_path"
    "SHUFFLE": False,
    "STRICT": False,
    "VERBOSE": False,
}

# ===================== 数据结构 =====================
@dataclass
class ImageIndex:
    by_basename: Dict[str, str]            # basename(含扩展) -> 绝对路径
    by_stem: Dict[str, List[str]]          # stem(不含扩展) -> 路径列表（可能多个扩展）
    all_files: List[str]                    # 全部图片绝对路径

# ===================== 工具函数 =====================

def setup_logging(verbose: bool) -> None:
    """配置中文日志输出。"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format="[%(levelname)s] %(message)s",
    )


def file_exists(path: str) -> bool:
    try:
        return os.path.exists(path)
    except Exception:
        return False


def ensure_dir(path: str) -> None:
    if not file_exists(path):
        os.makedirs(path, exist_ok=True)


def is_image_file(path: str, exts: Set[str]) -> bool:
    _, ext = os.path.splitext(path)
    return ext.lower() in exts


def sanitize_for_filename(s: str) -> str:
    """将字符串转换为安全的文件名片段。"""
    s = s.strip()
    s = re.sub(r"\s+", "_", s)
    s = re.sub(r"[^A-Za-z0-9_\-\.]+", "-", s)
    return s or "unknown"


def timestamp_str() -> str:
    return time.strftime("%Y%m%d-%H%M%S")


def read_csv_rows(csv_path: str) -> List[Dict[str, str]]:
    if not file_exists(csv_path):
        logging.error(f"CSV 文件不存在: {csv_path}")
        sys.exit(1)
    try:
        with open(csv_path, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            rows = [dict(row) for row in reader]
            if not rows:
                logging.warning("CSV 文件为空，未读取到任何行")
            return rows
    except UnicodeDecodeError:
        # 兼容 gbk
        with open(csv_path, "r", encoding="gbk", errors="ignore") as f:
            reader = csv.DictReader(f)
            rows = [dict(row) for row in reader]
            if not rows:
                logging.warning("CSV 文件为空（gbk），未读取到任何行")
            return rows
    except Exception as e:
        logging.error(f"读取 CSV 失败: {e}")
        sys.exit(1)


# ===================== 业务类别解析（支持一行多类别） =====================

def parse_business_categories(value: str) -> List[str]:
    """将业务列值按逗号分割为多个类别，并去除前后空格。
    支持英文逗号","、中文逗号"，"以及分隔符"、"。空项将被忽略。
    """
    v = (value or "").strip()
    if not v:
        return []
    parts = re.split(r"[，,、]+", v)
    cats = [p.strip() for p in parts if p.strip()]
    return cats


def expand_rows_by_category(rows: List[Dict[str, str]], biz_col: str) -> Dict[str, List[Dict[str, str]]]:
    """将每行按照业务类别展开到多个类别列表中。"""
    mapping: Dict[str, List[Dict[str, str]]] = {}
    for r in rows:
        cats = parse_business_categories(r.get(biz_col, ""))
        for c in cats:
            mapping.setdefault(c, []).append(r)
    return mapping


def index_images(images_dir: str, exts: Set[str]) -> ImageIndex:
    if not os.path.isdir(images_dir):
        logging.error(f"图片目录不存在: {images_dir}")
        sys.exit(1)

    by_basename: Dict[str, str] = {}
    by_stem: Dict[str, List[str]] = {}
    all_files: List[str] = []

    for root, _, files in os.walk(images_dir):
        for fn in files:
            full = os.path.join(root, fn)
            if not is_image_file(full, exts):
                continue
            all_files.append(full)
            base = os.path.basename(full).lower()
            stem, _ = os.path.splitext(base)
            if base not in by_basename:
                by_basename[base] = full
            by_stem.setdefault(stem, []).append(full)

    logging.info(f"索引到图片文件 {len(all_files)} 个（目录：{images_dir}）")
    return ImageIndex(by_basename=by_basename, by_stem=by_stem, all_files=all_files)


KNOWN_IMAGE_FIELDS = [
    "image_filename", "image_path", "filename", "file_name", "img_name", "image", "image_url"
]
KNOWN_ID_FIELDS = [
    "image_id", "img_id", "id", "uuid", "uid"
]


def basename_from_value(v: str) -> str:
    v = (v or "").strip()
    if not v:
        return ""
    # URL -> 取路径 basename
    if v.startswith("http://") or v.startswith("https://"):
        try:
            p = urlparse(v)
            v = os.path.basename(p.path)
        except Exception:
            v = os.path.basename(v)
    # 路径 -> 取 basename
    return os.path.basename(v)


def extract_candidate_names(row: Dict[str, str], image_field: Optional[str]) -> Tuple[List[str], List[str]]:
    """
    返回 (候选basename列表, 候选stem列表)
    - 优先使用 image_field 指定列
    - 否则尝试 KNOWN_IMAGE_FIELDS
    - 再尝试 KNOWN_ID_FIELDS 作为 stem
    """
    basenames: List[str] = []
    stems: List[str] = []

    if image_field and image_field in row and row[image_field]:
        base = basename_from_value(str(row[image_field]))
        if base:
            basenames.append(base)

    if not basenames:
        for col in KNOWN_IMAGE_FIELDS:
            if col in row and row[col]:
                base = basename_from_value(str(row[col]))
                if base:
                    basenames.append(base)
                    break

    for col in KNOWN_ID_FIELDS:
        if col in row and row[col]:
            stems.append(str(row[col]).strip())

    return basenames, stems


def find_image_for_row(row: Dict[str, str], biz: str, index: ImageIndex) -> Optional[str]:
    """根据行数据和业务类别，尝试在索引中找到对应图片路径。"""
    basenames, stems = extract_candidate_names(row, image_field=None)

    # 1) 先按 basename 精确匹配
    for base in basenames:
        path = index.by_basename.get(base.lower())
        if path:
            return path

    # 2) 再按 stem 精确匹配（相同 stem 可能有多个扩展，取第一个）
    for st in stems:
        paths = index.by_stem.get(st.lower())
        if paths:
            return paths[0]

    # 3) 兜底：按业务类别前缀匹配
    prefix = f"{sanitize_for_filename(biz).lower()}_"
    for path in index.all_files:
        base = os.path.basename(path).lower()
        if base.startswith(prefix):
            return path

    return None


def find_image_for_row_with_field(row: Dict[str, str], biz: str, index: ImageIndex, image_field: Optional[str]) -> Optional[str]:
    """允许传入 image_field 强制列名的版本。"""
    basenames, stems = extract_candidate_names(row, image_field=image_field)

    for base in basenames:
        path = index.by_basename.get(base.lower())
        if path:
            return path

    for st in stems:
        paths = index.by_stem.get(st.lower())
        if paths:
            return paths[0]

    prefix = f"{sanitize_for_filename(biz).lower()}_"
    for path in index.all_files:
        base = os.path.basename(path).lower()
        if base.startswith(prefix):
            return path

    return None


def select_images_for_business(rows: List[Dict[str, str]], biz: str, index: ImageIndex, need: int, image_field: Optional[str], shuffle: bool) -> List[str]:
    """从指定业务类型的行中选择图片路径列表（去重）。"""
    candidates: List[str] = []

    rows_iter = rows[:]
    if shuffle:
        random.shuffle(rows_iter)

    seen: Set[str] = set()
    for r in rows_iter:
        p = find_image_for_row_with_field(r, biz, index, image_field)
        if p and p not in seen:
            candidates.append(p)
            seen.add(p)
            if len(candidates) >= need:
                break

    if len(candidates) < need:
        logging.warning(f"业务类别 '{biz}' 目标 {need} 张，但仅找到 {len(candidates)} 张")
    else:
        logging.info(f"业务类别 '{biz}' 选取 {len(candidates)} 张")
    return candidates


def make_zip(output_dir: str, biz: str, files: List[str]) -> str:
    ensure_dir(output_dir)
    ts = timestamp_str()
    safe_biz = sanitize_for_filename(biz)
    zip_name = f"{safe_biz}_{ts}_{len(files)}images.zip"
    zip_path = os.path.join(output_dir, zip_name)

    with zipfile.ZipFile(zip_path, 'w', compression=zipfile.ZIP_DEFLATED) as zf:
        for fp in files:
            # 存储为 zip 内相对路径：业务类别/原文件名
            arcname = os.path.join(safe_biz, os.path.basename(fp))
            try:
                zf.write(fp, arcname)
            except FileNotFoundError:
                logging.warning(f"写入时未找到文件（忽略）：{fp}")
            except Exception as e:
                logging.warning(f"写入压缩包失败（忽略）：{fp}，原因：{e}")

    logging.info(f"已输出压缩包：{zip_path}")
    return zip_path






def main() -> None:
    cfg = CONFIG
    setup_logging(cfg.get("VERBOSE", False))

    csv_path = cfg["CSV_PATH"]
    images_dir = cfg["IMAGES_DIR"]
    output_dir = cfg["OUTPUT_DIR"]
    biz_col = cfg.get("BUSINESS_COLUMN", DEFAULT_BUSINESS_COLUMN)
    business_types_cfg = cfg.get("BUSINESS_TYPES", [])
    count_per_type = int(cfg.get("COUNT_PER_TYPE", DEFAULT_COUNT_PER_TYPE))
    count_map = {str(k): int(v) for k, v in cfg.get("COUNT_PER_TYPE_MAP", {}).items()}
    image_field = cfg.get("IMAGE_FIELD")
    shuffle = bool(cfg.get("SHUFFLE", False))
    strict = bool(cfg.get("STRICT", False))

    rows = read_csv_rows(csv_path)
    if not rows:
        logging.error("无可用数据行，程序结束")
        sys.exit(1)

    if biz_col not in rows[0]:
        logging.error(f"CSV 中不存在业务列 '{biz_col}'。请在 CONFIG 中设置 BUSINESS_COLUMN")
        sys.exit(1)

    # 扩展：支持一行多类别（逗号分隔） -> 将行复制到多个类别中
    biz_rows_map = expand_rows_by_category(rows, biz_col)

    # 业务类别集合
    if business_types_cfg:
        business_types = business_types_cfg
    else:
        business_types = sorted(biz_rows_map.keys())
        if not business_types:
            logging.error("CSV 中未发现任何业务类别值")
            sys.exit(1)
        logging.info(f"未在配置中指定 BUSINESS_TYPES，自动检测并处理 {len(business_types)} 个类别")

    # 图片索引
    index = index_images(images_dir, DEFAULT_IMAGE_EXTS)

    # 分业务筛选
    exit_with_error = False
    for biz in business_types:
        rows_biz = biz_rows_map.get(biz, [])
        need = count_map.get(biz, count_per_type)
        if not rows_biz:
            logging.warning(f"业务类别 '{biz}' 在 CSV 中没有对应数据行")
        selected = select_images_for_business(rows_biz, biz, index, need, image_field, shuffle)
        make_zip(output_dir, biz, selected)
        if strict and len(selected) < need:
            exit_with_error = True

    if exit_with_error:
        logging.error("严格模式下存在图片不足的类别，退出码 2")
        sys.exit(2)

    logging.info("全部处理完成")


if __name__ == "__main__":
    main()


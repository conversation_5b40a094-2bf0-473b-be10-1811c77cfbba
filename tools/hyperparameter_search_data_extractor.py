#!/usr/bin/env python3
"""
超参数搜索数据抽取工具

从完整数据集中抽取部分数据用于超参数搜索，避免数据集过大影响搜索效果。
功能：
1. 从每个类别的数据中选取指定比例的数据
2. 数据放在data/search目录中
3. 支持配置train/val分割比例
4. 生成dataset.yaml文件用于模型训练
"""

import os
import sys
import json
import random
import shutil
import yaml
import csv
from pathlib import Path
from typing import Dict, List, Tuple, Set, Optional
from collections import defaultdict
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from src.basic.logger import get_logger
from src.basic.config_manager import ConfigManager
from src.utils.file_utils import ensure_dir, get_image_files, get_label_files, copy_file

logger = get_logger(__name__)


class HyperparameterSearchDataExtractor:
    """超参数搜索数据抽取器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化抽取器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_manager = ConfigManager()
        if config_path:
            self.config_manager.load_config(config_path)
        
        # 默认配置
        self.default_config = {
            'source_dir': 'data/final',
            'output_dir': 'data/search',
            'sample_ratio': 0.2,  # 每个类别抽取20%的数据
            'split_ratios': {
                'train': 0.8,
                'val': 0.2
            },
            'random_seed': 42,
            'preserve_class_distribution': True,  # 是否保持类别分布
            'generate_statistics': True,
            'statistics_formats': ['json']
        }
        
        # 加载配置
        self.config = self.default_config.copy()
        
        # 从数据集配置文件加载超参数搜索配置
        try:
            dataset_config = self.config_manager.load_config('dataset')
            search_config = dataset_config.get('hyperparameter_search_extractor', {})
            if search_config:
                self.config.update(search_config)
                logger.info("已从配置文件加载超参数搜索配置")
            else:
                logger.info("配置文件中未找到超参数搜索配置，使用默认配置")
        except Exception as e:
            logger.warning(f"无法加载配置文件，使用默认配置: {e}")
        
        # 路径设置
        self.source_dir = Path(self.config['source_dir'])
        self.output_dir = Path(self.config['output_dir'])
        
        # 统计信息
        self.extraction_statistics = {}
        
    def extract_data_for_search(self) -> bool:
        """
        抽取数据用于超参数搜索
        
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始抽取超参数搜索数据")
            
            # 检查源数据集
            if not self._validate_source_dataset():
                return False
            
            # 准备输出目录
            self._prepare_output_directory()
            
            # 分析源数据集
            class_samples = self._analyze_source_dataset()
            if not class_samples:
                logger.error("源数据集中没有找到有效的类别数据")
                return False
            
            # 计算抽取计划
            extraction_plan = self._calculate_extraction_plan(class_samples)
            
            # 执行数据抽取
            if not self._execute_extraction(extraction_plan):
                return False
            
            # 生成dataset.yaml
            self._generate_dataset_yaml()
            
            # 生成统计报告
            self._generate_statistics_report()
            
            logger.info("超参数搜索数据抽取完成")
            return True
            
        except Exception as e:
            logger.error(f"抽取数据时发生错误: {str(e)}")
            return False
    
    def _validate_source_dataset(self) -> bool:
        """验证源数据集"""
        if not self.source_dir.exists():
            logger.error(f"源数据集目录不存在: {self.source_dir}")
            return False
        
        # 检查必要的子目录
        required_dirs = ['train', 'val']
        for dir_name in required_dirs:
            dir_path = self.source_dir / dir_name
            if not dir_path.exists():
                logger.error(f"源数据集缺少必要目录: {dir_path}")
                return False
            
            # 检查images和labels目录
            if not (dir_path / 'images').exists() or not (dir_path / 'labels').exists():
                logger.error(f"数据集目录结构不正确: {dir_path}")
                return False
        
        # 检查dataset.yaml
        dataset_yaml = self.source_dir / 'dataset.yaml'
        if not dataset_yaml.exists():
            logger.error(f"源数据集缺少dataset.yaml: {dataset_yaml}")
            return False
        
        logger.info("源数据集检查通过")
        return True
    
    def _prepare_output_directory(self):
        """准备输出目录"""
        if self.output_dir.exists():
            logger.info(f"清理现有输出目录: {self.output_dir}")
            shutil.rmtree(self.output_dir)
        
        # 创建目录结构
        for split in ['train', 'val']:
            ensure_dir(self.output_dir / split / 'images')
            ensure_dir(self.output_dir / split / 'labels')
        
        logger.info(f"输出目录已准备: {self.output_dir}")
    
    def _analyze_source_dataset(self) -> Dict[str, Dict[str, List[Tuple[Path, Path]]]]:
        """
        分析源数据集，按类别组织数据
        
        Returns:
            Dict[str, Dict[str, List[Tuple[Path, Path]]]]: 
            {class_id: {split: [(image_path, label_path), ...]}}
        """
        logger.info("分析源数据集")
        
        # 检查是否存在data_details.csv文件
        data_details_file = self.source_dir / 'data_details.csv'
        
        if data_details_file.exists():
            logger.info("发现data_details.csv文件，尝试加载...")
            class_samples = self._load_data_details(data_details_file)
            
            return class_samples
        else:
            logger.info("未发现data_details.csv文件，重新分析数据集")
            # 重新分析数据集
            logger.info("开始分析数据集...")
            class_samples = self._perform_full_analysis()
        
            # 保存分析结果到data_details.csv
            self._save_data_details(class_samples, data_details_file)
        
        return class_samples
    
    def _load_data_details(self, csv_file: Path) -> Dict[str, Dict[str, List[Tuple[Path, Path]]]]:
        """
        从CSV文件加载数据详情
        
        Args:
            csv_file: CSV文件路径
            
        Returns:
            Dict[str, Dict[str, List[Tuple[Path, Path]]]]: 类别样本数据
        """
        try:
            class_samples = defaultdict(lambda: defaultdict(list))
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    class_id = row['class_id']
                    split = row['split']
                    image_path = Path(row['image_path'])
                    label_path = Path(row['label_path'])
                    
                    class_samples[class_id][split].append((image_path, label_path))
            
            return dict(class_samples)
            
        except Exception as e:
            logger.error(f"加载data_details.csv失败: {e}")
            return {}
    
    def _validate_data_details(self, class_samples: Dict[str, Dict[str, List[Tuple[Path, Path]]]]) -> bool:
        """
        验证数据详情是否与实际文件一致
        
        Args:
            class_samples: 类别样本数据
            
        Returns:
            bool: 是否验证通过
        """
        logger.info("验证数据详情...")
        
        try:
            # 统计CSV中的文件数量
            csv_total_files = 0
            for class_id, splits in class_samples.items():
                for split, samples in splits.items():
                    csv_total_files += len(samples)
            
            # 统计实际目录中的文件数量
            actual_total_files = 0
            for split in ['train', 'val']:
                split_dir = self.source_dir / split
                if not split_dir.exists():
                    continue
                
                images_dir = split_dir / 'images'
                labels_dir = split_dir / 'labels'
                
                image_files = get_image_files(images_dir)
                label_files = get_label_files(labels_dir)
                file_pairs = self._match_files(image_files, label_files)
                actual_total_files += len(file_pairs)
            
            logger.info(f"CSV记录文件数: {csv_total_files}, 实际文件数: {actual_total_files}")
            
            # 检查数量是否一致
            if csv_total_files == actual_total_files:
                logger.info("文件数量验证通过")
                return True
            else:
                logger.warning(f"文件数量不一致: CSV={csv_total_files}, 实际={actual_total_files}")
                return False
                
        except Exception as e:
            logger.error(f"验证数据详情失败: {e}")
            return False
    
    def _perform_full_analysis(self) -> Dict[str, Dict[str, List[Tuple[Path, Path]]]]:
        """
        执行完整的数据集分析
        
        Returns:
            Dict[str, Dict[str, List[Tuple[Path, Path]]]]: 类别文件数据
        """
        class_samples = defaultdict(lambda: defaultdict(list))
        
        # 分别处理train和val目录
        for split in ['train', 'val']:
            split_dir = self.source_dir / split
            if not split_dir.exists():
                continue
            
            images_dir = split_dir / 'images'
            labels_dir = split_dir / 'labels'
            
            # 获取文件列表
            image_files = get_image_files(images_dir)
            label_files = get_label_files(labels_dir)
            
            # 匹配图像和标签文件
            file_pairs = self._match_files(image_files, label_files)
            
            logger.info(f"在 {split} 中找到 {len(file_pairs)} 个文件对")
            
            # 使用独立的进度条分析每个split
            with tqdm(file_pairs, desc=f"分析 {split} 数据") as pbar:
                for image_file, label_file in pbar:
                    classes_in_file = self._get_classes_from_label(label_file)
                    
                    if not classes_in_file:
                        # 空标签文件，归类为'empty'类别
                        class_samples['empty'][split].append((image_file, label_file))
                    else:
                        # 将文件对添加到相应的类别中
                        # 每个类别都算一个文件，即使一个文件包含多个类别
                        for class_id in classes_in_file:
                            class_samples[class_id][split].append((image_file, label_file))
        
        # 记录统计信息
        logger.info("类别文件分布统计:")
        for class_id, splits in class_samples.items():
            total_files = sum(len(files) for files in splits.values())
            logger.info(f"  类别 {class_id}: {total_files} 个文件")
            for split, files in splits.items():
                logger.info(f"    {split}: {len(files)} 个文件")
        
        return dict(class_samples)
    
    def _save_data_details(self, class_samples: Dict[str, Dict[str, List[Tuple[Path, Path]]]], csv_file: Path):
        """
        保存数据详情到CSV文件
        
        Args:
            class_samples: 类别文件数据
            csv_file: CSV文件路径
        """
        logger.info(f"保存数据详情到: {csv_file}")
        
        try:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入表头
                writer.writerow(['class_id', 'split', 'image_path', 'label_path'])
                
                # 写入数据
                total_records = 0
                for class_id, splits in class_samples.items():
                    for split, file_pairs in splits.items():
                        for image_path, label_path in file_pairs:
                            writer.writerow([class_id, split, str(image_path), str(label_path)])
                            total_records += 1
            
            logger.info(f"数据详情已保存到: {csv_file}, 总记录数: {total_records}")
            
        except Exception as e:
            logger.error(f"保存数据详情失败: {e}")
    
    def _match_files(self, image_files: List[Path], label_files: List[Path]) -> List[Tuple[Path, Path]]:
        """匹配图像和标签文件"""
        label_dict = {label_file.stem: label_file for label_file in label_files}
        
        pairs = []
        for image_file in image_files:
            if image_file.stem in label_dict:
                pairs.append((image_file, label_dict[image_file.stem]))
        
        return pairs
    
    def _get_classes_from_label(self, label_file: Path) -> Set[str]:
        """从标签文件中获取类别ID"""
        classes = set()
        
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split()
                        if len(parts) >= 5:  # YOLO格式：class_id x y w h
                            classes.add(parts[0])
        except Exception as e:
            logger.warning(f"读取标签文件失败 {label_file}: {str(e)}")
        
        return classes
    
    def _calculate_extraction_plan(self, class_samples: Dict[str, Dict[str, List[Tuple[Path, Path]]]]) -> Dict:
        """
        计算抽取计划
        
        Args:
            class_samples: 按类别组织的样本数据
            
        Returns:
            Dict: 抽取计划
        """
        logger.info("计算抽取计划")
        
        sample_ratio = self.config['sample_ratio']
        
        extraction_plan = {}
        
        for class_id, splits in class_samples.items():
            class_plan = {}
            
            for split, samples in splits.items():
                total_samples = len(samples)
                
                # 计算抽取数量
                target_count = int(total_samples * sample_ratio)
                target_count = min(target_count, total_samples)  # 不能超过实际样本数
                
                class_plan[split] = {
                    'total_samples': total_samples,
                    'target_count': target_count,
                    'samples': samples
                }
            
            extraction_plan[class_id] = class_plan
        
        # 记录计划
        logger.info("抽取计划:")
        for class_id, class_plan in extraction_plan.items():
            total_original = sum(split_info['total_samples'] for split_info in class_plan.values())
            total_target = sum(split_info['target_count'] for split_info in class_plan.values())
            logger.info(f"  类别 {class_id}: {total_original} -> {total_target} 个文件")
        
        return extraction_plan
    
    def _execute_extraction(self, extraction_plan: Dict) -> bool:
        """
        执行数据抽取
        
        Args:
            extraction_plan: 抽取计划
            
        Returns:
            bool: 是否成功
        """
        logger.info("执行数据抽取")
        
        random.seed(self.config['random_seed'])
        
        # 收集所有要抽取的文件
        all_extracted_files = defaultdict(list)  # {split: [(image_file, label_file), ...]}
        
        for class_id, class_plan in extraction_plan.items():
            for split, split_info in class_plan.items():
                samples = split_info['samples']
                target_count = split_info['target_count']
                
                if target_count > 0:
                    # 随机抽取样本
                    if len(samples) > target_count:
                        selected_samples = random.sample(samples, target_count)
                    else:
                        selected_samples = samples
                    
                    all_extracted_files[split].extend(selected_samples)
        
        # 重新分割数据（合并train和val，然后按新比例分割）
        if self.config.get('preserve_class_distribution', True):
            # 合并所有数据
            all_samples = []
            for split, samples in all_extracted_files.items():
                all_samples.extend(samples)
            
            # 按新比例分割
            split_ratios = self.config['split_ratios']
            split_data = self._split_data_by_ratio(all_samples, split_ratios)
        else:
            # 直接使用原有分割
            split_data = dict(all_extracted_files)
        
        # 复制文件
        for split, file_pairs in split_data.items():
            if not file_pairs:
                continue
            
            logger.info(f"复制 {split} 数据: {len(file_pairs)} 个文件对")
            
            for image_file, label_file in tqdm(file_pairs, desc=f"复制 {split} 数据"):
                # 目标路径
                target_image_path = self.output_dir / split / 'images' / image_file.name
                target_label_path = self.output_dir / split / 'labels' / label_file.name
                
                # 复制文件
                if not copy_file(image_file, target_image_path):
                    logger.error(f"复制图像文件失败: {image_file}")
                    return False
                
                if not copy_file(label_file, target_label_path):
                    logger.error(f"复制标签文件失败: {label_file}")
                    return False
        
        # 记录统计信息
        # 确保extraction_plan中的Path对象被转换为字符串
        serializable_extraction_plan = {}
        for class_id, class_plan in extraction_plan.items():
            serializable_class_plan = {}
            for split, split_info in class_plan.items():
                serializable_split_info = {
                    'total_samples': split_info['total_samples'],
                    'target_count': split_info['target_count'],
                    'samples': [(str(img), str(lbl)) for img, lbl in split_info['samples']]
                }
                serializable_class_plan[split] = serializable_split_info
            serializable_extraction_plan[class_id] = serializable_class_plan
        
        self.extraction_statistics = {
            'total_files_extracted': sum(len(pairs) for pairs in split_data.values()),
            'split_distribution': {split: len(pairs) for split, pairs in split_data.items()},
            'extraction_plan': serializable_extraction_plan
        }
        
        logger.info("数据抽取完成")
        return True
    
    def _split_data_by_ratio(self, file_pairs: List[Tuple[Path, Path]], split_ratios: Dict[str, float]) -> Dict[str, List[Tuple[Path, Path]]]:
        """
        按比例分割数据
        
        Args:
            file_pairs: 文件对列表
            split_ratios: 分割比例
            
        Returns:
            Dict[str, List[Tuple[Path, Path]]]: 分割后的数据
        """
        random.seed(self.config['random_seed'])
        
        # 随机打乱
        file_pairs_shuffled = file_pairs.copy()
        random.shuffle(file_pairs_shuffled)
        
        # 计算分割点
        total_count = len(file_pairs_shuffled)
        split_data = {}
        
        start_idx = 0
        for split, ratio in split_ratios.items():
            if split == list(split_ratios.keys())[-1]:  # 最后一个分割，取剩余所有数据
                end_idx = total_count
            else:
                end_idx = start_idx + int(total_count * ratio)
            
            split_data[split] = file_pairs_shuffled[start_idx:end_idx]
            start_idx = end_idx
        
        return split_data
    
    def _generate_dataset_yaml(self):
        """生成dataset.yaml文件"""
        logger.info("生成dataset.yaml文件")
        
        # 读取源数据集的yaml文件
        source_yaml_path = self.source_dir / 'dataset.yaml'
        with open(source_yaml_path, 'r', encoding='utf-8') as f:
            source_config = yaml.safe_load(f)
        
        # 创建新的配置
        yaml_config = {
            'path': str(self.output_dir.absolute()),
            'train': 'train/images',
            'val': 'val/images',
            'nc': source_config.get('nc', 0),
            'names': source_config.get('names', [])
        }
        
        # 添加额外信息
        yaml_config.update({
            'dataset_name': f"{source_config.get('dataset_name', 'Custom Dataset')} - Search Subset",
            'description': f"超参数搜索用数据子集 (抽取比例: {self.config['sample_ratio']})",
            'version': source_config.get('version', '1.0.0') + '-search',
            'created_from': str(self.source_dir),
            'sample_ratio': self.config['sample_ratio'],
            'split_ratios': self.config['split_ratios']
        })
        
        # 保存yaml文件
        yaml_path = self.output_dir / 'dataset.yaml'
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(yaml_config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"dataset.yaml已生成: {yaml_path}")
    
    def _generate_statistics_report(self):
        """生成统计报告"""
        if not self.config.get('generate_statistics', True):
            logger.info("统计报告生成已禁用")
            return
            
        logger.info("生成统计报告")
        
        # 分析抽取后的数据集
        extracted_class_stats = self._analyze_extracted_dataset()
        
        # 构建完整的统计报告
        # 转换Path对象为字符串以便JSON序列化
        serializable_config = {}
        for key, value in self.config.items():
            if isinstance(value, Path):
                serializable_config[key] = str(value)
            else:
                serializable_config[key] = value
        
        report = {
            'extraction_config': serializable_config,
            'extraction_statistics': self.extraction_statistics,
            'class_statistics': extracted_class_stats,
            'summary': {
                'total_files': self.extraction_statistics['total_files_extracted'],
                'classes_count': len(extracted_class_stats),
                'split_distribution': self.extraction_statistics['split_distribution']
            }
        }
        
        # 保存JSON报告
        report_path = self.output_dir / 'extraction_report.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        logger.info(f"统计报告已生成: {report_path}")
    
    def _analyze_extracted_dataset(self) -> Dict[str, Dict[str, int]]:
        """分析抽取后的数据集"""
        class_stats = defaultdict(lambda: defaultdict(int))
        
        for split in ['train', 'val']:
            labels_dir = self.output_dir / split / 'labels'
            if not labels_dir.exists():
                continue
            
            label_files = get_label_files(labels_dir)
            
            for label_file in label_files:
                classes_in_file = self._get_classes_from_label(label_file)
                
                if not classes_in_file:
                    # 空标签文件
                    class_stats['empty'][split] += 1
                else:
                    # 每个类别都算一个文件
                    for class_id in classes_in_file:
                        class_stats[class_id][split] += 1
        
        # 转换为普通字典
        return {class_id: dict(stats) for class_id, stats in class_stats.items()}


def main():
    """主函数"""
    print("超参数搜索数据抽取工具")
    print("=" * 50)
    
    # 创建抽取器
    extractor = HyperparameterSearchDataExtractor()
    
    # 执行抽取
    success = extractor.extract_data_for_search()
    
    if success:
        print("\n✅ 数据抽取成功完成！")
        print(f"抽取的数据保存在: {extractor.output_dir}")
        print(f"可以使用 {extractor.output_dir}/dataset.yaml 进行超参数搜索")
    else:
        print("\n❌ 数据抽取失败！")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据集整合工具
协调数据整合的完整流程：类别映射、数据合并、数据平衡、数据分割、最终数据集构建
"""

import sys
import yaml
from pathlib import Path
from typing import Dict, Optional, Any, List
from datetime import datetime
from collections import defaultdict
import shutil

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from src.basic.logger import get_logger
from src.basic.exceptions import ConfigError
from src.basic.config_manager import ConfigManager
from src.data_integration.class_mapper import ClassMapper
from src.data_integration.dataset_merger import DatasetMerger
from src.data_integration.data_balancer import DataBalancer
from src.data_integration.final_dataset_builder import FinalDatasetBuilder

logger = get_logger(__name__)


class DatasetIntegrationPipeline:
    """数据集整合流水线"""
    
    def __init__(self, config_path: str):
        """
        初始化数据集整合流水线
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.integration_config: Dict[str, Any] = {}
        
        # 组件实例
        self.class_mapper: Optional[ClassMapper] = None
        self.dataset_merger: Optional[DatasetMerger] = None
        self.data_balancer: Optional[DataBalancer] = None
        self.final_builder: Optional[FinalDatasetBuilder] = None
        
        # 运行时状态
        self.results: Dict[str, Any] = {}
        
        # 加载配置
        self._load_config()
        
    def _load_config(self):
        """加载配置文件"""
        try:
            config_manager = ConfigManager()
            self.config = config_manager.load_config('data_integration', self.config_path)
            if not isinstance(self.config, dict):
                raise ConfigError("配置必须是字典类型")
                
            self.integration_config = self.config.get('data_integration', {})
            
            if not self.integration_config:
                raise ConfigError("配置文件中未找到 data_integration 配置节")
                
            logger.info(f"配置加载成功: {self.config_path}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
            
    def run(self) -> bool:
        """
        运行完整的数据整合流水线
        
        Returns:
            bool: 是否成功
        """
        logger.info("开始执行数据集整合流水线")
        
        try:
            # 第1步：初始化组件
            self._initialize_components()
            
            # 第2步：检查需要处理的数据集
            datasets_to_process = self._get_datasets_to_process()
            
            # 如果没有需要处理的数据集，直接跳过所有处理步骤
            if not datasets_to_process:
                logger.info("没有需要处理的数据集，跳过所有处理步骤")
                # 仍然生成报告以更新状态
                self._generate_final_report()
                logger.info("数据集整合流水线执行完成（无需处理）")
                return True
            
            # 第3步：验证类别映射（只针对需要处理的数据集）
            if not self._validate_class_mapping(datasets_to_process):
                return False
                
            # 第4步：合并数据集（传入需要处理的数据集）
            if not self._merge_datasets(datasets_to_process):
                return False
                
            # 第5步：数据平衡（可选）
            if not self._balance_data():
                return False
                
            # 第6步：构建最终数据集
            if not self._build_final_dataset():
                return False
                
            # 第7步：生成报告
            self._generate_final_report()
            
            # 第8步：清理中间目录
            self._cleanup_intermediate_directories()
            
            logger.info("数据集整合流水线执行完成")
            return True
            
        except Exception as e:
            logger.error(f"数据集整合流水线执行失败: {e}")
            return False
            
    def _initialize_components(self):
        """初始化组件"""
        logger.info("初始化组件")
        
        if not isinstance(self.integration_config, dict):
            raise ConfigError("integration_config 必须是字典类型")
        
        # 初始化类别映射器
        self.class_mapper = ClassMapper(self.integration_config)
        
        # 初始化数据集合并器
        self.dataset_merger = DatasetMerger(self.integration_config, self.class_mapper)
        
        # 初始化数据平衡器
        self.data_balancer = DataBalancer(self.integration_config)
        
        # 初始化最终数据集构建器
        self.final_builder = FinalDatasetBuilder(
            self.integration_config, 
            self.class_mapper, 
            self.dataset_merger
        )
        
        logger.info("组件初始化完成")
        
    def _get_datasets_to_process(self) -> List[Dict]:
        """
        获取需要处理的数据集列表
        
        Returns:
            List[Dict]: 需要处理的数据集配置列表
        """
        import json
        
        if not isinstance(self.integration_config, dict):
            logger.error("integration_config 未正确初始化")
            return []
            
        dataset_configs = self.integration_config.get('datasets', [])
        enabled_datasets = [cfg for cfg in dataset_configs if cfg.get('enabled', True)]
        
        # 检查最终数据集的统计文件
        final_stats_file = Path("data/final/statistics.json")
        if not final_stats_file.exists():
            logger.info("未找到最终数据集统计文件，将处理所有启用的数据集")
            return enabled_datasets
            
        try:
            with open(final_stats_file, 'r', encoding='utf-8') as f:
                final_stats = json.load(f)
                
            # 获取已处理的数据集
            processed_sources = final_stats.get('dataset_sources', {})
            
            if not processed_sources:
                logger.info("统计文件中未找到已处理数据集信息，将处理所有启用的数据集")
                return enabled_datasets
                
            # 过滤已处理的数据集
            unprocessed_configs = []
            processed_datasets = []
            
            for config in enabled_datasets:
                dataset_name = config.get('name', '')
                if dataset_name in processed_sources:
                    processed_datasets.append(dataset_name)
                else:
                    unprocessed_configs.append(config)
                    
            if processed_datasets:
                logger.info(f"跳过已处理的数据集: {processed_datasets}")
                
            if unprocessed_configs:
                logger.info(f"需要处理的数据集: {[cfg.get('name', '') for cfg in unprocessed_configs]}")
            else:
                logger.info("所有数据集都已处理")
                
            return unprocessed_configs
            
        except Exception as e:
            logger.warning(f"读取最终数据集统计文件失败: {e}")
            logger.info("将处理所有启用的数据集")
            return enabled_datasets
        
    def _validate_class_mapping(self, datasets_to_validate: Optional[List[Dict]] = None) -> bool:
        """验证类别映射"""
        logger.info("验证类别映射")
        
        if not isinstance(self.integration_config, dict):
            logger.error("integration_config 未正确初始化")
            return False
            
        # 如果没有指定要验证的数据集，使用所有启用的数据集
        if datasets_to_validate is None:
            dataset_configs = self.integration_config.get('datasets', [])
            datasets_to_validate = [cfg for cfg in dataset_configs if cfg.get('enabled', True)]
        
        if not datasets_to_validate:
            logger.info("没有需要验证的数据集，跳过类别映射验证")
            return True
            
        # 验证映射关系
        if self.class_mapper is None:
            logger.error("类别映射器未初始化")
            return False
            
        success = self.class_mapper.validate_mapping(datasets_to_validate)
        
        if success:
            logger.info("类别映射验证通过")
            
            # 生成映射报告
            report_path = Path("logs/class_mapping_report.yaml")
            self.class_mapper.generate_mapping_report(report_path)
            
        return success
        
    def _merge_datasets(self, datasets_to_merge: Optional[List[Dict]] = None) -> bool:
        """合并数据集"""
        logger.info("合并数据集")
        
        if not isinstance(self.integration_config, dict):
            logger.error("integration_config 未正确初始化")
            return False
            
        # 如果没有指定要合并的数据集，使用所有启用的数据集
        if datasets_to_merge is None:
            dataset_configs = self.integration_config.get('datasets', [])
            datasets_to_merge = [cfg for cfg in dataset_configs if cfg.get('enabled', True)]
        
        if not datasets_to_merge:
            logger.info("没有需要合并的数据集，跳过合并步骤")
            return True
        
        # 执行合并
        if self.dataset_merger is None:
            logger.error("数据集合并器未初始化")
            return False
            
        success = self.dataset_merger.merge_datasets(datasets_to_merge)
        
        if success:
            # 记录合并结果
            self.results['merge_statistics'] = self.dataset_merger.get_statistics()
            self.results['file_mapping'] = self.dataset_merger.get_file_mapping()
            logger.info("数据集合并完成")
        else:
            logger.error("数据集合并失败")
            
        return success
        
    def _balance_data(self) -> bool:
        """数据平衡"""
        if not isinstance(self.integration_config, dict):
            logger.error("integration_config 未正确初始化")
            return False
            
        if not self.integration_config.get('data_balancing', {}).get('enabled', False):
            logger.info("数据平衡功能未启用，跳过")
            return True
            
        logger.info("执行数据平衡")
        
        # 获取合并后的数据集路径
        integrated_dir = Path(self.integration_config.get('dataset_merger', {}).get('output_dir', 'data/integrated'))
        
        if not integrated_dir.exists():
            logger.error(f"整合数据集目录不存在: {integrated_dir}")
            return False
            
        # 执行平衡
        if self.class_mapper is None:
            logger.error("类别映射器未初始化")
            return False
            
        if self.data_balancer is None:
            logger.error("数据平衡器未初始化")
            return False
            
        global_mapping = self.class_mapper.get_global_class_mapping()
        success = self.data_balancer.balance_dataset(integrated_dir, global_mapping)
        
        if success:
            self.results['balance_statistics'] = self.data_balancer.get_class_statistics()
            logger.info("数据平衡完成")
        else:
            logger.error("数据平衡失败")
            
        return success
        
    def _build_final_dataset(self) -> bool:
        """构建最终数据集"""
        logger.info("构建最终数据集")
        
        if not isinstance(self.integration_config, dict):
            logger.error("integration_config 未正确初始化")
            return False
            
        # 获取整合数据集路径
        integrated_dir = Path(self.integration_config.get('dataset_merger', {}).get('output_dir', 'data/integrated'))
        
        if not integrated_dir.exists():
            logger.error(f"整合数据集目录不存在: {integrated_dir}")
            return False
            
        # 构建最终数据集
        if self.final_builder is None:
            logger.error("最终数据集构建器未初始化")
            return False
            
        success = self.final_builder.build_final_dataset(integrated_dir)
        
        if success:
            self.results['final_statistics'] = self.final_builder.get_statistics()
            logger.info("最终数据集构建完成")
        else:
            logger.error("最终数据集构建失败")
            
        return success
        
    def _generate_final_report(self):
        """生成最终报告"""
        logger.info("生成最终报告")
        
        if self.class_mapper is None:
            logger.error("类别映射器未初始化")
            return
            
        # 收集所有结果
        final_report = {
            'pipeline_config': self.integration_config,
            'class_mapping': self.class_mapper.get_global_class_mapping(),
            'execution_results': self.results,
            'summary': {
                'total_global_classes': len(self.class_mapper.get_global_class_mapping()),
                'merge_success': 'merge_statistics' in self.results,
                'balance_applied': 'balance_statistics' in self.results,
                'final_dataset_created': 'final_statistics' in self.results
            }
        }
        
        # 保存报告（增量更新）
        self._save_incremental_report(final_report, "logs/dataset_integration_report.yaml")
        
        # 生成类别映射报告（增量更新）
        self._save_incremental_class_mapping_report()
        
    def _save_incremental_report(self, new_report: Dict, report_path: str):
        """增量保存报告"""
        report_file = Path(report_path)
        
        # 检查现有报告
        existing_report = {}
        if report_file.exists():
            try:
                with open(report_file, 'r', encoding='utf-8') as f:
                    existing_report = yaml.safe_load(f)
                    
                logger.info(f"找到现有报告，进行增量更新: {report_file}")
                
                # 合并执行结果
                if 'execution_results' in existing_report:
                    existing_results = existing_report['execution_results']
                    new_results = new_report['execution_results']
                    
                    # 过滤掉不需要保存的详细信息
                    filtered_new_results = self._filter_report_details(new_results)
                    
                    # 合并统计信息（排除详细信息）
                    for key in ['merge_statistics', 'balance_statistics', 'final_statistics']:
                        if key in filtered_new_results:
                            existing_results[key] = filtered_new_results[key]
                            
                    new_report['execution_results'] = existing_results
                else:
                    # 如果没有现有结果，也要过滤新结果
                    new_report['execution_results'] = self._filter_report_details(new_report['execution_results'])
                    
                # 更新其他字段
                if self.class_mapper is not None:
                    new_report['class_mapping'] = self.class_mapper.get_global_class_mapping()
                new_report['updated_time'] = datetime.now().isoformat()
                
            except Exception as e:
                logger.warning(f"读取现有报告失败，将创建新报告: {e}")
                new_report['created_time'] = datetime.now().isoformat()
                # 过滤新报告的详细信息
                new_report['execution_results'] = self._filter_report_details(new_report['execution_results'])
        else:
            new_report['created_time'] = datetime.now().isoformat()
            # 过滤新报告的详细信息
            new_report['execution_results'] = self._filter_report_details(new_report['execution_results'])
            
        # 保存报告前确保数据可序列化
        new_report = self._ensure_serializable(new_report)
        
        # 保存报告
        report_file.parent.mkdir(parents=True, exist_ok=True)
        with open(report_file, 'w', encoding='utf-8') as f:
            yaml.safe_dump(new_report, f, default_flow_style=False, allow_unicode=True)
            
        logger.info(f"报告已保存: {report_file}")
        
    def _filter_report_details(self, execution_results: Dict) -> Dict:
        """
        过滤报告中的详细信息，只保留汇总统计
        
        Args:
            execution_results: 执行结果字典
            
        Returns:
            Dict: 过滤后的执行结果
        """
        filtered_results = {}
        
        # 处理merge_statistics - 保留汇总信息，移除详细文件映射和样本跟踪
        if 'merge_statistics' in execution_results:
            merge_stats = execution_results['merge_statistics'].copy()
            
            # 移除详细的文件映射信息
            if 'file_mapping' in merge_stats:
                file_mapping = merge_stats['file_mapping']
                # 只保留文件映射的统计汇总
                merge_stats['file_mapping_summary'] = {
                    'total_files_mapped': len(file_mapping) if file_mapping else 0,
                    'mapping_completed': True if file_mapping else False
                }
                del merge_stats['file_mapping']
            
            # 移除详细的类别样本跟踪信息
            if 'class_sample_tracking' in merge_stats:
                class_tracking = merge_stats['class_sample_tracking']
                # 只保留类别样本跟踪的统计汇总
                merge_stats['class_sample_tracking_summary'] = {
                    'total_classes_tracked': len(class_tracking) if class_tracking else 0,
                    'total_samples_tracked': sum(len(samples) for samples in class_tracking.values()) if class_tracking else 0
                }
                del merge_stats['class_sample_tracking']
            
            # 移除详细的数据集类别统计信息
            if 'dataset_class_stats' in merge_stats:
                dataset_stats = merge_stats['dataset_class_stats']
                # 只保留数据集类别统计的汇总
                merge_stats['dataset_class_stats_summary'] = {
                    'total_datasets_processed': len(dataset_stats) if dataset_stats else 0,
                    'datasets_with_stats': list(dataset_stats.keys()) if dataset_stats else []
                }
                del merge_stats['dataset_class_stats']
                
            filtered_results['merge_statistics'] = merge_stats
        
        # 其他统计信息保持不变
        for key in ['balance_statistics', 'final_statistics']:
            if key in execution_results:
                filtered_results[key] = execution_results[key]
                
        return filtered_results
        
    def _save_incremental_class_mapping_report(self):
        """增量保存类别映射报告"""
        if self.class_mapper is None:
            logger.error("类别映射器未初始化，无法生成类别映射报告")
            return
            
        report_path = Path("logs/class_mapping_report.yaml")
        
        # 生成新的映射报告
        new_report = self.class_mapper.generate_mapping_report(output_path=None)
        
        # 检查现有报告
        existing_report = {}
        if report_path.exists():
            try:
                with open(report_path, 'r', encoding='utf-8') as f:
                    existing_report = yaml.safe_load(f)
                    
                logger.info(f"找到现有类别映射报告，进行增量更新: {report_path}")
                
                # 合并数据集分析
                if 'dataset_analysis' in existing_report and 'dataset_analysis' in new_report:
                    existing_analysis = existing_report['dataset_analysis']
                    new_analysis = new_report['dataset_analysis']
                    
                    # 更新每个数据集的分析结果
                    for dataset_name, analysis in new_analysis.items():
                        existing_analysis[dataset_name] = analysis
                        
                    new_report['dataset_analysis'] = existing_analysis
                    
                # 更新全局映射
                new_report['global_class_mapping'] = self.class_mapper.get_global_class_mapping()
                new_report['updated_time'] = datetime.now().isoformat()
                
            except Exception as e:
                logger.warning(f"读取现有类别映射报告失败，将创建新报告: {e}")
                new_report['created_time'] = datetime.now().isoformat()
        else:
            new_report['created_time'] = datetime.now().isoformat()
            
        # 保存报告
        report_path.parent.mkdir(parents=True, exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(new_report, f, default_flow_style=False, allow_unicode=True)
            
        logger.info(f"类别映射报告已保存: {report_path}")
            
    def get_results(self) -> Dict:
        """获取执行结果"""
        return self.results.copy()

    def _ensure_serializable(self, data) -> Any:
        """确保数据可序列化"""
        if isinstance(data, dict):
            # 处理defaultdict
            if isinstance(data, defaultdict):
                data = dict(data)
            # 递归处理字典中的每个值
            return {k: self._ensure_serializable(v) for k, v in data.items()}
        elif isinstance(data, list):
            # 递归处理列表中的每个元素
            return [self._ensure_serializable(item) for item in data]
        elif isinstance(data, defaultdict):
            # 将defaultdict转换为普通字典
            return dict(data)
        elif isinstance(data, (str, int, float, bool, type(None))):
            # 基本类型直接返回
            return data
        else:
            # 其他类型尝试转换为字符串
            try:
                return str(data)
            except:
                return f"<不可序列化对象: {type(data).__name__}>"
                
    def _cleanup_intermediate_directories(self):
        """清理中间目录"""
        if not isinstance(self.integration_config, dict):
            logger.warning("integration_config 未正确初始化，跳过清理")
            return
            
        # 获取中间目录路径
        integrated_dir = Path(self.integration_config.get('dataset_merger', {}).get('output_dir', 'data/integrated'))
        
        # 检查清理配置
        cleanup_config = self.integration_config.get('cleanup', {})
        cleanup_enabled = cleanup_config.get('enabled', True)
        cleanup_intermediate = cleanup_config.get('cleanup_intermediate', True)
        
        if not cleanup_enabled or not cleanup_intermediate:
            logger.info("中间目录清理功能未启用，跳过清理")
            return
            
        if integrated_dir.exists():
            try:
                # 创建备份（如果需要）
                if cleanup_config.get('backup_before_cleanup', False):
                    backup_dir = Path(cleanup_config.get('backup_dir', 'data/backup/cleanup'))
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    backup_path = backup_dir / f"integrated_backup_{timestamp}"
                    
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copytree(integrated_dir, backup_path)
                    logger.info(f"创建中间目录备份: {backup_path}")
                
                # 删除中间目录
                shutil.rmtree(integrated_dir)
                logger.info(f"已清理中间目录: {integrated_dir}")
                
            except Exception as e:
                logger.error(f"清理中间目录失败: {e}")
        else:
            logger.info(f"中间目录不存在，无需清理: {integrated_dir}")


def main():
    """主函数"""
    config_path = 'config/dataset_config.yaml'
    
    try:
        # 检查配置文件
        if not Path(config_path).exists():
            logger.error(f"配置文件不存在: {config_path}")
            return 1
            
        # 初始化流水线
        pipeline = DatasetIntegrationPipeline(config_path)
        
        # 执行完整流水线
        success = pipeline.run()
        
        if success:
            logger.info("数据集整合流水线执行成功")
            
            # 输出结果摘要
            results = pipeline.get_results()
            if 'final_statistics' in results:
                final_stats = results['final_statistics']
                logger.info(f"最终数据集统计:")
                logger.info(f"  总样本数: {final_stats.get('total_samples', 0)}")
                logger.info(f"  输出目录: {final_stats.get('output_directory', 'N/A')}")
                
                split_stats = final_stats.get('split_statistics', {})
                for split, count in split_stats.items():
                    logger.info(f"  {split}: {count} 个样本")
                    
            return 0
        else:
            logger.error("数据集整合流水线执行失败")
            return 1
            
    except KeyboardInterrupt:
        logger.info("用户中断执行")
        return 1
    except Exception as e:
        logger.error(f"执行失败: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main()) 
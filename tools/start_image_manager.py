#!/usr/bin/env python3
"""
图片管理Web应用启动脚本

启动图片质量管理系统的Web服务。
"""

import os
import sys
import logging
import argparse
import signal
import socket
import threading
import time
import webbrowser
from pathlib import Path

# 添加项目根目录到 Python 路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# 导入图片管理模块
from src.image_manager import create_app
from src.image_manager.config import ImageManagerConfig


def setup_logging(log_level: str = 'INFO') -> None:
    """设置日志配置
    
    Args:
        log_level: 日志级别
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=ImageManagerConfig.LOG_FORMAT,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(
                project_root / 'logs' / 'image_manager.log',
                encoding='utf-8'
            )
        ]
    )


def validate_environment() -> bool:
    """验证运行环境
    
    Returns:
        bool: 环境是否有效
    """
    logger = logging.getLogger(__name__)
    
    # 检查必要目录
    if not ImageManagerConfig.validate_directories():
        logger.error("必要的目录不存在，请检查以下目录:")
        logger.error(f"  - 数据根目录: {ImageManagerConfig.DATA_ROOT}")
        logger.error(f"  - 原始图片目录: {ImageManagerConfig.INITFILE_DIR}")
        logger.error(f"  - 标注图片目录: {ImageManagerConfig.INTEFILE_DIR}")
        return False
    
    # 检查必要文件
    if not ImageManagerConfig.validate_files():
        logger.error("必要的文件不存在，请检查以下文件:")
        logger.error(f"  - CSV记录文件: {ImageManagerConfig.QUERY_RESULTS_CSV}")
        return False
    
    # 检查图片文件数量
    initfile_count = len(list(ImageManagerConfig.INITFILE_DIR.glob("*.jpg")))
    intefile_count = len(list(ImageManagerConfig.INTEFILE_DIR.glob("*.jpg")))
    
    logger.info(f"环境检查通过:")
    logger.info(f"  - 原始图片数量: {initfile_count}")
    logger.info(f"  - 标注图片数量: {intefile_count}")
    logger.info(f"  - CSV记录文件: {ImageManagerConfig.QUERY_RESULTS_CSV}")
    
    return True


def get_local_ip() -> str:
    """获取本机IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"


def create_required_directories() -> None:
    """创建必要的目录"""
    logger = logging.getLogger(__name__)

    # 创建日志目录
    log_dir = project_root / 'logs'
    log_dir.mkdir(exist_ok=True)

    # 创建缩略图缓存目录
    thumbnail_dir = ImageManagerConfig.DATA_ROOT / "thumbnails"
    thumbnail_dir.mkdir(exist_ok=True)

    # 创建回收站目录
    trash_dir = ImageManagerConfig.DATA_ROOT / "trash"
    trash_dir.mkdir(exist_ok=True)

    # 创建多用户日志目录
    multiuser_log_dir = ImageManagerConfig.DATA_ROOT / "logs"
    multiuser_log_dir.mkdir(exist_ok=True)

    logger.info("已创建必要的目录")


def print_multiuser_info(host: str, port: int, admin_ip: str) -> None:
    """打印多用户功能信息"""
    local_ip = get_local_ip()

    print("=" * 70)
    print("🚀 多用户并发标注功能")
    print("=" * 70)

    print(f"📍 服务器信息:")
    print(f"   - 本机IP: {local_ip}")
    print(f"   - 服务端口: {port}")
    print(f"   - 本机访问: http://127.0.0.1:{port}")
    if host == "0.0.0.0":
        print(f"   - 局域网访问: http://{local_ip}:{port}")

    print(f"\n👥 多用户功能特性:")
    print(f"   - 支持最多10个用户同时标注")
    print(f"   - 每个分片包含200张图片")
    print(f"   - 管理员IP: {admin_ip}")
    print(f"   - 自动分片分配和负载均衡")
    print(f"   - 实时用户状态监控")

    print(f"\n🎯 使用说明:")
    print(f"   1. 不同设备使用不同IP访问系统")
    print(f"   2. 系统会自动为每个用户分配200张图片的分片")
    print(f"   3. 默认显示'未标记'的图片，进入页面自动全选")
    print(f"   4. 管理员可访问 /admin 页面监控用户状态")
    print(f"   5. 完成当前分片后系统自动分配下一个分片")

    print("=" * 70)


def open_browser_delayed(url: str, delay: int = 2) -> None:
    """延迟打开浏览器"""
    def _open():
        time.sleep(delay)
        webbrowser.open(url)

    thread = threading.Thread(target=_open)
    thread.daemon = True
    thread.start()


def signal_handler(signum, frame):
    """信号处理函数"""
    logger = logging.getLogger(__name__)
    logger.info("收到终止信号，正在优雅关闭...")
    sys.exit(0)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='图片质量管理系统启动脚本')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址 (默认: 0.0.0.0 支持局域网访问)')
    parser.add_argument('--port', type=int, default=8080, help='服务器端口 (默认: 8080)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    parser.add_argument('--check-only', action='store_true',
                       help='仅检查环境，不启动服务')
    parser.add_argument('--admin-ip', default='*************',
                       help='管理员IP地址 (默认: *************)')
    parser.add_argument('--no-browser', action='store_true',
                       help='不自动打开浏览器')
    parser.add_argument('--multiuser', action='store_true', default=True,
                       help='启用多用户模式 (默认启用)')

    args = parser.parse_args()

    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("=" * 60)
    logger.info("图片质量管理系统启动")
    logger.info("=" * 60)
    
    try:
        # 创建必要目录
        create_required_directories()
        
        # 验证环境
        if not validate_environment():
            logger.error("环境验证失败，请检查配置")
            sys.exit(1)
        
        if args.check_only:
            logger.info("环境检查完成，退出")
            return

        # 设置管理员IP环境变量
        os.environ['ADMIN_IP'] = args.admin_ip

        # 显示多用户功能信息
        if args.multiuser:
            print_multiuser_info(args.host, args.port, args.admin_ip)

        # 创建Flask应用
        config = {
            'host': args.host,
            'port': args.port,
            'debug': args.debug,
            'log_level': args.log_level,
            'admin_ip': args.admin_ip
        }

        app = create_app(config)

        logger.info(f"启动Web服务器:")
        logger.info(f"  - 主机: {args.host}")
        logger.info(f"  - 端口: {args.port}")
        logger.info(f"  - 调试模式: {args.debug}")
        logger.info(f"  - 管理员IP: {args.admin_ip}")
        logger.info(f"  - 访问地址: http://{args.host}:{args.port}")
        logger.info("=" * 60)

        # 自动打开浏览器
        if not args.no_browser:
            open_browser_delayed(f'http://127.0.0.1:{args.port}')

        # 启动服务器
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)
    finally:
        logger.info("图片质量管理系统已关闭")


if __name__ == '__main__':
    main()

2025-09-23 17:18:08,086 - __main__ - INFO - ============================================================
2025-09-23 17:18:08,086 - __main__ - INFO - 图片质量管理系统启动
2025-09-23 17:18:08,086 - __main__ - INFO - ============================================================
2025-09-23 17:18:08,086 - __main__ - INFO - 已创建必要的目录
2025-09-23 17:18:08,248 - __main__ - INFO - 环境检查通过:
2025-09-23 17:18:08,248 - __main__ - INFO -   - 原始图片数量: 65721
2025-09-23 17:18:08,248 - __main__ - INFO -   - 标注图片数量: 65718
2025-09-23 17:18:08,248 - __main__ - INFO -   - CSV记录文件: /home/<USER>/softwares/develop/Yolo_Utils/data/raw/backup/query_results.csv
2025-09-23 17:18:08,279 - __main__ - INFO - 启动Web服务器:
2025-09-23 17:18:08,279 - __main__ - INFO -   - 主机: 127.0.0.1
2025-09-23 17:18:08,279 - __main__ - INFO -   - 端口: 5000
2025-09-23 17:18:08,279 - __main__ - INFO -   - 调试模式: False
2025-09-23 17:18:08,279 - __main__ - INFO -   - 访问地址: http://127.0.0.1:5000
2025-09-23 17:18:08,279 - __main__ - INFO - ============================================================
 * Serving Flask app 'src.image_manager.app'
 * Debug mode: off
2025-09-23 17:18:08,281 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-09-23 17:18:08,281 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-09-23 17:18:14,281 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:14] "GET / HTTP/1.1" 200 -
2025-09-23 17:18:14,667 - DataManager - INFO - 成功加载CSV数据，共31363条记录
2025-09-23 17:18:14,671 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:14] "GET /api/statistics HTTP/1.1" 200 -
2025-09-23 17:18:14,737 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:14] "GET /api/statistics HTTP/1.1" 200 -
2025-09-23 17:18:15,773 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:15] "HEAD / HTTP/1.1" 200 -
2025-09-23 17:18:17,381 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:17] "GET /images HTTP/1.1" 200 -
2025-09-23 17:18:17,550 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:17] "GET /api/statistics HTTP/1.1" 200 -
2025-09-23 17:18:17,826 - ImageManager - INFO - 总记录数: 31363, 存在的图片数: 31342
2025-09-23 17:18:17,859 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:17] "GET /api/images?status=&page=1&page_size=20 HTTP/1.1" 200 -
2025-09-23 17:18:18,332 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:18] "HEAD /images HTTP/1.1" 200 -
2025-09-23 17:18:21,568 - ImageManager - INFO - 总记录数: 31225, 存在的图片数: 31204
2025-09-23 17:18:21,600 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:21] "GET /api/images?status=empty&page=1&page_size=20 HTTP/1.1" 200 -
2025-09-23 17:18:30,375 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:30] "GET /api/image/20250714100415_105_ShangXianWu2_1000274.jpg HTTP/1.1" 200 -
2025-09-23 17:18:50,603 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:50] "GET /api/image/20250914075105_108_JinQingXinZha1_1001325.jpg HTTP/1.1" 200 -
2025-09-23 17:18:58,331 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:18:58] "GET /api/image/20250815115910_102_ChenXiDaQiao1_1000158.jpg HTTP/1.1" 200 -
2025-09-23 17:19:01,978 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:19:01] "GET /api/image/20250726141012_104_WangJiaJing2_1000681.jpg HTTP/1.1" 200 -
2025-09-23 17:19:12,718 - DataManager - INFO - 成功保存CSV数据，共31363条记录
2025-09-23 17:19:12,718 - DataManager - INFO - 成功更新19条记录的状态为: valid
2025-09-23 17:19:12,718 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:19:12] "POST /api/images/mark_valid HTTP/1.1" 200 -
2025-09-23 17:19:12,814 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:19:12] "POST /api/images/soft_delete HTTP/1.1" 200 -
2025-09-23 17:19:12,919 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:19:12] "GET /api/statistics HTTP/1.1" 200 -
2025-09-23 17:19:13,075 - ImageManager - INFO - 总记录数: 31206, 存在的图片数: 31184
2025-09-23 17:19:13,585 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:19:13] "GET /api/images?status=empty&page=1&page_size=20 HTTP/1.1" 200 -
2025-09-23 17:42:01,839 - __main__ - INFO - ============================================================
2025-09-23 17:42:01,839 - __main__ - INFO - 图片质量管理系统启动
2025-09-23 17:42:01,839 - __main__ - INFO - ============================================================
2025-09-23 17:42:01,840 - __main__ - INFO - 已创建必要的目录
2025-09-23 17:42:01,999 - __main__ - INFO - 环境检查通过:
2025-09-23 17:42:01,999 - __main__ - INFO -   - 原始图片数量: 65720
2025-09-23 17:42:01,999 - __main__ - INFO -   - 标注图片数量: 65717
2025-09-23 17:42:01,999 - __main__ - INFO -   - CSV记录文件: /home/<USER>/softwares/develop/Yolo_Utils/data/raw/backup/query_results.csv
2025-09-23 17:42:02,029 - __main__ - INFO - 启动Web服务器:
2025-09-23 17:42:02,029 - __main__ - INFO -   - 主机: 127.0.0.1
2025-09-23 17:42:02,029 - __main__ - INFO -   - 端口: 5000
2025-09-23 17:42:02,029 - __main__ - INFO -   - 调试模式: False
2025-09-23 17:42:02,029 - __main__ - INFO -   - 访问地址: http://127.0.0.1:5000
2025-09-23 17:42:02,029 - __main__ - INFO - ============================================================
 * Serving Flask app 'src.image_manager.app'
 * Debug mode: off
2025-09-23 17:42:02,031 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-09-23 17:42:02,031 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-09-23 17:42:08,436 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:42:08] "GET /images HTTP/1.1" 200 -
2025-09-23 17:42:08,799 - DataManager - INFO - 成功加载CSV数据，共31363条记录
2025-09-23 17:42:08,803 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:42:08] "GET /api/statistics HTTP/1.1" 200 -
2025-09-23 17:42:08,830 - DataManager - INFO - 成功加载CSV数据，共31363条记录
2025-09-23 17:42:09,051 - ImageManager - INFO - 总记录数: 31363, 存在的图片数: 31341
2025-09-23 17:42:09,083 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:42:09] "GET /api/images?status=&page=1&page_size=20 HTTP/1.1" 200 -
2025-09-23 17:42:09,777 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:42:09] "HEAD /images HTTP/1.1" 200 -
2025-09-23 17:42:12,499 - ImageManager - INFO - 总记录数: 31206, 存在的图片数: 31184
2025-09-23 17:42:12,530 - werkzeug - INFO - 127.0.0.1 - - [23/Sep/2025 17:42:12] "GET /api/images?status=empty&page=1&page_size=20 HTTP/1.1" 200 -

"""
图片管理Web应用单元测试

测试图片管理系统的主要功能模块。
"""

import unittest
import tempfile
import shutil
import pandas as pd
from pathlib import Path
from unittest.mock import patch, MagicMock
import sys
import os

# 添加项目根目录到 Python 路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from src.image_manager.config import ImageManagerConfig
from src.image_manager.data_manager import DataManager
from src.image_manager.image_manager import ImageManager
from src.image_manager import create_app


class TestImageManagerConfig(unittest.TestCase):
    """测试图片管理配置类"""

    def test_config_dict(self):
        """测试配置字典获取"""
        config_dict = ImageManagerConfig.get_config_dict()

        self.assertIsInstance(config_dict, dict)
        self.assertIn('data_root', config_dict)
        self.assertIn('initfile_dir', config_dict)
        self.assertIn('intefile_dir', config_dict)
        self.assertIn('query_results_csv', config_dict)
        self.assertIn('thumbnail_size', config_dict)
        self.assertIn('default_page_size', config_dict)

    def test_status_constants(self):
        """测试状态常量"""
        self.assertEqual(ImageManagerConfig.STATUS_VALID, 'valid')
        self.assertEqual(ImageManagerConfig.STATUS_INVALID, 'invalid')
        self.assertEqual(ImageManagerConfig.STATUS_EMPTY, '')

    def test_page_size_options(self):
        """测试分页选项"""
        self.assertIn(20, ImageManagerConfig.PAGE_SIZE_OPTIONS)
        self.assertIn(30, ImageManagerConfig.PAGE_SIZE_OPTIONS)
        self.assertIn(50, ImageManagerConfig.PAGE_SIZE_OPTIONS)


class TestDataManager(unittest.TestCase):
    """测试CSV数据管理器"""

    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.csv_path = Path(self.temp_dir) / "test_query_results.csv"

        # 创建测试CSV文件
        test_data = {
            'initfile_name': ['image1.jpg', 'image2.jpg', 'image3.jpg'],
            'intefile_name': ['image1_predict.jpg', 'image2_predict.jpg', 'image3_predict.jpg'],
            'business_type': ['船只', '施工', '疑似异常藻类'],
            'event_content_pro': ['2', '7', '5'],
            'valid': ['valid', '', 'invalid']
        }
        df = pd.DataFrame(test_data)
        df.to_csv(self.csv_path, index=False)

        self.data_manager = DataManager(self.csv_path)

    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)

    def test_load_data(self):
        """测试数据加载"""
        df = self.data_manager.load_data()

        self.assertEqual(len(df), 3)
        self.assertIn('status', df.columns)
        self.assertEqual(df.iloc[0]['initfile_name'], 'image1.jpg')

    def test_get_images_by_status(self):
        """测试按状态获取图片"""
        # 测试获取所有图片
        all_images = self.data_manager.get_images_by_status('')
        self.assertEqual(len(all_images), 3)

        # 测试获取有效图片
        valid_images = self.data_manager.get_images_by_status('valid')
        self.assertEqual(len(valid_images), 1)
        self.assertEqual(valid_images[0]['initfile_name'], 'image1.jpg')

        # 测试获取未标记图片
        empty_images = self.data_manager.get_images_by_status('empty')
        self.assertEqual(len(empty_images), 1)
        self.assertEqual(empty_images[0]['initfile_name'], 'image2.jpg')

    def test_update_image_status(self):
        """测试更新图片状态"""
        # 更新单个图片状态
        updated_count = self.data_manager.update_image_status(['image2.jpg'], 'valid')
        self.assertEqual(updated_count, 1)

        # 验证更新结果
        df = self.data_manager.load_data()
        image2_row = df[df['initfile_name'] == 'image2.jpg'].iloc[0]
        self.assertEqual(image2_row['status'], 'valid')

    def test_get_image_info(self):
        """测试获取单个图片信息"""
        image_info = self.data_manager.get_image_info('image1.jpg')

        self.assertIsNotNone(image_info)
        self.assertEqual(image_info['initfile_name'], 'image1.jpg')
        self.assertEqual(image_info['business_type'], '船只')
        self.assertEqual(image_info['status'], 'valid')

        # 测试不存在的图片
        non_existent = self.data_manager.get_image_info('non_existent.jpg')
        self.assertIsNone(non_existent)

    def test_get_statistics(self):
        """测试获取统计信息"""
        stats = self.data_manager.get_statistics()

        self.assertEqual(stats['total'], 3)
        self.assertEqual(stats['valid'], 1)
        self.assertEqual(stats['invalid'], 1)
        self.assertEqual(stats['empty'], 1)

    def test_get_deleted_images(self):
        """测试获取已删除图片集合"""
        deleted_images = self.data_manager.get_deleted_images()

        self.assertIsInstance(deleted_images, set)
        self.assertIn('image3.jpg', deleted_images)
        self.assertEqual(len(deleted_images), 1)


class TestImageManager(unittest.TestCase):
    """测试图片文件管理器"""

    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.csv_path = Path(self.temp_dir) / "test_query_results.csv"

        # 创建测试目录结构
        self.initfile_dir = Path(self.temp_dir) / "initfile"
        self.intefile_dir = Path(self.temp_dir) / "intefile"
        self.initfile_dir.mkdir()
        self.intefile_dir.mkdir()

        # 创建测试CSV文件
        test_data = {
            'initfile_name': ['image1.jpg', 'image2.jpg'],
            'intefile_name': ['image1_predict.jpg', 'image2_predict.jpg'],
            'business_type': ['船只', '施工'],
            'event_content_pro': ['2', '7'],
            'valid': ['', '']
        }
        df = pd.DataFrame(test_data)
        df.to_csv(self.csv_path, index=False)

        # 创建测试图片文件（空文件）
        (self.initfile_dir / "image1.jpg").touch()
        (self.initfile_dir / "image2.jpg").touch()
        (self.intefile_dir / "image1_predict.jpg").touch()
        (self.intefile_dir / "image2_predict.jpg").touch()

        # 模拟配置
        with patch.object(ImageManagerConfig, 'DATA_ROOT', Path(self.temp_dir)):
            with patch.object(ImageManagerConfig, 'INITFILE_DIR', self.initfile_dir):
                with patch.object(ImageManagerConfig, 'INTEFILE_DIR', self.intefile_dir):
                    self.data_manager = DataManager(self.csv_path)
                    self.image_manager = ImageManager(self.data_manager)

    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)

    @patch.object(ImageManagerConfig, 'INITFILE_DIR')
    def test_get_image_list(self, mock_initfile_dir):
        """测试获取图片列表"""
        mock_initfile_dir.__truediv__ = lambda self, other: self.initfile_dir / other

        with patch.object(Path, 'exists', return_value=True):
            result = self.image_manager.get_image_list('', 1, 20)

            self.assertIn('images', result)
            self.assertIn('pagination', result)
            self.assertEqual(len(result['images']), 2)
            self.assertEqual(result['pagination']['total_count'], 2)

    def test_soft_delete_images(self):
        """测试软删除图片"""
        trash_dir = Path(self.temp_dir) / "trash"
        with patch.object(self.image_manager, 'trash_dir', trash_dir):
            # 创建回收站目录
            trash_dir.mkdir(exist_ok=True)

            result = self.image_manager.soft_delete_images(['image1.jpg'])

            self.assertEqual(result['success_count'], 1)
            self.assertEqual(result['failed_count'], 0)

    def test_get_trash_images(self):
        """测试获取回收站图片"""
        trash_dir = Path(self.temp_dir) / "trash"
        trash_dir.mkdir(exist_ok=True)

        # 创建回收站文件
        (trash_dir / "init_image1.jpg").touch()

        with patch.object(self.image_manager, 'trash_dir', trash_dir):
            result = self.image_manager.get_trash_images(1, 20)

            self.assertIn('images', result)
            self.assertIn('pagination', result)


class TestFlaskApp(unittest.TestCase):
    """测试Flask应用"""

    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()

        # 模拟配置
        with patch.object(ImageManagerConfig, 'validate_directories', return_value=True):
            with patch.object(ImageManagerConfig, 'validate_files', return_value=True):
                self.app = create_app({'TESTING': True})
                self.client = self.app.test_client()

    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)

    def test_index_route(self):
        """测试首页路由"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)

    def test_images_route(self):
        """测试原始图片页面路由"""
        response = self.client.get('/images')
        self.assertEqual(response.status_code, 200)

    def test_trash_route(self):
        """测试回收站页面路由"""
        response = self.client.get('/trash')
        self.assertEqual(response.status_code, 200)

    @patch('src.image_manager.app.ImageManager')
    @patch('src.image_manager.app.DataManager')
    def test_api_get_images(self, mock_data_manager, mock_image_manager):
        """测试获取图片列表API"""
        # 模拟返回数据
        mock_image_manager_instance = mock_image_manager.return_value
        mock_image_manager_instance.get_image_list.return_value = {
            'images': [{'initfile_name': 'test.jpg', 'thumbnail': ''}],
            'pagination': {'current_page': 1, 'total_pages': 1, 'total_count': 1}
        }
        mock_image_manager_instance.get_image_info.return_value = {
            'thumbnail': 'test_thumbnail_data'
        }

        response = self.client.get('/api/images')
        self.assertEqual(response.status_code, 200)

        data = response.get_json()
        self.assertTrue(data['success'])
        self.assertIn('data', data)

    def test_api_statistics(self):
        """测试统计信息API"""
        with patch('src.image_manager.app.DataManager') as mock_data_manager:
            mock_data_manager_instance = mock_data_manager.return_value
            mock_data_manager_instance.get_statistics.return_value = {
                'total': 100,
                'valid': 50,
                'invalid': 10,
                'empty': 40
            }

            # 重新创建应用以使用模拟的DataManager
            with patch.object(ImageManagerConfig, 'validate_directories', return_value=True):
                with patch.object(ImageManagerConfig, 'validate_files', return_value=True):
                    app = create_app({'TESTING': True})
                    client = app.test_client()

                    response = client.get('/api/statistics')
                    self.assertEqual(response.status_code, 200)

                    data = response.get_json()
                    self.assertTrue(data['success'])
                    self.assertEqual(data['data']['total'], 100)

    def test_client_log_endpoint(self):
        """    
            
        
         
        
        """
        response = self.client.post('/api/client-log', json={
            'level': 'info',
            'messages': ['unittest message'],
            'url': 'http://test/images',
            'user_agent': 'unittest',
            'timestamp': 0
        })
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertTrue(data['success'])


if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()

    # 添加测试类
    test_classes = [
        TestImageManagerConfig,
        TestDataManager,
        TestImageManager,
        TestFlaskApp
    ]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # 输出测试结果
    if result.wasSuccessful():
        print("\n所有测试通过！")
    else:
        print(f"\n测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")

"""
模型训练器主类

基于ultralytics框架的YOLO模型训练器，提供统一的训练接口。
支持固定参数训练和超参数搜索两种训练模式。
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional
import yaml
import json
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.basic.config_manager import ConfigManager
from src.basic.logger import get_logger
from src.basic.exceptions import TrainingError, ConfigurationError
from src.model_training.training_strategies.ultralytics_trainer import UltralyticsTrainer
from src.model_training.training_strategies.hyperparameter_tuner import HyperparameterTuner
from src.model_training.training_utils.model_manager import ModelManager


class YOLOTrainer:
    """YOLO模型训练器主类
    
    基于ultralytics框架的统一训练接口，支持多种训练策略。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化训练器
        
        Args:
            config_path: 配置文件路径，默认使用config/model_config.yaml
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config_manager = ConfigManager()
        
        # 设置配置路径
        if config_path is None:
            config_path = "config/model_config.yaml"
        self.config_path = Path(config_path)
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化组件
        self.ultralytics_trainer = UltralyticsTrainer()
        self.hyperparameter_tuner = HyperparameterTuner()
        self.model_manager = ModelManager()
        
        # 训练状态
        self.training_results = None
        self.current_experiment_dir = None
        
        self.logger.info("YOLOTrainer初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载训练配置
        
        Returns:
            Dict[str, Any]: 配置字典
            
        Raises:
            ConfigurationError: 配置加载失败
        """
        try:
            if not self.config_path.exists():
                raise ConfigurationError(f"配置文件不存在: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if not config:
                raise ConfigurationError("配置文件为空")
            
            self.logger.info(f"成功加载配置文件: {self.config_path}")
            return config
            
        except Exception as e:
            raise ConfigurationError(f"加载配置文件失败: {e}")
    
    def _validate_dataset(self, dataset_path: str) -> bool:
        """验证数据集有效性
        
        Args:
            dataset_path: 数据集配置文件路径
            
        Returns:
            bool: 数据集是否有效
            
        Raises:
            TrainingError: 数据集验证失败
        """
        try:
            dataset_path_obj = Path(dataset_path)
            
            # 检查数据集配置文件
            if not dataset_path_obj.exists():
                raise TrainingError(f"数据集配置文件不存在: {dataset_path_obj}")
            
            # 加载数据集配置
            with open(dataset_path_obj, 'r', encoding='utf-8') as f:
                dataset_config = yaml.safe_load(f)
            
            # 检查必需字段
            required_fields = ['path', 'train', 'val', 'nc', 'names']
            for field in required_fields:
                if field not in dataset_config:
                    raise TrainingError(f"数据集配置缺少必需字段: {field}")
            
            # 检查数据集路径
            dataset_root = Path(dataset_config['path'])
            train_path = dataset_root / dataset_config['train']
            val_path = dataset_root / dataset_config['val']
            
            if not train_path.exists():
                raise TrainingError(f"训练数据目录不存在: {train_path}")
            
            if not val_path.exists():
                raise TrainingError(f"验证数据目录不存在: {val_path}")
            
            # 检查是否有数据
            train_images = list(train_path.glob("*.jpg")) + list(train_path.glob("*.png"))
            val_images = list(val_path.glob("*.jpg")) + list(val_path.glob("*.png"))
            
            if len(train_images) == 0:
                raise TrainingError("训练集中没有图像文件")
            
            if len(val_images) == 0:
                raise TrainingError("验证集中没有图像文件")
            
            self.logger.info(f"数据集验证通过: 训练集{len(train_images)}张图像，验证集{len(val_images)}张图像")
            return True
            
        except Exception as e:
            if isinstance(e, TrainingError):
                raise
            else:
                raise TrainingError(f"数据集验证失败: {e}")
    
    def _setup_training_environment(self, mode: str) -> str:
        """设置训练环境
        
        Args:
            mode: 训练模式 (fixed_params 或 hyperparameter_search)
            
        Returns:
            str: 实验输出目录路径
        """
        try:
            # 获取训练配置
            training_config = self.config['training_modes'][mode]
            
            # 创建实验目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            experiment_name = f"{training_config.get('name', mode)}_{timestamp}"
            
            project_dir = training_config.get('project', f'runs/train_{mode}')
            experiment_dir = Path(project_dir) / experiment_name
            experiment_dir.mkdir(parents=True, exist_ok=True)
            
            self.current_experiment_dir = str(experiment_dir)
            
            # 保存配置到实验目录
            config_backup_path = experiment_dir / 'config_backup.yaml'
            with open(config_backup_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info(f"训练环境设置完成: {self.current_experiment_dir}")
            return self.current_experiment_dir
            
        except Exception as e:
            raise TrainingError(f"设置训练环境失败: {e}")
    
    def train_with_fixed_params(self) -> Dict[str, Any]:
        """使用固定超参数进行训练
        
        用于测试代码可行性和快速验证模型架构。
        
        Returns:
            Dict[str, Any]: 训练结果
            
        Raises:
            TrainingError: 训练失败
        """
        try:
            self.logger.info("开始固定参数训练模式")
            
            # 获取固定参数配置
            fixed_config = self.config['training_modes']['fixed_params']
            
            # 验证数据集
            dataset_path = self.config['dataset']['config_path']
            self._validate_dataset(dataset_path)
            
            # 设置训练环境
            experiment_dir = self._setup_training_environment('fixed_params')
            
            # 准备训练参数
            training_params = self._prepare_training_params(fixed_config, 'fixed_params')
            
            # 执行训练
            self.training_results = self.ultralytics_trainer.execute_training(
                training_params, 
                experiment_dir
            )
            
            # 保存训练结果
            self._save_training_results(self.training_results, experiment_dir)
            
            self.logger.info("固定参数训练完成")
            return self.training_results
            
        except Exception as e:
            if isinstance(e, TrainingError):
                raise
            else:
                raise TrainingError(f"固定参数训练失败: {e}")
    
    def train_with_hyperparameter_search(self) -> Dict[str, Any]:
        """使用超参数搜索进行训练
        
        用于最终模型的优化训练，自动搜索最佳超参数组合。
        支持单线程和并行搜索，通过配置文件控制。
        
        Returns:
            Dict[str, Any]: 训练结果
            
        Raises:
            TrainingError: 训练失败
        """
        try:
            self.logger.info("开始超参数搜索训练模式")
            
            # 获取超参数搜索配置
            search_config = self.config['training_modes']['hyperparameter_search']
            
            # 验证数据集 - 使用专门的搜索数据集配置
            dataset_path = self.config['dataset'].get('search_config_path', self.config['dataset']['config_path'])
            self._validate_dataset(dataset_path)
            
            # 设置训练环境
            experiment_dir = self._setup_training_environment('hyperparameter_search')
            
            # 准备搜索参数
            search_params = self._prepare_search_params(search_config)
            
            # 配置超参数搜索器（根据配置文件确定是否启用并行）
            if search_config.get('enable_parallel', False):
                # 创建支持并行搜索的超参数搜索器
                tuner_config = {
                    'enable_parallel': True,
                    'max_workers': search_config.get('max_workers', 4)
                }
                self.hyperparameter_tuner = HyperparameterTuner(tuner_config)
                self.logger.info(f"启用并行搜索，策略: {search_config.get('search_strategy', 'ultralytics')}")
            else:
                # 使用单线程搜索
                self.logger.info("使用单线程超参数搜索")
            
            # 执行超参数搜索
            self.training_results = self.hyperparameter_tuner.tune_hyperparameters(
                search_params,
                experiment_dir
            )
            
            # 保存训练结果
            self._save_training_results(self.training_results, experiment_dir)
            
            self.logger.info("超参数搜索训练完成")
            return self.training_results
            
        except Exception as e:
            if isinstance(e, TrainingError):
                raise
            else:
                raise TrainingError(f"超参数搜索训练失败: {e}")
    
    def _prepare_training_params(self, config: Dict[str, Any], mode: str) -> Dict[str, Any]:
        """准备训练参数
        
        Args:
            config: 训练配置
            mode: 训练模式
            
        Returns:
            Dict[str, Any]: ultralytics训练参数
        """
        # 基础模型配置
        model_config = self.config['model']
        environment_config = self.config['environment']
        
        # 构建训练参数
        training_params = {
            # 模型配置
            'model': f"{model_config['architecture']}{model_config['scale']}.pt",
            'data': self.config['dataset']['config_path'],
            
            # 设备配置
            'device': environment_config['device'],
            
            # 基础训练参数
            'epochs': config['epochs'],
            'batch': config['batch'],
            'imgsz': config['imgsz'],
            'workers': config['workers'],
            # 使用当前实验目录作为project，name设为空避免ultralytics再次创建子目录
            'project': self.current_experiment_dir,
            'name': '',
            
            # 优化器参数
            'optimizer': config['optimizer'],
            'lr0': config['lr0'],
            'lrf': config['lrf'],
            'momentum': config['momentum'],
            'weight_decay': config['weight_decay'],
            'warmup_epochs': config['warmup_epochs'],
            'warmup_momentum': config['warmup_momentum'],
            'warmup_bias_lr': config['warmup_bias_lr'],
            
            # 数据增强参数
            'hsv_h': config['hsv_h'],
            'hsv_s': config['hsv_s'],
            'hsv_v': config['hsv_v'],
            'degrees': config['degrees'],
            'translate': config['translate'],
            'scale': config['scale'],
            'shear': config['shear'],
            'perspective': config['perspective'],
            'flipud': config['flipud'],
            'fliplr': config['fliplr'],
            'mosaic': config['mosaic'],
            'mixup': config['mixup'],
            'copy_paste': config['copy_paste'],
            
            # 损失函数参数
            'box': config['box'],
            'cls': config['cls'],
            'dfl': config['dfl'],
            
            # 验证和保存参数
            'val': config['val'],
            'save': config['save'],
            'save_period': config['save_period'],
            'patience': config['patience'],
            
            # 其他训练参数
            'verbose': config['verbose'],
            'seed': config['seed'],
            'deterministic': config['deterministic'],
            'single_cls': config['single_cls'],
            'rect': config['rect'],
            'cos_lr': config['cos_lr'],
            'close_mosaic': config['close_mosaic'],
            'resume': config['resume'],
            'amp': config['amp'],
            'fraction': config['fraction'],
            'profile': config['profile'],
            'freeze': config['freeze']
        }
        
        # 移除None值
        training_params = {k: v for k, v in training_params.items() if v is not None}
        
        return training_params
    
    def _prepare_search_params(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """准备超参数搜索参数
        
        Args:
            config: 搜索配置
            
        Returns:
            Dict[str, Any]: ultralytics搜索参数
        """
        # 基础模型配置
        model_config = self.config['model']
        environment_config = self.config['environment']
        
        # 构建搜索参数
        search_params = {
            # 模型配置
            'model': f"{model_config['architecture']}{model_config['scale']}.pt",
            'data': self.config['dataset'].get('search_config_path', self.config['dataset']['config_path']),
            
            # 设备配置
            'device': environment_config['device'],
            
            # 基础训练参数
            'epochs': config['epochs'],
            'batch': config['batch'],
            'imgsz': config['imgsz'],
            'workers': config['workers'],
            # 使用当前实验目录作为project，name设为空避免ultralytics再次创建子目录
            'project': self.current_experiment_dir,
            'name': '',
            
            # 搜索控制参数
            'iterations': config.get('iterations', 10),  # 使用iterations而不是max_samples
            'optimizer': config.get('optimizer', 'AdamW'),
            'plots': config.get('plots', True),
            'save': config.get('save', True),
            'val': config.get('val', True),
            
            # 并行搜索参数
            'search_strategy': config.get('search_strategy', 'ultralytics'),
            'num_samples': config.get('num_samples', 20),
            # 'max_combinations': config.get('max_combinations', 50),
            'enable_parallel': config.get('enable_parallel', False),
            'max_workers': config.get('max_workers', 4),
            
            # 搜索空间
            'search_space': config.get('search_space', {})
        }
        
        # 移除None值
        search_params = {k: v for k, v in search_params.items() if v is not None}
        
        return search_params
    
    def _save_training_results(self, results: Dict[str, Any], output_dir: str) -> None:
        """保存训练结果
        
        Args:
            results: 训练结果
            output_dir: 输出目录
        """
        try:
            output_dir_path = Path(output_dir)
            
            # 保存结果JSON
            results_file = output_dir_path / 'training_results.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"训练结果已保存到: {output_dir_path}")
            
        except Exception as e:
            self.logger.error(f"保存训练结果失败: {e}")
    
    def get_latest_model_path(self, mode: str = 'fixed_params') -> Optional[str]:
        """获取最新训练的模型路径
        
        Args:
            mode: 训练模式
            
        Returns:
            Optional[str]: 模型路径，如果找不到返回None
        """
        try:
            return self.model_manager.find_latest_model(mode)
        except Exception as e:
            self.logger.error(f"获取最新模型路径失败: {e}")
            return None
    
    def get_best_model_path(self, mode: str = 'fixed_params') -> Optional[str]:
        """获取最佳训练的模型路径
        
        Args:
            mode: 训练模式
            
        Returns:
            Optional[str]: 模型路径，如果找不到返回None
        """
        try:
            return self.model_manager.find_best_model(mode)
        except Exception as e:
            self.logger.error(f"获取最佳模型路径失败: {e}")
            return None
    
    def resume_training(self, checkpoint_path: str) -> Dict[str, Any]:
        """从检查点恢复训练
        
        Args:
            checkpoint_path: 检查点文件路径
            
        Returns:
            Dict[str, Any]: 训练结果
            
        Raises:
            TrainingError: 恢复训练失败
        """
        try:
            self.logger.info(f"从检查点恢复训练: {checkpoint_path}")
            
            checkpoint_path_obj = Path(checkpoint_path)
            if not checkpoint_path_obj.exists():
                raise TrainingError(f"检查点文件不存在: {checkpoint_path_obj}")
            
            # 设置训练环境
            experiment_dir = self._setup_training_environment('resume')
            
            # 准备恢复参数
            resume_params = {
                'model': str(checkpoint_path_obj),
                'resume': True,
                'data': self.config['dataset']['config_path'],
                'device': self.config['environment']['device'],
                'project': experiment_dir,
                'name': 'resumed_training'
            }
            
            # 执行恢复训练
            self.training_results = self.ultralytics_trainer.execute_training(
                resume_params,
                experiment_dir
            )
            
            # 保存训练结果
            self._save_training_results(self.training_results, experiment_dir)
            
            self.logger.info("恢复训练完成")
            return self.training_results
            
        except Exception as e:
            if isinstance(e, TrainingError):
                raise
            else:
                raise TrainingError(f"恢复训练失败: {e}") 
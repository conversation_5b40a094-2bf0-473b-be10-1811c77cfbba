"""
ultralytics训练策略

基于ultralytics框架的YOLO模型训练策略实现。
"""

import sys
from pathlib import Path
from typing import Dict, Any
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.basic.logger import get_logger
from src.basic.exceptions import TrainingError

try:
    from ultralytics import YOLO
except ImportError:
    raise ImportError("请安装ultralytics库: pip install ultralytics")


class UltralyticsTrainer:
    """基于ultralytics的YOLO训练策略
    
    实现具体的ultralytics YOLO模型训练逻辑。
    """
    
    def __init__(self):
        """初始化训练策略"""
        self.logger = get_logger(self.__class__.__name__)
        self.model = None
        self.training_results = None
        
    def execute_training(self, training_params: Dict[str, Any], experiment_dir: str) -> Dict[str, Any]:
        """执行训练
        
        Args:
            training_params: 训练参数
            experiment_dir: 实验输出目录
            
        Returns:
            Dict[str, Any]: 训练结果
            
        Raises:
            TrainingError: 训练失败
        """
        try:
            self.logger.info("开始执行ultralytics训练")
            
            # 准备模型
            model_name = training_params.get('model', 'yolo11n.pt')
            self.model = self._prepare_model(model_name)
            
            # 配置训练参数
            train_kwargs = self._configure_training_params(training_params)
            
            # 开始训练
            self.logger.info(f"开始训练模型: {model_name}")
            self.logger.info(f"训练参数: {train_kwargs}")
            
            start_time = time.time()
            
            # 执行训练
            results = self.model.train(**train_kwargs)
            
            training_time = time.time() - start_time
            
            # 处理训练结果
            training_results = self._process_training_results(results, training_time, experiment_dir)
            
            self.training_results = training_results
            self.logger.info("ultralytics训练完成")
            
            return training_results
            
        except Exception as e:
            self.logger.error(f"ultralytics训练失败: {e}")
            raise TrainingError(f"训练执行失败: {e}")
    
    def _prepare_model(self, model_name: str) -> YOLO:
        """准备训练模型
        
        Args:
            model_name: 模型名称或路径
            
        Returns:
            YOLO: 准备好的模型实例
            
        Raises:
            TrainingError: 模型准备失败
        """
        try:
            self.logger.info(f"准备模型: {model_name}")
            
            # 检查模型文件是否存在（对于本地文件）
            model_path = Path(model_name)
            if model_path.suffix == '.pt' and not model_path.exists():
                # 如果是.pt文件但不存在，尝试使用预训练模型名称
                if not any(x in model_name for x in ['yolo11', 'yolo8', 'yolo5']):
                    raise TrainingError(f"模型文件不存在: {model_name}")
                else:
                    self.logger.info(f"使用预训练模型: {model_name}")
            
            # 创建YOLO模型实例
            model = YOLO(model_name)
            
            self.logger.info(f"模型准备完成: {model_name}")
            return model
            
        except Exception as e:
            raise TrainingError(f"模型准备失败: {e}")
    
    def _configure_training_params(self, training_params: Dict[str, Any]) -> Dict[str, Any]:
        """配置训练参数
        
        Args:
            training_params: 原始训练参数
            
        Returns:
            Dict[str, Any]: 配置好的训练参数
        """
        # 复制参数字典，避免修改原始参数
        train_kwargs = training_params.copy()
        
        # 移除不是ultralytics train方法的参数
        non_train_params = ['model']
        for param in non_train_params:
            train_kwargs.pop(param, None)
        
        # 确保必需参数存在
        if 'data' not in train_kwargs:
            raise TrainingError("缺少必需的数据集配置参数: data")
        
        # 设置默认值
        train_kwargs.setdefault('epochs', 100)
        train_kwargs.setdefault('batch', 16)
        train_kwargs.setdefault('imgsz', 640)
        train_kwargs.setdefault('workers', 8)
        train_kwargs.setdefault('device', 'auto')
        train_kwargs.setdefault('verbose', True)
        
        self.logger.debug(f"配置的训练参数: {train_kwargs}")
        return train_kwargs
    
    def _process_training_results(self, results: Any, training_time: float, experiment_dir: str) -> Dict[str, Any]:
        """处理训练结果
        
        Args:
            results: ultralytics训练结果
            training_time: 训练耗时
            experiment_dir: 实验目录
            
        Returns:
            Dict[str, Any]: 处理后的训练结果
        """
        try:
            processed_results = {
                'training_time': training_time,
                'experiment_dir': experiment_dir,
                'status': 'completed'
            }
            
            # 提取训练指标
            if hasattr(results, 'results_dict'):
                metrics = results.results_dict
                processed_results['final_metrics'] = metrics
                
                # 提取关键指标
                if 'fitness' in metrics:
                    processed_results['best_fitness'] = float(metrics['fitness'])
                
                # 提取mAP指标
                for key in ['metrics/mAP50(B)', 'metrics/mAP50-95(B)']:
                    if key in metrics:
                        metric_name = key.split('/')[-1].replace('(B)', '')
                        processed_results[f'final_{metric_name}'] = float(metrics[key])
            
            # 获取保存的模型路径
            if hasattr(results, 'save_dir'):
                save_dir = Path(results.save_dir)
                processed_results['save_dir'] = str(save_dir)
                
                # 查找最佳模型和最后模型
                best_model = save_dir / 'weights' / 'best.pt'
                last_model = save_dir / 'weights' / 'last.pt'
                
                if best_model.exists():
                    processed_results['best_model_path'] = str(best_model)
                
                if last_model.exists():
                    processed_results['last_model_path'] = str(last_model)
            
            # 训练成功标志
            processed_results['success'] = True
            
            self.logger.info(f"训练结果处理完成，耗时: {training_time:.2f}秒")
            return processed_results
            
        except Exception as e:
            self.logger.error(f"处理训练结果失败: {e}")
            return {
                'training_time': training_time,
                'experiment_dir': experiment_dir,
                'status': 'completed_with_errors',
                'success': False,
                'error': str(e)
            }
    
    def validate_model(self, model_path: str, dataset_path: str) -> Dict[str, Any]:
        """验证模型
        
        Args:
            model_path: 模型文件路径
            dataset_path: 数据集配置文件路径
            
        Returns:
            Dict[str, Any]: 验证结果
            
        Raises:
            TrainingError: 验证失败
        """
        try:
            self.logger.info(f"开始验证模型: {model_path}")
            
            # 加载模型
            model = YOLO(model_path)
            
            # 执行验证
            results = model.val(data=dataset_path)
            
            # 处理验证结果
            validation_results = {
                'model_path': model_path,
                'dataset_path': dataset_path,
                'validation_results': results.results_dict if hasattr(results, 'results_dict') else {}
            }
            
            self.logger.info("模型验证完成")
            return validation_results
            
        except Exception as e:
            raise TrainingError(f"模型验证失败: {e}")
    
    def get_model_info(self, model_path: str) -> Dict[str, Any]:
        """获取模型信息
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            Dict[str, Any]: 模型信息
            
        Raises:
            TrainingError: 获取信息失败
        """
        try:
            self.logger.info(f"获取模型信息: {model_path}")
            
            model = YOLO(model_path)
            
            model_info: Dict[str, Any] = {
                'model_path': model_path,
                'model_type': type(model).__name__,
            }
            
            # 尝试获取模型的类别名称
            try:
                if hasattr(model, 'names') and model.names:
                    model_info['class_names'] = dict(model.names)
            except Exception:
                pass
            
            self.logger.info("模型信息获取完成")
            return model_info
            
        except Exception as e:
            raise TrainingError(f"获取模型信息失败: {e}")
    
    def export_model(self, model_path: str, export_format: str = 'onnx', **kwargs) -> str:
        """导出模型
        
        Args:
            model_path: 模型文件路径
            export_format: 导出格式
            **kwargs: 其他导出参数
            
        Returns:
            str: 导出文件路径
            
        Raises:
            TrainingError: 导出失败
        """
        try:
            self.logger.info(f"导出模型: {model_path} -> {export_format}")
            
            model = YOLO(model_path)
            
            # 执行导出
            export_path = model.export(format=export_format, **kwargs)
            
            self.logger.info(f"模型导出完成: {export_path}")
            return str(export_path)
            
        except Exception as e:
            raise TrainingError(f"模型导出失败: {e}") 
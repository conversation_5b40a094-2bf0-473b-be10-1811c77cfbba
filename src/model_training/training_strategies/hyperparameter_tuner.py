"""
超参数搜索器

基于ultralytics框架的YOLO模型超参数搜索和优化。
支持单线程和多线程并行搜索。
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
import time
import json
import threading
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing as mp
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.basic.logger import get_logger
from src.basic.exceptions import TrainingError

try:
    from ultralytics import YOLO
    import pandas as pd
except ImportError:
    raise ImportError("请安装ultralytics和pandas库: pip install ultralytics pandas")


class HyperparameterTuner:
    """超参数搜索器
    
    基于ultralytics的YOLO模型超参数搜索和优化。
    支持单线程原生搜索和多线程并行搜索。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化搜索器
        
        Args:
            config: 配置参数
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config or {}
        self.model = None
        self.tuning_results = None
        
        # 并行化配置
        self.max_workers = self.config.get('max_workers', min(mp.cpu_count(), 8))
        self.enable_parallel = self.config.get('enable_parallel', False)
        
        self.logger.info(f"超参数搜索器初始化完成")
        if self.enable_parallel:
            self.logger.info(f"启用并行搜索，最大工作线程数: {self.max_workers}")
    
    def tune_hyperparameters(self, search_params: Dict[str, Any], experiment_dir: str) -> Dict[str, Any]:
        """执行超参数搜索
        
        Args:
            search_params: 搜索参数
            experiment_dir: 实验输出目录
            
        Returns:
            Dict[str, Any]: 搜索结果
            
        Raises:
            TrainingError: 搜索失败
        """
        try:
            self.logger.info("开始执行超参数搜索")
            
            # 创建实验目录
            experiment_path = Path(experiment_dir)
            experiment_path.mkdir(parents=True, exist_ok=True)
            
            # 选择搜索策略
            search_strategy = search_params.get('search_strategy', 'ultralytics')
            
            if self.enable_parallel and search_strategy in ['random', 'grid']:
                # 使用并行搜索
                if search_strategy == 'random':
                    results = self._tune_with_parallel_random_search(search_params, experiment_dir)
                elif search_strategy == 'grid':
                    results = self._tune_with_parallel_grid_search(search_params, experiment_dir)
            else:
                # 使用原生ultralytics搜索
                results = self._tune_with_ultralytics(search_params, experiment_dir)
            
            self.tuning_results = results
            self.logger.info("超参数搜索完成")
            return results
            
        except Exception as e:
            self.logger.error(f"超参数搜索失败: {e}")
            raise TrainingError(f"超参数搜索执行失败: {e}")
    
    def _tune_with_ultralytics(self, search_params: Dict[str, Any], experiment_dir: str) -> Dict[str, Any]:
        """使用原生ultralytics搜索
        
        Args:
            search_params: 搜索参数
            experiment_dir: 实验目录
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        self.logger.info("使用原生ultralytics搜索")
        
        # 准备模型
        model_name = search_params.get('model', 'yolo11n.pt')
        self.model = self._prepare_model(model_name)
        
        # 配置搜索参数
        tune_kwargs = self._configure_search_params(search_params)
        
        # 开始搜索
        self.logger.info(f"开始超参数搜索: {model_name}")
        self.logger.info(f"搜索参数: {tune_kwargs}")
        
        start_time = time.time()
        
        # 执行超参数搜索
        results = self.model.tune(**tune_kwargs)
        
        tuning_time = time.time() - start_time
        
        # 简化结果处理 - 原生ultralytics会直接生成best_hyperparameters.yaml文件
        tuning_results = {
            'search_strategy': 'ultralytics_native',
            'tuning_time': tuning_time,
            'experiment_dir': experiment_dir,
            'status': 'completed',
            'success': True
        }
        
        # 尝试获取搜索结果目录
        if results and hasattr(results, 'save_dir'):
            save_dir = Path(results.save_dir)
            tuning_results['save_dir'] = str(save_dir)
            
            # 查找生成的最佳超参数文件
            best_hyperparams_file = save_dir / 'best_hyperparameters.yaml'
            if best_hyperparams_file.exists():
                tuning_results['best_hyperparameters_file'] = str(best_hyperparams_file)
                self.logger.info(f"最佳超参数已保存至: {best_hyperparams_file}")
            
            # 查找最佳模型
            best_model = save_dir / 'weights' / 'best.pt'
            if best_model.exists():
                tuning_results['best_model_path'] = str(best_model)
        
        self.logger.info(f"超参数搜索完成，耗时: {tuning_time:.2f}秒")
        return tuning_results
    
    def _tune_with_parallel_random_search(self, search_params: Dict[str, Any], experiment_dir: str) -> Dict[str, Any]:
        """使用多线程随机搜索
        
        Args:
            search_params: 搜索参数
            experiment_dir: 实验目录
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        self.logger.info("使用多线程随机搜索")
        
        # 生成随机搜索参数组合
        num_samples = search_params.get('num_samples', 20)
        param_combinations = self._generate_random_combinations(search_params, num_samples)
        
        self.logger.info(f"生成{len(param_combinations)}个随机参数组合")
        
        # 并行执行搜索
        start_time = time.time()
        results = self._execute_parallel_search(param_combinations, search_params, experiment_dir)
        tuning_time = time.time() - start_time
        
        return {
            'search_strategy': 'parallel_random_search',
            'best_config': results['best_config'],
            'best_metrics': results['best_metrics'],
            'best_fitness': results['best_fitness'],
            'num_trials': len(param_combinations),
            'tuning_time': tuning_time,
            'experiment_dir': experiment_dir,
            'success': True,
            'all_results': results['all_results']
        }
    
    def _tune_with_parallel_grid_search(self, search_params: Dict[str, Any], experiment_dir: str) -> Dict[str, Any]:
        """使用多线程网格搜索
        
        Args:
            search_params: 搜索参数
            experiment_dir: 实验目录
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        self.logger.info("使用多线程网格搜索")
        
        # 生成网格搜索参数组合
        param_combinations = self._generate_grid_combinations(search_params)
        
        if not param_combinations:
            raise TrainingError("网格搜索参数组合为空")
        
        self.logger.info(f"生成{len(param_combinations)}个参数组合")
        
        # 限制组合数量
        # max_combinations = search_params.get('max_combinations', 50)
        # if len(param_combinations) > max_combinations:
        #     param_combinations = param_combinations[:max_combinations]
        #     self.logger.info(f"限制搜索组合数量为: {max_combinations}")
        
        # 并行执行搜索
        start_time = time.time()
        results = self._execute_parallel_search(param_combinations, search_params, experiment_dir)
        tuning_time = time.time() - start_time
        
        return {
            'search_strategy': 'parallel_grid_search',
            'best_config': results['best_config'],
            'best_metrics': results['best_metrics'],
            'best_fitness': results['best_fitness'],
            'num_trials': len(param_combinations),
            'tuning_time': tuning_time,
            'experiment_dir': experiment_dir,
            'success': True,
            'all_results': results['all_results']
        }
    
    def _generate_random_combinations(self, search_params: Dict[str, Any], num_samples: int) -> List[Dict[str, Any]]:
        """生成随机搜索参数组合
        
        Args:
            search_params: 搜索参数
            num_samples: 样本数量
            
        Returns:
            List[Dict[str, Any]]: 参数组合列表
        """
        # 定义搜索空间范围
        search_space = search_params.get('search_space', {})
        
        if not search_space:
            # 使用默认搜索空间范围
            search_space = {
                'lr0': (0.001, 0.1),
                'momentum': (0.8, 0.98),
                'weight_decay': (0.0001, 0.001),
                'box': (5.0, 15.0),
                'cls': (0.1, 1.0),
                'warmup_epochs': (1.0, 5.0),
                'hsv_h': (0.01, 0.05),
                'hsv_s': (0.5, 0.9),
                'hsv_v': (0.2, 0.6)
            }
        
        combinations = []
        for _ in range(num_samples):
            param_dict = {}
            for param_name, param_range in search_space.items():
                if isinstance(param_range, tuple) and len(param_range) == 2:
                    # 连续范围
                    min_val, max_val = param_range
                    param_dict[param_name] = random.uniform(min_val, max_val)
                elif isinstance(param_range, list):
                    # 离散选择
                    param_dict[param_name] = random.choice(param_range)
                else:
                    # 固定值
                    param_dict[param_name] = param_range
            
            combinations.append(param_dict)
        
        return combinations
    
    def _generate_grid_combinations(self, search_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成网格搜索参数组合
        
        Args:
            search_params: 搜索参数
            
        Returns:
            List[Dict[str, Any]]: 参数组合列表
        """
        # 定义搜索空间
        search_space = search_params.get('search_space', {})
        
        if not search_space:
            # 使用默认搜索空间
            search_space = {
                'lr0': [0.001, 0.01, 0.1],
                'momentum': [0.9, 0.937, 0.95],
                'weight_decay': [0.0001, 0.0005, 0.001],
                'box': [5.0, 7.5, 10.0],
                'cls': [0.3, 0.5, 0.7]
            }
        
        # 生成所有组合
        import itertools
        
        keys = list(search_space.keys())
        values = list(search_space.values())
        
        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _execute_parallel_search(self, param_combinations: List[Dict[str, Any]], 
                               search_params: Dict[str, Any], experiment_dir: str) -> Dict[str, Any]:
        """并行执行搜索
        
        Args:
            param_combinations: 参数组合列表
            search_params: 搜索参数
            experiment_dir: 实验目录
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        self.logger.info(f"开始并行搜索，工作线程数: {self.max_workers}")
        
        all_results = []
        best_fitness = -float('inf')
        best_config = None
        best_metrics = None
        
        # 使用线程池执行并行搜索
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_config = {}
            for i, config in enumerate(param_combinations):
                future = executor.submit(
                    self._train_single_config,
                    config, search_params, experiment_dir, i
                )
                future_to_config[future] = config
            
            # 收集结果
            for future in as_completed(future_to_config):
                config = future_to_config[future]
                try:
                    result = future.result()
                    all_results.append(result)
                    
                    # 更新最佳结果
                    if result['fitness'] > best_fitness:
                        best_fitness = result['fitness']
                        best_config = config
                        best_metrics = result['metrics']
                    
                    self.logger.info(f"完成配置 {result['config_id']}: 适应度 {result['fitness']:.4f}")
                    
                except Exception as e:
                    self.logger.error(f"配置训练失败: {e}")
        
        return {
            'best_config': best_config,
            'best_metrics': best_metrics,
            'best_fitness': best_fitness,
            'all_results': all_results
        }
    
    def _train_single_config(self, config: Dict[str, Any], search_params: Dict[str, Any], 
                           experiment_dir: str, config_id: int) -> Dict[str, Any]:
        """训练单个配置
        
        Args:
            config: 超参数配置
            search_params: 搜索参数
            experiment_dir: 实验目录
            config_id: 配置ID
            
        Returns:
            Dict[str, Any]: 训练结果
        """
        try:
            # 创建配置专用目录
            config_dir = Path(experiment_dir) / f"config_{config_id}"
            config_dir.mkdir(parents=True, exist_ok=True)
            
            # 准备模型
            model_name = search_params.get('model', 'yolo11n.pt')
            model = YOLO(model_name)
            
            # 准备训练参数
            train_params = {
                'data': search_params['data'],
                'epochs': search_params.get('epochs', 10),
                'project': str(config_dir),
                'name': 'train',
                'device': search_params.get('device', 'auto'),
                'verbose': False,  # 减少输出
                'save': False,     # 节省空间
                'plots': False     # 节省时间
            }
            
            # 添加超参数
            train_params.update(config)
            
            # 执行训练
            results = model.train(**train_params)
            
            # 提取适应度
            fitness = 0.0
            metrics = {}
            
            if results and hasattr(results, 'results_dict'):
                metrics = results.results_dict
                fitness = metrics.get('fitness', 0.0)
            elif results and hasattr(results, 'box'):
                # 从验证结果计算适应度
                box_map = getattr(results.box, 'map', 0.0)
                box_map50 = getattr(results.box, 'map50', 0.0)
                fitness = 0.1 * box_map + 0.9 * box_map50
                metrics = {
                    'box_map': box_map,
                    'box_map50': box_map50,
                    'fitness': fitness
                }
            
            return {
                'config_id': config_id,
                'config': config,
                'fitness': fitness,
                'metrics': metrics,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"配置 {config_id} 训练失败: {e}")
            return {
                'config_id': config_id,
                'config': config,
                'fitness': -1.0,
                'metrics': {},
                'success': False,
                'error': str(e)
            }
    
    def _prepare_model(self, model_name: str) -> YOLO:
        """准备搜索模型
        
        Args:
            model_name: 模型名称或路径
            
        Returns:
            YOLO: 准备好的模型实例
            
        Raises:
            TrainingError: 模型准备失败
        """
        try:
            self.logger.info(f"准备搜索模型: {model_name}")
            
            # 检查模型文件是否存在（对于本地文件）
            model_path = Path(model_name)
            if model_path.suffix == '.pt' and not model_path.exists():
                # 如果是.pt文件但不存在，尝试使用预训练模型名称
                if not any(x in model_name for x in ['yolo11', 'yolo8', 'yolo5']):
                    raise TrainingError(f"模型文件不存在: {model_name}")
                else:
                    self.logger.info(f"使用预训练模型: {model_name}")
            
            # 创建YOLO模型实例
            model = YOLO(model_name)
            
            self.logger.info(f"搜索模型准备完成: {model_name}")
            return model
            
        except Exception as e:
            raise TrainingError(f"搜索模型准备失败: {e}")
    
    def _configure_search_params(self, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """配置搜索参数
        
        Args:
            search_params: 原始搜索参数
            
        Returns:
            Dict[str, Any]: 配置好的搜索参数
        """
        # 复制参数字典，避免修改原始参数
        tune_kwargs = search_params.copy()
        
        # 移除不是ultralytics tune方法的参数
        non_tune_params = [
            'model', 'space', 'grace_period', 'max_samples', 'gpu_per_trial', 'search_space', 'search_strategy', 'max_workers', 'num_samples', 'enable_parallel'
        ]
        for param in non_tune_params:
            tune_kwargs.pop(param, None)
        
        # 确保必需参数存在
        if 'data' not in tune_kwargs:
            raise TrainingError("缺少必需的数据集配置参数: data")
        
        # 设置默认值 - 只使用YOLO tune方法支持的参数
        tune_kwargs.setdefault('epochs', 10)
        tune_kwargs.setdefault('iterations', 10)  # 搜索迭代次数
        tune_kwargs.setdefault('device', 'auto')
        tune_kwargs.setdefault('plots', True)
        tune_kwargs.setdefault('save', True)
        tune_kwargs.setdefault('val', True)
        
        # 移除任何可能的None值
        tune_kwargs = {k: v for k, v in tune_kwargs.items() if v is not None}
        
        self.logger.debug(f"配置的搜索参数: {tune_kwargs}")
        return tune_kwargs
    
    def _define_custom_search_space(self, search_space: Dict[str, Any]) -> Dict[str, Any]:
        """定义自定义搜索空间
        
        Args:
            search_space: 搜索空间配置
            
        Returns:
            Dict[str, Any]: 格式化的搜索空间
        """
        formatted_space = {}
        
        for param, value_range in search_space.items():
            if isinstance(value_range, list) and len(value_range) == 2:
                # 数值范围 [min, max]
                formatted_space[param] = tuple(value_range)
            elif isinstance(value_range, list):
                # 选择列表
                formatted_space[param] = value_range
            else:
                # 单一值
                formatted_space[param] = value_range
        
        return formatted_space
    
    def analyze_tuning_results(self, results_dir: str) -> Dict[str, Any]:
        """分析搜索结果
        
        Args:
            results_dir: 结果目录路径
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            results_dir_path = Path(results_dir)
            
            analysis = {
                'results_dir': str(results_dir_path),
                'analysis_time': time.time()
            }
            
            # 查找结果文件
            csv_files = list(results_dir_path.glob("**/results.csv"))
            if not csv_files:
                analysis['error'] = "未找到结果文件"
                return analysis
            
            # 读取并分析结果
            for csv_file in csv_files:
                try:
                    df = pd.read_csv(csv_file)
                    
                    # 基本统计
                    analysis[f'stats_{csv_file.parent.name}'] = {
                        'total_trials': len(df),
                        'columns': list(df.columns),
                    }
                    
                    # 最佳结果
                    if 'fitness' in df.columns:
                        best_idx = df['fitness'].idxmax()
                        analysis[f'best_trial_{csv_file.parent.name}'] = df.iloc[best_idx].to_dict()
                    
                except Exception as e:
                    self.logger.warning(f"分析文件失败 {csv_file}: {e}")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析搜索结果失败: {e}")
            return {'error': str(e)}
    
    def get_best_config_from_tuning(self, results_dir: str) -> Optional[Dict[str, Any]]:
        """从搜索结果中获取最佳配置
        
        Args:
            results_dir: 结果目录路径
            
        Returns:
            Optional[Dict[str, Any]]: 最佳配置，如果未找到返回None
        """
        try:
            results_dir_path = Path(results_dir)
            
            # 查找结果文件
            csv_files = list(results_dir_path.glob("**/results.csv"))
            if not csv_files:
                self.logger.warning("未找到搜索结果文件")
                return None
            
            best_config = None
            best_fitness = -float('inf')
            
            for csv_file in csv_files:
                try:
                    df = pd.read_csv(csv_file)
                    
                    if 'fitness' in df.columns and len(df) > 0:
                        # 找到最佳fitness的行
                        max_fitness_idx = df['fitness'].idxmax()
                        max_fitness = df.loc[max_fitness_idx, 'fitness']
                        
                        if max_fitness > best_fitness:
                            best_fitness = max_fitness
                            best_config = df.iloc[max_fitness_idx].to_dict()
                
                except Exception as e:
                    self.logger.warning(f"处理结果文件失败 {csv_file}: {e}")
            
            if best_config:
                self.logger.info(f"找到最佳配置，fitness: {best_fitness}")
                return best_config
            else:
                self.logger.warning("未找到有效的最佳配置")
                return None
                
        except Exception as e:
            self.logger.error(f"获取最佳配置失败: {e}")
            return None
    
    def save_tuning_report(self, results: Dict[str, Any], output_path: str) -> None:
        """保存搜索报告
        
        Args:
            results: 搜索结果
            output_path: 输出文件路径
        """
        try:
            output_path_obj = Path(output_path)
            output_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            # 生成报告
            report = {
                'hyperparameter_tuning_report': {
                    'summary': {
                        'status': results.get('status'),
                        'success': results.get('success'),
                        'tuning_time': results.get('tuning_time'),
                        'experiment_dir': results.get('experiment_dir')
                    },
                    'best_results': results.get('best_hyperparameters', {}),
                    'metrics': results.get('tuning_metrics', {}),
                    'model_paths': {
                        'best_model': results.get('best_model_path'),
                        'save_dir': results.get('save_dir')
                    }
                }
            }
            
            # 保存JSON报告
            with open(output_path_obj, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"搜索报告已保存: {output_path_obj}")
            
        except Exception as e:
            self.logger.error(f"保存搜索报告失败: {e}") 
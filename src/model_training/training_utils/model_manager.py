"""
模型管理器

管理训练后的YOLO模型文件，提供模型查找、加载和信息获取功能。
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import json
from datetime import datetime
import csv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.basic.logger import get_logger
from src.basic.exceptions import TrainingError


class ModelManager:
    """模型管理器
    
    管理训练后的YOLO模型文件，包括模型查找、加载和信息管理。
    """
    
    def __init__(self):
        """初始化模型管理器"""
        self.logger = get_logger(self.__class__.__name__)
        
        # 模型搜索目录配置
        self.search_dirs = [
            "runs/train",
            "runs/tune", 
            "models"
        ]
        
        # 模型文件优先级
        self.model_priority = ["best.pt", "last.pt"]
        
    def find_latest_model(self, mode: str = 'fixed_params') -> Optional[str]:
        """查找最新训练的模型
        
        Args:
            mode: 训练模式 (fixed_params 或 hyperparameter_search)
            
        Returns:
            Optional[str]: 模型文件路径，如果找不到返回None
        """
        try:
            self.logger.info(f"查找最新模型: {mode}")
            
            # 根据模式确定搜索目录
            if mode == 'fixed_params':
                search_patterns = ["runs/train", "runs/train_fixed_params"]
            elif mode == 'hyperparameter_search':
                search_patterns = ["runs/tune", "runs/train_hyperparameter_search"]
            else:
                search_patterns = ["runs/train", "runs/tune"]
            
            # 查找所有实验目录（包括嵌套目录）
            experiment_dirs = []
            for pattern in search_patterns:
                base_path = Path(pattern)
                if base_path.exists():
                    # 直接搜索基础目录
                    experiment_dirs.append(base_path)
                    # 递归搜索子目录
                    for sub_dir in base_path.rglob("*"):
                        if sub_dir.is_dir():
                            experiment_dirs.append(sub_dir)
            
            if not experiment_dirs:
                self.logger.warning(f"未找到{mode}模式的训练目录")
                return None
            
            # 按修改时间排序，最新的在前
            experiment_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 在最新的实验目录中查找模型文件
            for exp_dir in experiment_dirs:
                model_path = self._find_model_in_directory(exp_dir)
                if model_path:
                    self.logger.info(f"找到最新模型: {model_path}")
                    return str(model_path)
            
            self.logger.warning(f"未找到{mode}模式的模型文件")
            return None
            
        except Exception as e:
            self.logger.error(f"查找最新模型失败: {e}")
            return None
    
    def find_best_model(self, mode: str = 'fixed_params') -> Optional[str]:
        """查找最佳训练的模型
        
        Args:
            mode: 训练模式
            
        Returns:
            Optional[str]: 最佳模型文件路径，如果找不到返回None
        """
        try:
            self.logger.info(f"查找最佳模型: {mode}")
            
            # 根据模式确定搜索目录
            if mode == 'fixed_params':
                search_patterns = ["runs/train", "runs/train_fixed_params"]
            elif mode == 'hyperparameter_search':
                search_patterns = ["runs/tune", "runs/train_hyperparameter_search"]
            else:
                search_patterns = ["runs/train", "runs/tune"]
            
            # 查找所有实验目录（包括嵌套目录）
            experiment_dirs = []
            for pattern in search_patterns:
                base_path = Path(pattern)
                if base_path.exists():
                    # 直接搜索基础目录
                    experiment_dirs.append(base_path)
                    # 递归搜索子目录
                    for sub_dir in base_path.rglob("*"):
                        if sub_dir.is_dir():
                            experiment_dirs.append(sub_dir)
            
            if not experiment_dirs:
                self.logger.warning(f"未找到{mode}模式的训练目录")
                return None
            
            # 优先查找best.pt文件
            best_models = []
            for exp_dir in experiment_dirs:
                best_pt = exp_dir / "weights" / "best.pt"
                if best_pt.exists():
                    # 尝试获取模型的性能指标
                    metrics = self._get_model_metrics(exp_dir)
                    best_models.append((best_pt, metrics))
            
            if not best_models:
                # 如果没有best.pt，查找latest模型
                return self.find_latest_model(mode)
            
            # 根据性能指标排序（如果有的话）
            if all(metrics is not None for _, metrics in best_models):
                # 按fitness或mAP排序
                best_models.sort(
                    key=lambda x: x[1].get('fitness', x[1].get('mAP50', 0)), 
                    reverse=True
                )
            else:
                # 按修改时间排序
                best_models.sort(key=lambda x: x[0].stat().st_mtime, reverse=True)
            
            best_model_path = str(best_models[0][0])
            self.logger.info(f"找到最佳模型: {best_model_path}")
            return best_model_path
            
        except Exception as e:
            self.logger.error(f"查找最佳模型失败: {e}")
            return None
    
    def _find_model_in_directory(self, directory: Path) -> Optional[Path]:
        """在指定目录中查找模型文件
        
        Args:
            directory: 搜索目录
            
        Returns:
            Optional[Path]: 模型文件路径
        """
        try:
            # 检查weights子目录
            weights_dir = directory / "weights"
            if weights_dir.exists():
                # 按优先级查找模型文件
                for model_name in self.model_priority:
                    model_path = weights_dir / model_name
                    if model_path.exists():
                        return model_path
            
            # 直接在目录中查找.pt文件
            pt_files = list(directory.glob("*.pt"))
            if pt_files:
                # 优先返回best.pt或last.pt
                for model_name in self.model_priority:
                    for pt_file in pt_files:
                        if pt_file.name == model_name:
                            return pt_file
                # 返回第一个.pt文件
                return pt_files[0]
            
            return None
            
        except Exception as e:
            self.logger.error(f"在目录{directory}中查找模型失败: {e}")
            return None
    
    def _get_model_metrics(self, experiment_dir: Path) -> Optional[Dict[str, Any]]:
        """获取模型的性能指标
        
        Args:
            experiment_dir: 实验目录
            
        Returns:
            Optional[Dict[str, Any]]: 性能指标字典
        """
        try:
            # 查找结果文件
            results_files = [
                experiment_dir / "results.csv",
                experiment_dir / "training_results.json",
            ]
            
            for results_file in results_files:
                if results_file.exists():
                    if results_file.suffix == '.csv':
                        # 读取CSV结果文件
                        try:
                            with open(results_file, 'r', encoding='utf-8') as f:
                                reader = csv.DictReader(f)
                                rows = list(reader)
                                if rows:
                                    # 返回最后一行的指标
                                    last_row = rows[-1]
                                    metrics = {}
                                    for key, value in last_row.items():
                                        try:
                                            # 尝试转换为数值
                                            metrics[key] = float(value)
                                        except (ValueError, TypeError):
                                            metrics[key] = value
                                    return metrics
                        except Exception as e:
                            self.logger.warning(f"读取CSV文件失败 {results_file}: {e}")
                    
                    elif results_file.suffix == '.json':
                        # 读取JSON结果文件
                        try:
                            with open(results_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                if 'final_metrics' in data:
                                    return data['final_metrics']
                                elif isinstance(data, dict):
                                    return data
                        except Exception as e:
                            self.logger.warning(f"读取JSON文件失败 {results_file}: {e}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取模型指标失败: {e}")
            return None
    
    def list_all_models(self) -> List[Dict[str, Any]]:
        """列出所有可用的模型
        
        Returns:
            List[Dict[str, Any]]: 模型信息列表
        """
        try:
            models = []
            
            # 遍历搜索目录
            for search_dir in self.search_dirs:
                search_path = Path(search_dir)
                if not search_path.exists():
                    continue
                
                # 查找所有.pt文件
                pt_files = list(search_path.rglob("*.pt"))
                
                for pt_file in pt_files:
                    try:
                        model_info = {
                            'model_path': str(pt_file),
                            'model_name': pt_file.name,
                            'directory': str(pt_file.parent),
                            'size_mb': pt_file.stat().st_size / (1024 * 1024),
                            'modified_time': datetime.fromtimestamp(pt_file.stat().st_mtime).isoformat(),
                            'is_best': pt_file.name == 'best.pt',
                            'is_last': pt_file.name == 'last.pt'
                        }
                        
                        # 尝试获取实验目录的指标
                        exp_dir = pt_file.parent.parent if pt_file.parent.name == 'weights' else pt_file.parent
                        metrics = self._get_model_metrics(exp_dir)
                        if metrics:
                            model_info['metrics'] = metrics
                        
                        models.append(model_info)
                        
                    except Exception as e:
                        self.logger.warning(f"获取模型信息失败 {pt_file}: {e}")
            
            # 按修改时间排序
            models.sort(key=lambda x: x['modified_time'], reverse=True)
            
            self.logger.info(f"找到{len(models)}个模型文件")
            return models
            
        except Exception as e:
            self.logger.error(f"列出模型失败: {e}")
            return []
    
    def get_model_info(self, model_path: str) -> Dict[str, Any]:
        """获取指定模型的详细信息
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            Dict[str, Any]: 模型详细信息
        """
        try:
            model_path_obj = Path(model_path)
            
            if not model_path_obj.exists():
                raise TrainingError(f"模型文件不存在: {model_path_obj}")
            
            model_info = {
                'model_path': str(model_path_obj),
                'model_name': model_path_obj.name,
                'directory': str(model_path_obj.parent),
                'size_mb': model_path_obj.stat().st_size / (1024 * 1024),
                'modified_time': datetime.fromtimestamp(model_path_obj.stat().st_mtime).isoformat(),
                'absolute_path': str(model_path_obj.absolute())
            }
            
            # 尝试获取实验目录的指标
            exp_dir = model_path_obj.parent.parent if model_path_obj.parent.name == 'weights' else model_path_obj.parent
            metrics = self._get_model_metrics(exp_dir)
            if metrics:
                model_info['metrics'] = metrics
            
            self.logger.info(f"获取模型信息: {model_path_obj}")
            return model_info
            
        except Exception as e:
            raise TrainingError(f"获取模型信息失败: {e}")
    
    def compare_models(self, model_paths: List[str]) -> Dict[str, Any]:
        """比较多个模型的性能
        
        Args:
            model_paths: 模型文件路径列表
            
        Returns:
            Dict[str, Any]: 比较结果
        """
        try:
            comparison = {
                'models': [],
                'comparison_time': datetime.now().isoformat(),
                'total_models': len(model_paths)
            }
            
            for model_path in model_paths:
                try:
                    model_info = self.get_model_info(model_path)
                    comparison['models'].append(model_info)
                except Exception as e:
                    self.logger.warning(f"获取模型信息失败 {model_path}: {e}")
                    comparison['models'].append({
                        'model_path': model_path,
                        'error': str(e)
                    })
            
            # 生成比较统计
            valid_models = [m for m in comparison['models'] if 'error' not in m and 'metrics' in m]
            
            if valid_models:
                metrics_keys = set()
                for model in valid_models:
                    if 'metrics' in model:
                        metrics_keys.update(model['metrics'].keys())
                
                # 为每个指标找到最佳模型
                best_models = {}
                for metric in metrics_keys:
                    try:
                        metric_values = []
                        for model in valid_models:
                            if 'metrics' in model and metric in model['metrics']:
                                value = model['metrics'][metric]
                                if isinstance(value, (int, float)):
                                    metric_values.append((value, model['model_path']))
                        
                        if metric_values:
                            # 大多数指标越大越好
                            best_value, best_model = max(metric_values)
                            best_models[metric] = {
                                'model_path': best_model,
                                'value': best_value
                            }
                    except Exception as e:
                        self.logger.warning(f"比较指标失败 {metric}: {e}")
                
                comparison['best_models_by_metric'] = best_models
            
            self.logger.info(f"模型比较完成，共{len(model_paths)}个模型")
            return comparison
            
        except Exception as e:
            self.logger.error(f"模型比较失败: {e}")
            return {'error': str(e)}
    
    def cleanup_old_models(self, keep_count: int = 10) -> List[str]:
        """清理旧的模型文件
        
        Args:
            keep_count: 保留的模型数量
            
        Returns:
            List[str]: 被删除的模型路径列表
        """
        try:
            deleted_models = []
            
            # 获取所有模型
            all_models = self.list_all_models()
            
            # 按目录分组
            models_by_dir = {}
            for model in all_models:
                directory = model['directory']
                if directory not in models_by_dir:
                    models_by_dir[directory] = []
                models_by_dir[directory].append(model)
            
            # 在每个目录中清理旧模型
            for directory, models in models_by_dir.items():
                # 排除best.pt和last.pt
                cleanable_models = [m for m in models if not (m['is_best'] or m['is_last'])]
                
                # 按修改时间排序，保留最新的
                cleanable_models.sort(key=lambda x: x['modified_time'], reverse=True)
                
                # 删除超出保留数量的模型
                for model in cleanable_models[keep_count:]:
                    try:
                        model_path = Path(model['model_path'])
                        if model_path.exists():
                            model_path.unlink()
                            deleted_models.append(str(model_path))
                            self.logger.info(f"删除旧模型: {model_path}")
                    except Exception as e:
                        self.logger.warning(f"删除模型失败 {model['model_path']}: {e}")
            
            self.logger.info(f"清理完成，删除了{len(deleted_models)}个旧模型")
            return deleted_models
            
        except Exception as e:
            self.logger.error(f"清理旧模型失败: {e}")
            return []
    
    def export_model_inventory(self, output_path: str) -> None:
        """导出模型清单
        
        Args:
            output_path: 输出文件路径
        """
        try:
            output_path_obj = Path(output_path)
            output_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            # 获取所有模型信息
            models = self.list_all_models()
            
            # 生成清单
            inventory = {
                'export_time': datetime.now().isoformat(),
                'total_models': len(models),
                'models': models,
                'summary': {
                    'total_size_mb': sum(m.get('size_mb', 0) for m in models),
                    'best_models': len([m for m in models if m.get('is_best', False)]),
                    'last_models': len([m for m in models if m.get('is_last', False)])
                }
            }
            
            # 保存清单
            with open(output_path_obj, 'w', encoding='utf-8') as f:
                json.dump(inventory, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"模型清单已导出: {output_path_obj}")
            
        except Exception as e:
            self.logger.error(f"导出模型清单失败: {e}") 
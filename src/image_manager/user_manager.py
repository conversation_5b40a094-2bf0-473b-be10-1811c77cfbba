"""
用户管理和分片分配模块

负责管理多用户会话、分片分配和用户权限控制。
"""

import time
import logging
import threading
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from .config import ImageManagerConfig


class UserManager:
    """用户管理器
    
    负责用户识别、分片分配、会话管理等功能。
    """
    
    # 管理员IP地址（从配置中读取）
    @property
    def ADMIN_IP(self):
        return ImageManagerConfig.ADMIN_IP
    
    # 分片配置
    CHUNK_SIZE = 200  # 每个分片200张图片
    SESSION_TIMEOUT = 300  # 会话超时时间（秒）
    
    def __init__(self):
        """初始化用户管理器"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._lock = threading.Lock()
        
        # 用户会话信息 {user_ip: session_info}
        self._user_sessions = {}
        
        # 分片分配信息 {chunk_id: user_ip}
        self._chunk_assignments = {}
        
        # 已分配的分片集合
        self._allocated_chunks = set()
        
        self.logger.info("用户管理器初始化完成")
    
    def get_user_id(self, request) -> str:
        """从请求中获取用户ID（基于IP地址）

        Args:
            request: Flask请求对象

        Returns:
            str: 用户IP地址
        """
        # 优先获取真实IP（考虑代理情况）
        user_ip = request.headers.get('X-Forwarded-For', '').split(',')[0].strip()
        if not user_ip:
            user_ip = request.headers.get('X-Real-IP', '')
        if not user_ip:
            user_ip = request.remote_addr

        final_ip = user_ip or 'unknown'

        # 添加调试日志
        if request.endpoint in ['/api/user/info', '/admin']:
            self.logger.info(f"获取用户IP: {final_ip}, 是否管理员: {self.is_admin(final_ip)}")

        return final_ip
    
    def is_admin(self, user_ip: str) -> bool:
        """检查用户是否为管理员
        
        Args:
            user_ip: 用户IP地址
            
        Returns:
            bool: 是否为管理员
        """
        return user_ip == self.ADMIN_IP
    
    def update_user_activity(self, user_ip: str) -> None:
        """更新用户活跃状态
        
        Args:
            user_ip: 用户IP地址
        """
        with self._lock:
            current_time = time.time()
            
            if user_ip not in self._user_sessions:
                # 新用户，创建会话
                self._user_sessions[user_ip] = {
                    'ip': user_ip,
                    'login_time': current_time,
                    'last_activity': current_time,
                    'is_admin': self.is_admin(user_ip),
                    'assigned_chunks': [],
                    'annotated_count': 0
                }
                self.logger.info(f"新用户会话创建: {user_ip}")
            else:
                # 更新活跃时间
                self._user_sessions[user_ip]['last_activity'] = current_time
    
    def cleanup_expired_sessions(self) -> None:
        """清理过期的用户会话"""
        with self._lock:
            self._cleanup_expired_sessions_internal()

    def _cleanup_expired_sessions_internal(self) -> None:
        """清理过期的用户会话（内部方法，需要在锁内调用）"""
        current_time = time.time()
        expired_users = []

        for user_ip, session in self._user_sessions.items():
            if current_time - session['last_activity'] > self.SESSION_TIMEOUT:
                expired_users.append(user_ip)

        for user_ip in expired_users:
            self._release_user_chunks(user_ip)
            del self._user_sessions[user_ip]
            self.logger.info(f"用户会话过期，已清理: {user_ip}")
    
    def _release_user_chunks(self, user_ip: str) -> None:
        """释放用户的分片分配（内部方法，需要在锁内调用）
        
        Args:
            user_ip: 用户IP地址
        """
        if user_ip in self._user_sessions:
            assigned_chunks = self._user_sessions[user_ip].get('assigned_chunks', [])
            for chunk_id in assigned_chunks:
                if chunk_id in self._chunk_assignments:
                    del self._chunk_assignments[chunk_id]
                self._allocated_chunks.discard(chunk_id)
            
            self._user_sessions[user_ip]['assigned_chunks'] = []
            self.logger.info(f"释放用户分片: {user_ip}, 分片数量: {len(assigned_chunks)}")
    
    def get_user_chunk_range(self, user_ip: str, total_images: int) -> Optional[Tuple[int, int]]:
        """获取用户当前分片的图片范围

        Args:
            user_ip: 用户IP地址
            total_images: 图片总数

        Returns:
            Optional[Tuple[int, int]]: (start_index, end_index) 或 None
        """
        try:
            with self._lock:
                self._cleanup_expired_sessions_internal()

                # 直接在锁内更新用户活跃状态，避免重复获取锁
                current_time = time.time()
                if user_ip not in self._user_sessions:
                    # 新用户，创建会话
                    self._user_sessions[user_ip] = {
                        'ip': user_ip,
                        'login_time': current_time,
                        'last_activity': current_time,
                        'is_admin': self.is_admin(user_ip),
                        'assigned_chunks': [],
                        'annotated_count': 0
                    }
                    self.logger.info(f"新用户会话创建: {user_ip}")
                else:
                    # 更新活跃时间
                    self._user_sessions[user_ip]['last_activity'] = current_time

                session = self._user_sessions.get(user_ip)
                if not session:
                    self.logger.warning(f"用户会话不存在: {user_ip}")
                    return None

                # 检查是否有已分配的分片
                assigned_chunks = session.get('assigned_chunks', [])
                if assigned_chunks:
                    # 返回第一个分片的范围
                    chunk_id = assigned_chunks[0]
                    start_index = chunk_id * self.CHUNK_SIZE
                    end_index = min(start_index + self.CHUNK_SIZE, total_images)
                    self.logger.debug(f"用户 {user_ip} 使用现有分片 {chunk_id}: {start_index}-{end_index}")
                    return (start_index, end_index)

                # 分配新的分片
                chunk_id = self._allocate_next_chunk(user_ip, total_images)
                if chunk_id is not None:
                    start_index = chunk_id * self.CHUNK_SIZE
                    end_index = min(start_index + self.CHUNK_SIZE, total_images)
                    self.logger.info(f"为用户 {user_ip} 分配新分片 {chunk_id}: {start_index}-{end_index}")
                    return (start_index, end_index)

                self.logger.warning(f"无法为用户 {user_ip} 分配分片")
                return None
        except Exception as e:
            self.logger.error(f"获取用户分片范围失败: {user_ip}, 错误: {e}")
            return None
    
    def _allocate_next_chunk(self, user_ip: str, total_images: int) -> Optional[int]:
        """为用户分配下一个可用分片（内部方法，需要在锁内调用）
        
        Args:
            user_ip: 用户IP地址
            total_images: 图片总数
            
        Returns:
            Optional[int]: 分片ID，如果没有可用分片则返回None
        """
        total_chunks = (total_images + self.CHUNK_SIZE - 1) // self.CHUNK_SIZE
        
        # 寻找第一个未分配的分片
        for chunk_id in range(total_chunks):
            if chunk_id not in self._allocated_chunks:
                # 分配分片
                self._allocated_chunks.add(chunk_id)
                self._chunk_assignments[chunk_id] = user_ip
                
                # 更新用户会话信息
                if user_ip in self._user_sessions:
                    self._user_sessions[user_ip]['assigned_chunks'].append(chunk_id)
                
                self.logger.info(f"为用户 {user_ip} 分配分片 {chunk_id}")
                return chunk_id
        
        self.logger.warning(f"没有可用分片分配给用户: {user_ip}")
        return None
    
    def complete_current_chunk(self, user_ip: str, total_images: int) -> Optional[Tuple[int, int]]:
        """完成当前分片，分配下一个分片
        
        Args:
            user_ip: 用户IP地址
            total_images: 图片总数
            
        Returns:
            Optional[Tuple[int, int]]: 新分片的范围 (start_index, end_index) 或 None
        """
        with self._lock:
            session = self._user_sessions.get(user_ip)
            if not session:
                return None
            
            # 移除当前分片
            assigned_chunks = session.get('assigned_chunks', [])
            if assigned_chunks:
                completed_chunk = assigned_chunks.pop(0)
                if completed_chunk in self._chunk_assignments:
                    del self._chunk_assignments[completed_chunk]
                # 注意：不从_allocated_chunks中移除，表示该分片已完成
                
                self.logger.info(f"用户 {user_ip} 完成分片 {completed_chunk}")
            
            # 分配下一个分片
            chunk_id = self._allocate_next_chunk(user_ip, total_images)
            if chunk_id is not None:
                start_index = chunk_id * self.CHUNK_SIZE
                end_index = min(start_index + self.CHUNK_SIZE, total_images)
                return (start_index, end_index)
            
            return None
    
    def get_all_users_status(self) -> List[Dict]:
        """获取所有用户状态信息（管理员功能）
        
        Returns:
            List[Dict]: 用户状态列表
        """
        with self._lock:
            self._cleanup_expired_sessions_internal()
            
            users_status = []
            current_time = time.time()
            
            for user_ip, session in self._user_sessions.items():
                is_online = (current_time - session['last_activity']) < 60  # 1分钟内活跃视为在线
                
                users_status.append({
                    'ip': user_ip,
                    'is_admin': session['is_admin'],
                    'is_online': is_online,
                    'login_time': session['login_time'],
                    'last_activity': session['last_activity'],
                    'assigned_chunks': session['assigned_chunks'],
                    'annotated_count': session['annotated_count']
                })
            
            return users_status
    
    def get_user_info(self, user_ip: str) -> Optional[Dict]:
        """获取指定用户信息
        
        Args:
            user_ip: 用户IP地址
            
        Returns:
            Optional[Dict]: 用户信息
        """
        with self._lock:
            self._cleanup_expired_sessions_internal()

            # 直接在锁内更新用户活跃状态
            current_time = time.time()
            if user_ip not in self._user_sessions:
                # 新用户，创建会话
                self._user_sessions[user_ip] = {
                    'ip': user_ip,
                    'login_time': current_time,
                    'last_activity': current_time,
                    'is_admin': self.is_admin(user_ip),
                    'assigned_chunks': [],
                    'annotated_count': 0
                }
                self.logger.info(f"新用户会话创建: {user_ip}")
            else:
                # 更新活跃时间
                self._user_sessions[user_ip]['last_activity'] = current_time

            session = self._user_sessions.get(user_ip)
            if not session:
                return None

            is_online = (current_time - session['last_activity']) < 60

            return {
                'ip': user_ip,
                'is_admin': session['is_admin'],
                'is_online': is_online,
                'login_time': session['login_time'],
                'last_activity': session['last_activity'],
                'assigned_chunks': session['assigned_chunks'],
                'annotated_count': session['annotated_count']
            }
    
    def increment_user_annotation_count(self, user_ip: str, count: int = 1) -> None:
        """增加用户标注计数
        
        Args:
            user_ip: 用户IP地址
            count: 增加的数量
        """
        with self._lock:
            if user_ip in self._user_sessions:
                self._user_sessions[user_ip]['annotated_count'] += count
                self.logger.debug(f"用户 {user_ip} 标注计数增加 {count}")
    
    def force_release_user_chunks(self, user_ip: str) -> bool:
        """强制释放用户分片（管理员功能）
        
        Args:
            user_ip: 用户IP地址
            
        Returns:
            bool: 是否成功释放
        """
        with self._lock:
            if user_ip in self._user_sessions:
                self._release_user_chunks(user_ip)
                self.logger.info(f"管理员强制释放用户分片: {user_ip}")
                return True
            return False

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}图片质量管理系统{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- 控制台保护与日志上报（调试辅助） -->
    <script>
        // 说明：拦截 console.clear 并将浏览器端日志转发到后端，便于在服务器控制台查看
        (function() {
            try {
                const original = Object.assign({}, console);
                const levels = ['log', 'info', 'warn', 'error', 'debug'];

                // 拦截清空控制台的行为
                console.clear = function() {
                    try {
                        if (original && original.warn) {
                            original.warn('已拦截 console.clear() 调用，调试模式下不清空控制台');
                        }
                    } catch (e) {}
                };

                function sanitizeString(str) {
                    try {
                        // 尝试当作JSON解析并按键名过滤大字段
                        const obj = JSON.parse(str);
                        return JSON.stringify(obj, function (key, value) {
                            if (typeof value === 'string') {
                                const k = (key || '').toLowerCase();
                                if (k.includes('thumbnail')) {
                                    return `[thumbnail_omitted(len=${value.length})]`;
                                }
                                if (value.startsWith('data:image')) {
                                    return `data:image;base64,[omitted(len=${value.length})]`;
                                }
                                if (value.length > 20000) {
                                    return `[omitted(len=${value.length})]`;
                                }
                            }
                            return value;
                        });
                    } catch (e) {
                        // 非JSON字符串：脱敏 data:image;base64 内容，并截断特别长的字符串
                        try {
                            str = str.replace(/data:image\/[a-zA-Z+.-]+;base64,[A-Za-z0-9+/=]+/g, function (m) {
                                return 'data:image;base64,[omitted(len=' + m.length + ')]';
                            });
                        } catch (_) {}
                        if (str.length > 20000) {
                            return str.slice(0, 1000) + '...[omitted(len=' + str.length + ')]';
                        }
                        return str;
                    }
                }

                function safeSerialize(arg) {
                    try {
                        if (typeof arg === 'string') return sanitizeString(arg);
                        if (arg && typeof arg === 'object') {
                            return JSON.stringify(arg, function (key, value) {
                                if (typeof value === 'string') {
                                    const k = (key || '').toLowerCase();
                                    if (k.includes('thumbnail')) {
                                        return `[thumbnail_omitted(len=${value.length})]`;
                                    }
                                    if (value.startsWith('data:image')) {
                                        return `data:image;base64,[omitted(len=${value.length})]`;
                                    }
                                    if (value.length > 20000) {
                                        return `[omitted(len=${value.length})]`;
                                    }
                                }
                                return value;
                            });
                        }
                        return String(arg);
                    } catch (e) {
                        return String(arg);
                    }
                }

                function send(level, args) {
                    try {
                        const payload = {
                            level: level,
                            messages: Array.from(args || []).map(safeSerialize),
                            url: location.href,
                            user_agent: navigator.userAgent,
                            timestamp: Date.now()
                        };
                        fetch('/api/client-log', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        }).catch(function(){});
                    } catch (e) {}
                }

                // 包装各级别日志
                levels.forEach(function(level) {
                    const orig = original[level] ? original[level].bind(console) : function(){};
                    console[level] = function() {
                        try { send(level, arguments); } catch (e) {}
                        return orig.apply(console, arguments);
                    };
                });

                // 捕获未处理错误
                window.addEventListener('error', function(ev) {
                    send('error', [String(ev.message || 'window.onerror'), (ev.filename||'') + ':' + (ev.lineno||'') + ':' + (ev.colno||'')]);
                });
                window.addEventListener('unhandledrejection', function(ev) {
                    send('error', ['UnhandledPromiseRejection', String(ev.reason)]);
                });
            } catch (e) {
                try { console.warn('前端日志上报初始化失败:', e); } catch (_) {}
            }
        })();
    </script>

    <style>
        body {
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: bold;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .image-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
            position: relative;
        }

        .image-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .image-card.selected {
            border: 3px solid #007bff;
            transform: translateY(-2px);
        }

        .image-thumbnail {
            width: 100%;
            height: 150px;
            object-fit: cover;
            cursor: pointer;
        }

        .image-info {
            padding: 10px;
            font-size: 12px;
            color: #666;
        }

        .image-checkbox {
            position: absolute;
            top: 8px;
            left: 8px;
            z-index: 10;
        }

        .status-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 10;
        }

        .image-type-badge {
            position: absolute;
            top: 35px;
            right: 8px;
            z-index: 10;
            font-size: 0.7em;
        }

        .image-preview-modal .modal-dialog {
            max-width: 90vw;
            max-height: 90vh;
        }

        .image-preview-modal .modal-body {
            padding: 0;
            text-align: center;
        }

        .image-preview-modal img {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .action-buttons {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-section {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pagination-section {
            margin: 20px 0;
            text-align: center;
        }

        .stats-section {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        @media (max-width: 1200px) {
            .image-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 992px) {
            .image-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .image-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 576px) {
            .image-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-images"></i> 图片质量管理系统
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'images' %}active{% endif %}"
                           href="{{ url_for('images') }}">
                            <i class="fas fa-image"></i> 原始图片
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'trash' %}active{% endif %}"
                           href="{{ url_for('trash') }}">
                            <i class="fas fa-trash"></i> 回收站
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin' %}active{% endif %}"
                           href="{{ url_for('admin') }}" id="adminLink" style="display: none;">
                            <i class="fas fa-crown text-warning"></i> 管理员
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text" id="stats-display">
                            <i class="fas fa-chart-bar"></i> 统计信息
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <!-- 图片预览模态框 -->
    <div class="modal fade image-preview-modal" id="imagePreviewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <img id="previewImage" src="" alt="图片预览">
                </div>
                <div class="modal-footer">
                    <div id="imageDetails" class="text-start flex-grow-1">
                        <!-- 图片详细信息将在这里显示 -->
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="confirmMessage">确定要执行此操作吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmButton">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notificationToast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">系统通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- 通知消息 -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 检测Bootstrap是否加载成功 -->
    <script>
        // 检测Bootstrap是否加载成功
        window.addEventListener('load', function() {
            if (typeof bootstrap === 'undefined') {
                console.warn('Bootstrap未能加载，使用备用方案');
                // 创建一个简单的Modal替代方案
                window.bootstrap = {
                    Modal: function(element) {
                        return {
                            show: function() {
                                if (element) element.style.display = 'block';
                            },
                            hide: function() {
                                if (element) element.style.display = 'none';
                            }
                        };
                    },
                    Toast: function(element) {
                        return {
                            show: function() {
                                if (element) {
                                    element.style.display = 'block';
                                    setTimeout(() => {
                                        element.style.display = 'none';
                                    }, 3000);
                                }
                            }
                        };
                    }
                };
            }
        });
    </script>

    <script>
        // 工具函数
        function showToast(message, type = 'info') {
            const toast = document.getElementById('notificationToast');
            const toastMessage = document.getElementById('toastMessage');

            toastMessage.textContent = message;

            // 设置样式
            toast.className = `toast ${type === 'error' ? 'bg-danger text-white' :
                                     type === 'success' ? 'bg-success text-white' :
                                     'bg-info text-white'}`;

            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        function showLoading() {
            document.querySelector('.loading-spinner').style.display = 'block';
        }

        function hideLoading() {
            document.querySelector('.loading-spinner').style.display = 'none';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(timestamp) {
            return new Date(timestamp * 1000).toLocaleString('zh-CN');
        }

        // 加载统计信息
        function loadStatistics() {
            fetch('/api/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.data;
                        document.getElementById('stats-display').innerHTML =
                            `<i class="fas fa-chart-bar"></i> 总计: ${stats.total} | 有效: ${stats.valid} | 未标记: ${stats.empty} | 已删除: ${stats.invalid}`;
                    }
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                });
        }

        // 检查用户权限并显示管理员链接
        function checkUserPermissions() {
            fetch('/api/user/info')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.is_admin) {
                        document.getElementById('adminLink').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('检查用户权限失败:', error);
                });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            checkUserPermissions();
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

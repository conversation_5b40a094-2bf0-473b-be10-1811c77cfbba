{% extends "base.html" %}

{% block title %}管理员控制台 - 图片质量管理系统{% endblock %}

{% block content %}
<!-- 管理员标题 -->
<div class="admin-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h4><i class="fas fa-crown text-warning"></i> 管理员控制台</h4>
            <p class="text-muted mb-0">监控用户状态和管理系统分片分配</p>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-outline-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i> 刷新数据
            </button>
        </div>
    </div>
</div>

<!-- 系统统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">在线用户</h5>
                <h2 class="text-primary" id="onlineUsers">0</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">总标注数</h5>
                <h2 class="text-success" id="totalAnnotations">0</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">活跃分片</h5>
                <h2 class="text-info" id="activeChunks">0</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">今日操作</h5>
                <h2 class="text-warning" id="todayOperations">0</h2>
            </div>
        </div>
    </div>
</div>

<!-- 用户状态表格 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-users"></i> 用户状态监控</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>用户IP</th>
                        <th>状态</th>
                        <th>角色</th>
                        <th>上线时间</th>
                        <th>最后活跃</th>
                        <th>分配分片</th>
                        <th>已标注</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <!-- 用户数据将在这里动态生成 -->
                </tbody>
            </table>
        </div>
        
        <div class="text-center py-3" id="noUsersMessage" style="display: none;">
            <i class="fas fa-users-slash fa-2x text-muted mb-2"></i>
            <p class="text-muted">暂无在线用户</p>
        </div>
    </div>
</div>

<!-- 操作统计图表区域 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">用户标注进度</h6>
            </div>
            <div class="card-body">
                <canvas id="userProgressChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">操作类型分布</h6>
            </div>
            <div class="card-body">
                <canvas id="operationChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 强制释放确认模态框 -->
<div class="modal fade" id="forceReleaseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认强制释放</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要强制释放用户 <strong id="targetUserIP"></strong> 的分片分配吗？</p>
                <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> 此操作将立即释放该用户的所有分片，可能影响其正在进行的标注工作。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmForceRelease">确认释放</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let usersData = [];
    let statisticsData = {};
    
    // 加载管理员数据
    function loadAdminData() {
        fetch('/api/admin/users')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    usersData = data.data.users;
                    statisticsData = data.data.statistics;
                    
                    updateStatistics();
                    renderUsersTable();
                } else {
                    showToast(data.error || '加载数据失败', 'error');
                }
            })
            .catch(error => {
                console.error('加载管理员数据失败:', error);
                showToast('网络错误，请稍后重试', 'error');
            });
    }
    
    // 更新统计信息
    function updateStatistics() {
        const onlineUsers = usersData.filter(user => user.is_online).length;
        const totalAnnotations = usersData.reduce((sum, user) => sum + (user.annotated_count || 0), 0);
        const activeChunks = usersData.reduce((sum, user) => sum + (user.assigned_chunks?.length || 0), 0);
        const todayOperations = statisticsData.total_operations || 0;
        
        document.getElementById('onlineUsers').textContent = onlineUsers;
        document.getElementById('totalAnnotations').textContent = totalAnnotations;
        document.getElementById('activeChunks').textContent = activeChunks;
        document.getElementById('todayOperations').textContent = todayOperations;
    }
    
    // 渲染用户表格
    function renderUsersTable() {
        const tbody = document.getElementById('usersTableBody');
        const noUsersMessage = document.getElementById('noUsersMessage');
        
        if (usersData.length === 0) {
            tbody.innerHTML = '';
            noUsersMessage.style.display = 'block';
            return;
        }
        
        noUsersMessage.style.display = 'none';
        
        tbody.innerHTML = usersData.map(user => `
            <tr class="${user.is_online ? 'table-success' : 'table-light'}">
                <td>
                    <code>${user.ip}</code>
                    ${user.is_admin ? '<span class="badge bg-warning ms-1">管理员</span>' : ''}
                </td>
                <td>
                    <span class="badge ${user.is_online ? 'bg-success' : 'bg-secondary'}">
                        <i class="fas ${user.is_online ? 'fa-circle' : 'fa-circle'}"></i>
                        ${user.is_online ? '在线' : '离线'}
                    </span>
                </td>
                <td>
                    ${user.is_admin ? 
                        '<span class="badge bg-warning"><i class="fas fa-crown"></i> 管理员</span>' : 
                        '<span class="badge bg-primary">用户</span>'
                    }
                </td>
                <td>${formatTime(user.login_time)}</td>
                <td>${formatTime(user.last_activity)}</td>
                <td>
                    ${user.assigned_chunks && user.assigned_chunks.length > 0 ? 
                        `<span class="badge bg-info">${user.assigned_chunks.length} 个分片</span>` : 
                        '<span class="text-muted">无</span>'
                    }
                </td>
                <td>
                    <span class="badge bg-success">${user.annotated_count || 0}</span>
                </td>
                <td>
                    ${user.assigned_chunks && user.assigned_chunks.length > 0 && !user.is_admin ? 
                        `<button class="btn btn-sm btn-outline-danger" onclick="forceReleaseUser('${user.ip}')">
                            <i class="fas fa-unlock"></i> 释放
                        </button>` : 
                        '<span class="text-muted">-</span>'
                    }
                </td>
            </tr>
        `).join('');
    }
    
    // 格式化时间
    function formatTime(timestamp) {
        if (!timestamp) return '-';
        const date = new Date(timestamp * 1000);
        return date.toLocaleString('zh-CN');
    }
    
    // 强制释放用户分片
    function forceReleaseUser(userIP) {
        document.getElementById('targetUserIP').textContent = userIP;
        const modal = new bootstrap.Modal(document.getElementById('forceReleaseModal'));
        
        document.getElementById('confirmForceRelease').onclick = function() {
            modal.hide();
            
            fetch('/api/admin/force_release', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    target_user_ip: userIP
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`成功释放用户 ${userIP} 的分片`, 'success');
                    loadAdminData();
                } else {
                    showToast(data.error || '释放失败', 'error');
                }
            })
            .catch(error => {
                console.error('释放用户分片失败:', error);
                showToast('网络错误，请稍后重试', 'error');
            });
        };
        
        modal.show();
    }
    
    // 刷新数据
    function refreshData() {
        loadAdminData();
        showToast('数据已刷新', 'info');
    }
    
    // 显示提示消息
    function showToast(message, type = 'info') {
        // 简单的提示实现
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const toast = document.createElement('div');
        toast.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
    
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadAdminData();
        
        // 定期刷新数据
        setInterval(loadAdminData, 30000); // 每30秒刷新一次
    });
</script>
{% endblock %}

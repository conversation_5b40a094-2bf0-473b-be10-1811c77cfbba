{% extends "base.html" %}

{% block title %}错误 {{ error_code }} - 图片质量管理系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="text-center">
            <div class="error-icon mb-4">
                {% if error_code == 404 %}
                    <i class="fas fa-search fa-5x text-warning"></i>
                {% else %}
                    <i class="fas fa-exclamation-triangle fa-5x text-danger"></i>
                {% endif %}
            </div>
            
            <h1 class="display-1 fw-bold">{{ error_code }}</h1>
            
            <h2 class="mb-4">
                {% if error_code == 404 %}
                    页面未找到
                {% else %}
                    服务器错误
                {% endif %}
            </h2>
            
            <p class="lead text-muted mb-4">
                {{ error_message }}
            </p>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
                <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i> 返回上页
                </button>
            </div>
        </div>
    </div>
</div>

{% if error_code == 404 %}
<div class="row justify-content-center mt-5">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb"></i> 建议
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> 检查URL是否正确</li>
                    <li><i class="fas fa-check text-success"></i> 尝试从导航菜单重新访问</li>
                    <li><i class="fas fa-check text-success"></i> 如果问题持续存在，请联系管理员</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% extends "base.html" %}

{% block title %}原始图片 - 图片质量管理系统{% endblock %}

{% block content %}
<!-- 用户信息区域 -->
<div class="user-info-section mb-3">
    <div class="card">
        <div class="card-body py-2">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <small class="text-muted">
                        <i class="fas fa-user"></i> 当前用户: <span id="userIP">加载中...</span>
                        <span class="ms-3"><i class="fas fa-layer-group"></i> 分片范围: <span id="chunkRange">加载中...</span></span>
                        <span class="ms-3"><i class="fas fa-check-circle"></i> 已标注: <span id="annotatedCount">0</span> 张</span>
                    </small>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success" id="userStatus">在线</span>
                    <button class="btn btn-sm btn-outline-primary ms-2" id="completeChunkBtn" style="display: none;">
                        <i class="fas fa-forward"></i> 完成当前分片
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和统计区域 -->
<div class="filter-section">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h4><i class="fas fa-image"></i> 原始图片管理</h4>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group" role="group">
                <input type="radio" class="btn-check" name="statusFilter" id="filterAll" value="">
                <label class="btn btn-outline-primary" for="filterAll">全部</label>

                <input type="radio" class="btn-check" name="statusFilter" id="filterValid" value="valid">
                <label class="btn btn-outline-success" for="filterValid">有效</label>

                <input type="radio" class="btn-check" name="statusFilter" id="filterEmpty" value="empty" checked>
                <label class="btn btn-outline-info" for="filterEmpty">未标记</label>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮区域 -->
<div class="action-buttons">
    <div class="row align-items-center">
        <div class="col-md-6">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="selectAllBtn">
                    <i class="fas fa-check-square"></i> 全选
                </button>
                <button type="button" class="btn btn-outline-secondary" id="clearSelectionBtn">
                    <i class="fas fa-square"></i> 清除选择
                </button>
            </div>
            
            <span class="ms-3 text-muted" id="selectionInfo">
                已选择 <span id="selectedCount">0</span> 张图片
            </span>
        </div>
        
        <div class="col-md-6 text-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-success" id="markValidBtn" disabled>
                    <i class="fas fa-check"></i> 标记有效
                </button>
                <button type="button" class="btn btn-warning" id="softDeleteBtn" disabled>
                    <i class="fas fa-trash"></i> 移到回收站
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 分页和页面大小选择 -->
<div class="pagination-section">
    <div class="row align-items-center">
        <div class="col-md-6">
            <div class="d-flex align-items-center">
                <label for="pageSizeSelect" class="form-label me-2">每页显示:</label>
                <select class="form-select" id="pageSizeSelect" style="width: auto;">
                    <option value="20">20</option>
                    <option value="30">30</option>
                    <option value="50">50</option>
                </select>
                <span class="ms-3 text-muted" id="pageInfo">
                    <!-- 页面信息将在这里显示 -->
                </span>
            </div>
        </div>
        
        <div class="col-md-6">
            <nav>
                <ul class="pagination justify-content-end mb-0" id="pagination">
                    <!-- 分页按钮将在这里动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 加载指示器 -->
<div class="loading-spinner">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2">正在加载图片...</p>
</div>

<!-- 图片网格 -->
<div class="image-grid" id="imageGrid">
    <!-- 图片卡片将在这里动态生成 -->
</div>

<!-- 空状态提示 -->
<div class="text-center py-5" id="emptyState" style="display: none;">
    <i class="fas fa-images fa-3x text-muted mb-3"></i>
    <h5 class="text-muted">没有找到图片</h5>
    <p class="text-muted">请尝试调整筛选条件或检查数据目录</p>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面特定变量
    let images = [];
    let pagination = {};
    let selectedImages = new Set();
    let currentFilter = 'empty';  // 默认显示未标记的图片
    let currentPage = 1;
    let currentPageSize = 20;
    let userInfo = null;
    let heartbeatInterval = null;

    // 加载用户信息
    function loadUserInfo() {
        console.log('开始加载用户信息...');
        fetch('/api/user/info')
            .then(response => {
                console.log('用户信息API响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('用户信息API响应数据:', data);
                if (data.success) {
                    userInfo = data.data;
                    console.log('用户信息加载成功:', userInfo);
                    updateUserInfoDisplay();
                    startHeartbeat();
                } else {
                    console.error('获取用户信息失败:', data.error);
                    // 显示错误信息
                    document.getElementById('userIP').textContent = '加载失败';
                    document.getElementById('chunkRange').textContent = '加载失败';
                }
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
                // 显示错误信息
                document.getElementById('userIP').textContent = '网络错误';
                document.getElementById('chunkRange').textContent = '网络错误';
            });
    }

    // 更新用户信息显示
    function updateUserInfoDisplay() {
        if (!userInfo) return;

        document.getElementById('userIP').textContent = userInfo.ip;
        document.getElementById('annotatedCount').textContent = userInfo.annotated_count || 0;

        if (userInfo.chunk_range) {
            const [start, end] = userInfo.chunk_range;
            document.getElementById('chunkRange').textContent = `${start + 1} - ${end}`;
        } else {
            document.getElementById('chunkRange').textContent = '无分配';
        }

        // 显示管理员标识（先清理旧的，避免重复）
        const statusContainer = document.getElementById('userStatus').parentNode;
        statusContainer.querySelectorAll('.admin-badge').forEach(el => el.remove());
        if (userInfo.is_admin) {
            const adminBadge = document.createElement('span');
            adminBadge.className = 'badge bg-warning ms-2 admin-badge';
            adminBadge.innerHTML = '<i class="fas fa-crown"></i> 管理员';
            statusContainer.insertBefore(adminBadge, document.getElementById('userStatus'));
        }
    }

    // 启动心跳
    function startHeartbeat() {
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
        }

        heartbeatInterval = setInterval(() => {
            fetch('/api/user/heartbeat', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('userStatus').textContent = '在线';
                        document.getElementById('userStatus').className = 'badge bg-success';
                    }
                })
                .catch(error => {
                    document.getElementById('userStatus').textContent = '离线';
                    document.getElementById('userStatus').className = 'badge bg-danger';
                });
        }, 30000); // 每30秒发送一次心跳
    }

    // 加载图片列表
    function loadImages() {
        console.log('开始加载图片列表...');
        showLoading();

        const params = new URLSearchParams({
            status: currentFilter,
            page: currentPage,
            page_size: currentPageSize
        });

        console.log('图片列表API请求参数:', params.toString());
        fetch(`/api/images?${params}`)
            .then(response => {
                console.log('图片列表API响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('图片列表API响应数据:', data);
                hideLoading();

                if (data.success) {
                    images = data.data.images;
                    pagination = data.data.pagination;
                    console.log('图片数据加载成功，图片数量:', images.length);

                    // 更新用户信息
                    if (data.data.user_info) {
                        userInfo = data.data.user_info;
                        console.log('更新用户信息:', userInfo);
                        updateUserInfoDisplay();
                    }

                    renderImages();
                    renderPagination();
                    updatePageInfo();
                    clearSelection();

                    // 自动全选当前页面的图片
                    setTimeout(selectAll, 100);
                } else {
                    console.error('图片列表加载失败:', data.error);
                    showToast(data.error || '加载图片失败', 'error');
                    showEmptyState();
                }
            })
            .catch(error => {
                hideLoading();
                console.error('加载图片失败:', error);
                showToast('网络错误，请稍后重试', 'error');
                showEmptyState();
            });
    }
    
    // 渲染图片网格
    function renderImages() {
        const grid = document.getElementById('imageGrid');
        const emptyState = document.getElementById('emptyState');

        if (images.length === 0) {
            showEmptyState();
            return;
        }
        
        emptyState.style.display = 'none';
        
        grid.innerHTML = images.map(image => `
            <div class="image-card" data-filename="${image.initfile_name}">
                <input type="checkbox" class="form-check-input image-checkbox" 
                       value="${image.initfile_name}" onchange="updateSelection()">
                
                ${getStatusBadge(image.status)}
                ${getImageTypeBadge(image)}

                <img src="data:image/jpeg;base64,${image.thumbnail}"
                     alt="${image.initfile_name}" 
                     class="image-thumbnail"
                     onclick="showImagePreview('${image.initfile_name}')">
                
                <div class="image-info">
                    <div class="fw-bold text-truncate" title="${image.initfile_name}">
                        ${image.initfile_name}
                    </div>
                    <div class="text-muted">
                        ${image.business_type || '未分类'} | 
                        事件: ${image.event_content_pro || '无'}
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    // 获取状态徽章
    function getStatusBadge(status) {
        if (status === 'valid') {
            return '<span class="badge bg-success status-badge">有效</span>';
        } else if (status === 'invalid') {
            return '<span class="badge bg-warning status-badge">回收站</span>';
        } else if (status === 'deleted') {
            return '<span class="badge bg-danger status-badge">已删除</span>';
        } else {
            return '<span class="badge bg-secondary status-badge">未标记</span>';
        }
    }

    // 获取图片类型徽章
    function getImageTypeBadge(image) {
        if (image.is_intefile) {
            return '<span class="badge bg-info image-type-badge">标注图片</span>';
        } else {
            return '<span class="badge bg-light text-dark image-type-badge">原始图片</span>';
        }
    }
    
    // 显示空状态
    function showEmptyState() {
        document.getElementById('imageGrid').innerHTML = '';
        document.getElementById('emptyState').style.display = 'block';
    }
    
    // 渲染分页
    function renderPagination() {
        const paginationEl = document.getElementById('pagination');
        
        if (pagination.total_pages <= 1) {
            paginationEl.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // 上一页
        html += `
            <li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(pagination.total_pages, currentPage + 2);
        
        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        }
        
        if (endPage < pagination.total_pages) {
            if (endPage < pagination.total_pages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${pagination.total_pages})">${pagination.total_pages}</a></li>`;
        }
        
        // 下一页
        html += `
            <li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
        
        paginationEl.innerHTML = html;
    }
    
    // 更新页面信息
    function updatePageInfo() {
        const info = document.getElementById('pageInfo');
        info.textContent = `第 ${pagination.current_page} 页，共 ${pagination.total_pages} 页，总计 ${pagination.total_count} 张图片`;
    }
    
    // 切换页面
    function changePage(page) {
        if (page < 1 || page > pagination.total_pages || page === currentPage) {
            return;
        }
        currentPage = page;
        loadImages();
    }
    
    // 更新选择状态
    function updateSelection() {
        const checkboxes = document.querySelectorAll('.image-checkbox:checked');
        selectedImages.clear();
        
        checkboxes.forEach(cb => {
            selectedImages.add(cb.value);
            cb.closest('.image-card').classList.add('selected');
        });
        
        // 移除未选中的卡片的选中样式
        document.querySelectorAll('.image-card').forEach(card => {
            const checkbox = card.querySelector('.image-checkbox');
            if (!checkbox.checked) {
                card.classList.remove('selected');
            }
        });
        
        // 更新选择计数和按钮状态
        document.getElementById('selectedCount').textContent = selectedImages.size;
        
        const hasSelection = selectedImages.size > 0;
        document.getElementById('markValidBtn').disabled = !hasSelection;
        document.getElementById('softDeleteBtn').disabled = !hasSelection;
    }
    
    // 全选
    function selectAll() {
        const checkboxes = document.querySelectorAll('.image-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = true;
        });
        updateSelection();
    }
    
    // 清除选择
    function clearSelection() {
        const checkboxes = document.querySelectorAll('.image-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = false;
        });
        selectedImages.clear();
        updateSelection();
    }
    
    // 显示图片预览
    function showImagePreview(filename) {
        const modal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
        const previewImg = document.getElementById('previewImage');
        const details = document.getElementById('imageDetails');
        
        // 设置图片源
        previewImg.src = `/api/image/${filename}`;
        
        // 获取图片详细信息
        const image = images.find(img => img.initfile_name === filename);
        if (image) {
            details.innerHTML = `
                <strong>文件名:</strong> ${image.initfile_name}<br>
                <strong>业务类型:</strong> ${image.business_type || '未分类'}<br>
                <strong>事件内容:</strong> ${image.event_content_pro || '无'}<br>
                <strong>状态:</strong> ${getStatusText(image.valid)}
            `;
        }
        
        modal.show();
    }
    
    // 获取状态文本
    function getStatusText(status) {
        if (status === 'valid') return '有效';
        if (status === 'invalid') return '已删除';
        return '未标记';
    }
    
    // 标记为有效
    function markAsValid() {
        if (selectedImages.size === 0) return;

        const selectedArray = Array.from(selectedImages);

        // 获取当前页面所有图片
        const currentPageImages = images.map(img => img.initfile_name);
        // 计算未选中的图片
        const unselectedImages = currentPageImages.filter(name => !selectedImages.has(name));

        // 显示确认对话框
        const confirmMessage = `确认操作：\n` +
            `• 标记 ${selectedArray.length} 张图片为有效\n` +
            `• 将剩余 ${unselectedImages.length} 张图片移到回收站\n\n` +
            `是否继续？`;

        if (!confirm(confirmMessage)) {
            return;
        }

        // 执行标记有效操作
        fetch('/api/images/mark_valid', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                initfile_names: selectedArray
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`成功标记 ${data.data.updated_count} 张图片为有效`, 'success');

                // 如果有未选中的图片，自动移到回收站
                if (unselectedImages.length > 0) {
                    return autoSoftDeleteRemaining(unselectedImages);
                }
                return Promise.resolve();
            } else {
                throw new Error(data.error || '标记失败');
            }
        })
        .then(() => {
            // 刷新页面和统计信息
            loadImages();
            loadStatistics();
        })
        .catch(error => {
            console.error('操作失败:', error);
            showToast(error.message || '网络错误，请稍后重试', 'error');
        });
    }

    // 自动软删除剩余图片（标记有效后的批量处理）
    function autoSoftDeleteRemaining(unselectedImages) {
        return fetch('/api/images/soft_delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                initfile_names: unselectedImages
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`自动移动 ${data.data.success_count} 张剩余图片到回收站`, 'info');
                if (data.data.failed_files && data.data.failed_files.length > 0) {
                    console.warn('部分文件移动失败:', data.data.failed_files);
                }
            } else {
                throw new Error(data.error || '自动删除剩余图片失败');
            }
        });
    }

    // 软删除
    function softDelete() {
        if (selectedImages.size === 0) return;
        
        const selectedArray = Array.from(selectedImages);
        
        // 显示确认对话框
        document.getElementById('confirmMessage').textContent = 
            `确定要将选中的 ${selectedArray.length} 张图片移动到回收站吗？`;
        
        const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
        
        document.getElementById('confirmButton').onclick = function() {
            modal.hide();
            
            fetch('/api/images/soft_delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    initfile_names: selectedArray
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const result = data.data;
                    if (result.failed_count > 0) {
                        showToast(`成功移动 ${result.success_count} 张图片，${result.failed_count} 张失败`, 'warning');
                    } else {
                        showToast(`成功移动 ${result.success_count} 张图片到回收站`, 'success');
                    }
                    loadImages();
                    loadStatistics();
                } else {
                    showToast(data.error || '移动失败', 'error');
                }
            })
            .catch(error => {
                console.error('移动失败:', error);
                showToast('网络错误，请稍后重试', 'error');
            });
        };
        
        modal.show();
    }
    
    // 事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM加载完成，开始初始化...');

        // 检查必要的元素是否存在
        const requiredElements = ['userIP', 'chunkRange', 'annotatedCount', 'imageGrid'];
        const missingElements = requiredElements.filter(id => !document.getElementById(id));

        if (missingElements.length > 0) {
            console.error('缺少必要的DOM元素:', missingElements);
            return;
        }

        // 筛选器变化
        document.querySelectorAll('input[name="statusFilter"]').forEach(radio => {
            radio.addEventListener('change', function() {
                currentFilter = this.value;
                currentPage = 1;
                loadImages();
            });
        });

        // 页面大小变化
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', function() {
                currentPageSize = parseInt(this.value);
                currentPage = 1;
                loadImages();
            });
        }

        // 按钮事件
        const selectAllBtn = document.getElementById('selectAllBtn');
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        const markValidBtn = document.getElementById('markValidBtn');
        const softDeleteBtn = document.getElementById('softDeleteBtn');

        if (selectAllBtn) selectAllBtn.addEventListener('click', selectAll);
        if (clearSelectionBtn) clearSelectionBtn.addEventListener('click', clearSelection);
        if (markValidBtn) markValidBtn.addEventListener('click', markAsValid);
        if (softDeleteBtn) softDeleteBtn.addEventListener('click', softDelete);

        // 初始加载
        console.log('开始初始化加载...');
        loadUserInfo();
        loadImages();
    });
</script>
{% endblock %}

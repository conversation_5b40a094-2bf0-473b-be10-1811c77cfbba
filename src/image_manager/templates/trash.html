{% extends "base.html" %}

{% block title %}回收站 - 图片质量管理系统{% endblock %}

{% block content %}
<!-- 标题区域 -->
<div class="filter-section">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h4><i class="fas fa-trash"></i> 回收站管理</h4>
            <p class="text-muted mb-0">这里显示已移动到回收站的图片，可以进行永久删除操作</p>
        </div>
        <div class="col-md-6 text-end">
            <div class="alert alert-warning mb-0" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>注意:</strong> 永久删除操作不可恢复！
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮区域 -->
<div class="action-buttons">
    <div class="row align-items-center">
        <div class="col-md-6">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="selectAllBtn">
                    <i class="fas fa-check-square"></i> 全选
                </button>
                <button type="button" class="btn btn-outline-secondary" id="clearSelectionBtn">
                    <i class="fas fa-square"></i> 清除选择
                </button>
            </div>

            <span class="ms-3 text-muted" id="selectionInfo">
                已选择 <span id="selectedCount">0</span> 张图片
            </span>
        </div>

        <div class="col-md-6 text-end">
            <button type="button" class="btn btn-danger" id="permanentDeleteBtn" disabled>
                <i class="fas fa-times"></i> 永久删除
            </button>
        </div>
    </div>
</div>

<!-- 分页和页面大小选择 -->
<div class="pagination-section">
    <div class="row align-items-center">
        <div class="col-md-6">
            <div class="d-flex align-items-center">
                <label for="pageSizeSelect" class="form-label me-2">每页显示:</label>
                <select class="form-select" id="pageSizeSelect" style="width: auto;">
                    <option value="20">20</option>
                    <option value="30">30</option>
                    <option value="50">50</option>
                </select>
                <span class="ms-3 text-muted" id="pageInfo">
                    <!-- 页面信息将在这里显示 -->
                </span>
            </div>
        </div>

        <div class="col-md-6">
            <nav>
                <ul class="pagination justify-content-end mb-0" id="pagination">
                    <!-- 分页按钮将在这里动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 加载指示器 -->
<div class="loading-spinner">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2">正在加载回收站图片...</p>
</div>

<!-- 图片网格 -->
<div class="image-grid" id="imageGrid">
    <!-- 图片卡片将在这里动态生成 -->
</div>

<!-- 空状态提示 -->
<div class="text-center py-5" id="emptyState" style="display: none;">
    <i class="fas fa-trash fa-3x text-muted mb-3"></i>
    <h5 class="text-muted">回收站为空</h5>
    <p class="text-muted">没有找到已删除的图片</p>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面特定变量
    let trashImages = [];
    let pagination = {};
        // 页面级变量（避免依赖 base.html 全局）
        let selectedImages = new Set();
        let currentPage = 1;
        let currentPageSize = 20;


    // 加载回收站图片列表
    function loadTrashImages() {
        showLoading();

        const params = new URLSearchParams({
            page: currentPage,
            page_size: currentPageSize
        });

        fetch(`/api/trash?${params}`)
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success) {
                    trashImages = data.data.images;
                    pagination = data.data.pagination;

                    renderTrashImages();
                    renderPagination();
                    updatePageInfo();
                    clearSelection();
                } else {
                    showToast(data.error || '加载回收站失败', 'error');
                    showEmptyState();
                }
            })
            .catch(error => {
                hideLoading();
                console.error('加载回收站失败:', error);
                showToast('网络错误，请稍后重试', 'error');
                showEmptyState();
            });
    }

    // 渲染回收站图片网格
    function renderTrashImages() {
        const grid = document.getElementById('imageGrid');
        const emptyState = document.getElementById('emptyState');

        if (trashImages.length === 0) {
            showEmptyState();
            return;
        }

        emptyState.style.display = 'none';

        grid.innerHTML = trashImages.map(image => `
            <div class="image-card" data-filename="${image.initfile_name}">
                <input type="checkbox" class="form-check-input image-checkbox"
                       value="${image.initfile_name}" onchange="updateSelection()">

                <span class="badge bg-danger status-badge">回收站</span>

                <img src="data:image/jpeg;base64,${image.thumbnail}"
                     alt="${image.initfile_name}"
                     class="image-thumbnail"
                     onclick="showImagePreview('${image.initfile_name}')">

                <div class="image-info">
                    <div class="fw-bold text-truncate" title="${image.initfile_name}">
                        ${image.initfile_name}
                    </div>
                    <div class="text-muted">
                        ${image.business_type || '未分类'} |
                        事件: ${image.event_content_pro || '无'}
                    </div>
                </div>
            </div>
        `).join('');
    }

    // 显示空状态
    function showEmptyState() {
        document.getElementById('imageGrid').innerHTML = '';
        document.getElementById('emptyState').style.display = 'block';
    }

    // 渲染分页（复用原始图片页面的逻辑）
    function renderPagination() {
        const paginationEl = document.getElementById('pagination');

        if (pagination.total_pages <= 1) {
            paginationEl.innerHTML = '';
            return;
        }

        let html = '';

        // 上一页
        html += `
            <li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(pagination.total_pages, currentPage + 2);

        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < pagination.total_pages) {
            if (endPage < pagination.total_pages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${pagination.total_pages})">${pagination.total_pages}</a></li>`;
        }

        // 下一页
        html += `
            <li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;

        paginationEl.innerHTML = html;
    }

    // 更新页面信息
    function updatePageInfo() {
        const info = document.getElementById('pageInfo');
        info.textContent = `第 ${pagination.current_page} 页，共 ${pagination.total_pages} 页，总计 ${pagination.total_count} 张图片`;
    }

    // 切换页面
    function changePage(page) {
        if (page < 1 || page > pagination.total_pages || page === currentPage) {
            return;
        }
        currentPage = page;
        loadTrashImages();
    }

    // 更新选择状态
    function updateSelection() {
        const checkboxes = document.querySelectorAll('.image-checkbox:checked');
        selectedImages.clear();

        checkboxes.forEach(cb => {
            selectedImages.add(cb.value);
            cb.closest('.image-card').classList.add('selected');
        });

        // 移除未选中的卡片的选中样式
        document.querySelectorAll('.image-card').forEach(card => {
            const checkbox = card.querySelector('.image-checkbox');
            if (!checkbox.checked) {
                card.classList.remove('selected');
            }
        });

        // 更新选择计数和按钮状态
        document.getElementById('selectedCount').textContent = selectedImages.size;

        const hasSelection = selectedImages.size > 0;
        document.getElementById('permanentDeleteBtn').disabled = !hasSelection;
    }

    // 全选
    function selectAll() {
        const checkboxes = document.querySelectorAll('.image-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = true;
        });
        updateSelection();
    }

    // 清除选择
    function clearSelection() {
        const checkboxes = document.querySelectorAll('.image-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = false;
        });
        selectedImages.clear();
        updateSelection();
    }

    // 显示图片预览
    function showImagePreview(filename) {
        const modal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
        const previewImg = document.getElementById('previewImage');
        const details = document.getElementById('imageDetails');

        // 设置图片源（从回收站读取）
        previewImg.src = `/api/image/${filename}`;

        // 获取图片详细信息
        const image = trashImages.find(img => img.initfile_name === filename);
        if (image) {
            details.innerHTML = `
                <strong>文件名:</strong> ${image.initfile_name}<br>
                <strong>业务类型:</strong> ${image.business_type || '未分类'}<br>
                <strong>事件内容:</strong> ${image.event_content_pro || '无'}<br>
                <strong>状态:</strong> <span class="text-danger">回收站</span>
            `;
        }

        modal.show();
    }

    // 永久删除
    function permanentDelete() {
        if (selectedImages.size === 0) return;

        const selectedArray = Array.from(selectedImages);

        // 显示确认对话框
        document.getElementById('confirmMessage').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>警告：此操作不可恢复！</strong>
            </div>
            <p>确定要永久删除选中的 <strong>${selectedArray.length}</strong> 张图片吗？</p>
            <p class="text-muted">这将同时删除原始文件和标注文件，并将CSV记录标记为"invalid"。</p>
        `;

        const modal = new bootstrap.Modal(document.getElementById('confirmModal'));

        document.getElementById('confirmButton').onclick = function() {
            modal.hide();

            fetch('/api/trash/permanent_delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    initfile_names: selectedArray
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const result = data.data;
                    if (result.failed_count > 0) {
                        showToast(`成功删除 ${result.success_count} 张图片，${result.failed_count} 张失败`, 'warning');
                    } else {
                        showToast(`成功永久删除 ${result.success_count} 张图片`, 'success');
                    }
                    loadTrashImages();
                    loadStatistics();
                } else {
                    showToast(data.error || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('删除失败:', error);
                showToast('网络错误，请稍后重试', 'error');
            });
        };

        modal.show();
    }

    // 事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        // 页面大小变化
        document.getElementById('pageSizeSelect').addEventListener('change', function() {
            currentPageSize = parseInt(this.value);
            currentPage = 1;
            loadTrashImages();
        });

        // 按钮事件
        document.getElementById('selectAllBtn').addEventListener('click', selectAll);
        document.getElementById('clearSelectionBtn').addEventListener('click', clearSelection);
        document.getElementById('permanentDeleteBtn').addEventListener('click', permanentDelete);

        // 初始加载
        loadTrashImages();
    });
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}首页 - 图片质量管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-white p-5 rounded shadow-sm">
            <h1 class="display-4">
                <i class="fas fa-images text-primary"></i> 
                图片质量管理系统
            </h1>
            <p class="lead">
                用于检查和管理标注图片质量的Web应用系统，提供图片质量检查、软删除、回收站管理等功能。
            </p>
            <hr class="my-4">
            <p>
                系统支持对原始图片进行质量检查，标记有效图片，将不合格图片移动到回收站，并支持永久删除操作。
            </p>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-image fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">原始图片管理</h5>
                            <p class="card-text">
                                查看、筛选和管理原始图片，支持批量标记为有效或移动到回收站。
                            </p>
                            <a href="{{ url_for('images') }}" class="btn btn-primary">
                                <i class="fas fa-arrow-right"></i> 进入管理
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-trash fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">回收站管理</h5>
                            <p class="card-text">
                                查看回收站中的图片，支持恢复或永久删除操作。
                            </p>
                            <a href="{{ url_for('trash') }}" class="btn btn-warning">
                                <i class="fas fa-arrow-right"></i> 进入回收站
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统统计信息 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> 系统统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="statisticsCards">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-images fa-2x mb-2"></i>
                                <h4 id="totalCount">-</h4>
                                <p class="mb-0">总图片数</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h4 id="validCount">-</h4>
                                <p class="mb-0">有效图片</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-question-circle fa-2x mb-2"></i>
                                <h4 id="emptyCount">-</h4>
                                <p class="mb-0">未标记</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-trash fa-2x mb-2"></i>
                                <h4 id="invalidCount">-</h4>
                                <p class="mb-0">已删除</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 功能说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> 功能说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-image text-primary"></i> 原始图片页面</h6>
                        <ul class="list-unstyled ms-3">
                            <li><i class="fas fa-check text-success"></i> 网格布局展示图片（每行5张）</li>
                            <li><i class="fas fa-check text-success"></i> 支持分页显示（20/30/50张每页）</li>
                            <li><i class="fas fa-check text-success"></i> 点击图片查看完整尺寸预览</li>
                            <li><i class="fas fa-check text-success"></i> 复选框选择单张或多张图片</li>
                            <li><i class="fas fa-check text-success"></i> 批量标记为"有效"</li>
                            <li><i class="fas fa-check text-success"></i> 批量移动到回收站（软删除）</li>
                            <li><i class="fas fa-check text-success"></i> 基于状态筛选（全部/有效/未标记）</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6><i class="fas fa-trash text-warning"></i> 回收站页面</h6>
                        <ul class="list-unstyled ms-3">
                            <li><i class="fas fa-check text-success"></i> 与原始图片页面相同的布局</li>
                            <li><i class="fas fa-check text-success"></i> 显示回收站中的图片</li>
                            <li><i class="fas fa-check text-success"></i> 支持永久删除操作</li>
                            <li><i class="fas fa-check text-success"></i> 同时删除原始文件和标注文件</li>
                            <li><i class="fas fa-check text-success"></i> 更新CSV记录状态为"invalid"</li>
                            <li><i class="fas fa-check text-success"></i> 防止重复拷贝机制</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 加载详细统计信息
    function loadDetailedStatistics() {
        fetch('/api/statistics')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const stats = data.data;
                    document.getElementById('totalCount').textContent = stats.total;
                    document.getElementById('validCount').textContent = stats.valid;
                    document.getElementById('emptyCount').textContent = stats.empty;
                    document.getElementById('invalidCount').textContent = stats.invalid;
                }
            })
            .catch(error => {
                console.error('加载统计信息失败:', error);
                showToast('加载统计信息失败', 'error');
            });
    }
    
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        loadDetailedStatistics();
        
        // 每30秒刷新一次统计信息
        setInterval(loadDetailedStatistics, 30000);
    });
</script>
{% endblock %}

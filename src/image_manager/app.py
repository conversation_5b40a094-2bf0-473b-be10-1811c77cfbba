"""
Flask Web应用核心模块

创建和配置Flask应用，定义路由和API接口。
"""

import time
import logging
from flask import Flask, render_template, request, jsonify, send_file
from pathlib import Path
from typing import Dict, Any

from .config import ImageManagerConfig
from .data_manager import DataManager
from .image_manager import ImageManager
from .user_manager import UserManager
from .concurrent_manager import ConcurrentManager


def create_app(config: Dict[str, Any] = None) -> Flask:
    """创建Flask应用实例

    Args:
        config: 额外的配置参数

    Returns:
        Flask: 配置好的Flask应用实例
    """
    app = Flask(__name__,
                template_folder='templates',
                static_folder='static')

    # 加载配置
    app_config = ImageManagerConfig.get_config_dict()
    if config:
        app_config.update(config)

    app.config.update(app_config)
    app.secret_key = app_config['secret_key']

    # 配置日志
    logging.basicConfig(
        level=getattr(logging, app_config['log_level']),
        format=app_config['log_format']
    )

    # 初始化管理器
    data_manager = DataManager()
    image_manager = ImageManager(data_manager)
    user_manager = UserManager()
    concurrent_manager = ConcurrentManager()

    # 验证必要的目录和文件
    if not ImageManagerConfig.validate_directories():
        app.logger.error("必要的目录不存在，请检查配置")
        raise RuntimeError("必要的目录不存在")

    if not ImageManagerConfig.validate_files():
        app.logger.error("必要的文件不存在，请检查配置")
        raise RuntimeError("必要的文件不存在")

    @app.before_request
    def before_request():
        """请求前处理 - 用户识别和会话管理"""
        if request.endpoint and not request.endpoint.startswith('static'):
            user_ip = user_manager.get_user_id(request)
            user_manager.update_user_activity(user_ip)
            # 将用户信息存储在请求上下文中
            request.user_ip = user_ip
            request.is_admin = user_manager.is_admin(user_ip)

    @app.route('/')
    def index():
        """首页 - 重定向到原始图片页面"""
        return render_template('index.html')

    @app.route('/images')
    def images():
        """原始图片页面"""
        return render_template('images.html')

    @app.route('/trash')
    def trash():
        """回收站页面"""
        return render_template('trash.html')

    @app.route('/admin')
    def admin():
        """管理员页面"""
        is_admin = getattr(request, 'is_admin', False)
        if not is_admin:
            return render_template('error.html',
                                 error_code=403,
                                 error_message='权限不足，需要管理员权限'), 403
        return render_template('admin.html')

    @app.route('/api/images')
    def api_get_images():
        """获取图片列表API"""
        try:
            # 获取查询参数
            status_filter = request.args.get('status', 'empty')  # 默认显示未标记的图片
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', ImageManagerConfig.DEFAULT_PAGE_SIZE))

            # 验证参数
            if page < 1:
                page = 1
            if page_size not in ImageManagerConfig.PAGE_SIZE_OPTIONS:
                page_size = ImageManagerConfig.DEFAULT_PAGE_SIZE

            # 获取用户分片范围
            user_ip = getattr(request, 'user_ip', 'unknown')
            total_images = len(data_manager.load_data())

            # 尝试获取用户分片范围，如果失败则不应用分片过滤
            try:
                user_range = user_manager.get_user_chunk_range(user_ip, total_images)
                app.logger.debug(f"用户 {user_ip} 分片范围: {user_range}")
            except Exception as e:
                app.logger.warning(f"获取用户分片失败: {e}, 使用全部数据")
                user_range = None

            # 获取图片列表（应用用户分片）
            result = image_manager.get_image_list(status_filter, page, page_size, user_range)

            # 为每个图片添加详细信息和缩略图
            for image in result['images']:
                image_info = image_manager.get_image_info(image['initfile_name'])
                if image_info:
                    # 合并详细信息
                    image.update(image_info)
                else:
                    image['thumbnail'] = ''

            # 添加用户信息到响应（并补齐分片范围，避免前端覆盖后显示“无分配”）
            user_info = user_manager.get_user_info(user_ip)
            if user_info is not None:
                user_info['chunk_range'] = user_range
                user_info['total_images'] = total_images
            result['user_info'] = user_info

            return jsonify({
                'success': True,
                'data': result
            })

        except Exception as e:
            app.logger.error(f"获取图片列表失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/user/info')
    def api_get_user_info():
        """获取当前用户信息API"""
        try:
            user_ip = getattr(request, 'user_ip', 'unknown')
            user_info = user_manager.get_user_info(user_ip)

            if user_info:
                # 获取用户分片信息
                total_images = len(data_manager.load_data())
                try:
                    user_range = user_manager.get_user_chunk_range(user_ip, total_images)
                except Exception as e:
                    app.logger.warning(f"获取用户分片信息失败: {e}")
                    user_range = None

                user_info['chunk_range'] = user_range
                user_info['total_images'] = total_images

                return jsonify({
                    'success': True,
                    'data': user_info
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '用户信息不存在'
                }), 404

        except Exception as e:
            app.logger.error(f"获取用户信息失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/user/heartbeat', methods=['POST'])
    def api_user_heartbeat():
        """用户心跳API"""
        try:
            user_ip = getattr(request, 'user_ip', 'unknown')
            user_manager.update_user_activity(user_ip)

            return jsonify({
                'success': True,
                'data': {'timestamp': time.time()}
            })

        except Exception as e:
            app.logger.error(f"用户心跳失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/user/complete_chunk', methods=['POST'])
    def api_complete_chunk():
        """完成当前分片，获取下一个分片API"""
        try:
            user_ip = getattr(request, 'user_ip', 'unknown')
            total_images = len(data_manager.load_data())

            new_range = user_manager.complete_current_chunk(user_ip, total_images)

            return jsonify({
                'success': True,
                'data': {
                    'new_range': new_range,
                    'has_more': new_range is not None
                }
            })

        except Exception as e:
            app.logger.error(f"完成分片失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/images/mark_valid', methods=['POST'])
    def api_mark_images_valid():
        """标记图片为有效API"""
        try:
            data = request.get_json()
            initfile_names = data.get('initfile_names', [])
            user_ip = getattr(request, 'user_ip', 'unknown')

            if not initfile_names:
                return jsonify({
                    'success': False,
                    'error': '未提供图片文件名'
                }), 400

            # 检查冲突
            conflicts = concurrent_manager.check_image_conflicts(user_ip, initfile_names)
            if conflicts:
                return jsonify({
                    'success': False,
                    'error': f'图片存在操作冲突: {", ".join(conflicts[:5])}'
                }), 409

            # 使用文件锁安全更新状态
            def update_operation():
                return data_manager.update_image_status(
                    initfile_names, ImageManagerConfig.STATUS_VALID)

            updated_count = concurrent_manager.safe_csv_operation(
                ImageManagerConfig.QUERY_RESULTS_CSV, update_operation)

            # 记录操作日志
            concurrent_manager.log_annotation_operation(
                user_ip, 'mark_valid', initfile_names)

            # 更新用户标注计数
            user_manager.increment_user_annotation_count(user_ip, updated_count)

            return jsonify({
                'success': True,
                'data': {
                    'updated_count': updated_count,
                    'total_requested': len(initfile_names)
                }
            })

        except Exception as e:
            app.logger.error(f"标记图片有效失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/images/soft_delete', methods=['POST'])
    def api_soft_delete_images():
        """软删除图片API"""
        try:
            data = request.get_json()
            initfile_names = data.get('initfile_names', [])

            if not initfile_names:
                return jsonify({
                    'success': False,
                    'error': '未提供图片文件名'
                }), 400

            # 执行软删除
            result = image_manager.soft_delete_images(initfile_names)

            return jsonify({
                'success': True,
                'data': result
            })

        except Exception as e:
            app.logger.error(f"软删除图片失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/trash')
    def api_get_trash():
        """获取回收站图片列表API"""
        try:
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', ImageManagerConfig.DEFAULT_PAGE_SIZE))

            if page < 1:
                page = 1
            if page_size not in ImageManagerConfig.PAGE_SIZE_OPTIONS:
                page_size = ImageManagerConfig.DEFAULT_PAGE_SIZE

            result = image_manager.get_trash_images(page, page_size)

            return jsonify({
                'success': True,
                'data': result
            })

        except Exception as e:
            app.logger.error(f"获取回收站列表失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/trash/permanent_delete', methods=['POST'])
    def api_permanent_delete():
        """永久删除图片API"""
        try:
            data = request.get_json()
            initfile_names = data.get('initfile_names', [])

            if not initfile_names:
                return jsonify({
                    'success': False,
                    'error': '未提供图片文件名'
                }), 400

            # 执行永久删除
            result = image_manager.permanent_delete_images(initfile_names)

            return jsonify({
                'success': True,
                'data': result
            })

        except Exception as e:
            app.logger.error(f"永久删除图片失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/admin/users')
    def api_get_all_users():
        """获取所有用户状态API（管理员功能）"""
        try:
            user_ip = getattr(request, 'user_ip', 'unknown')
            is_admin = getattr(request, 'is_admin', False)

            if not is_admin:
                return jsonify({
                    'success': False,
                    'error': '权限不足，需要管理员权限'
                }), 403

            users_status = user_manager.get_all_users_status()
            annotation_stats = concurrent_manager.get_annotation_statistics()

            return jsonify({
                'success': True,
                'data': {
                    'users': users_status,
                    'statistics': annotation_stats
                }
            })

        except Exception as e:
            app.logger.error(f"获取用户状态失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/admin/force_release', methods=['POST'])
    def api_force_release_user():
        """强制释放用户分片API（管理员功能）"""
        try:
            user_ip = getattr(request, 'user_ip', 'unknown')
            is_admin = getattr(request, 'is_admin', False)

            if not is_admin:
                return jsonify({
                    'success': False,
                    'error': '权限不足，需要管理员权限'
                }), 403

            data = request.get_json()
            target_user_ip = data.get('target_user_ip', '')

            if not target_user_ip:
                return jsonify({
                    'success': False,
                    'error': '未提供目标用户IP'
                }), 400

            success = user_manager.force_release_user_chunks(target_user_ip)

            return jsonify({
                'success': True,
                'data': {
                    'released': success,
                    'target_user': target_user_ip
                }
            })

        except Exception as e:
            app.logger.error(f"强制释放用户分片失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/statistics')
    def api_get_statistics():
        """获取统计信息API"""
        try:
            stats = data_manager.get_statistics()
            return jsonify({
                'success': True,
                'data': stats
            })

        except Exception as e:
            app.logger.error(f"获取统计信息失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/image/<path:filename>')
    def api_get_image(filename):
        """获取图片文件（优先返回标注图片）"""
        try:
            # 获取图片信息
            image_info = image_manager.get_image_info(filename)
            if not image_info:
                return jsonify({
                    'success': False,
                    'error': '图片记录不存在'
                }), 404

            # 优先使用标注图片
            intefile_name = image_info.get('intefile_name', '')
            if intefile_name:
                intefile_path = ImageManagerConfig.INTEFILE_DIR / intefile_name
                if intefile_path.exists() and intefile_path.is_file():
                    return send_file(str(intefile_path))

            # 如果标注图片不存在，使用原始图片
            initfile_path = ImageManagerConfig.INITFILE_DIR / filename
            if initfile_path.exists() and initfile_path.is_file():
                return send_file(str(initfile_path))

            return jsonify({
                'success': False,
                'error': '图片文件不存在'
            }), 404

        except Exception as e:
            app.logger.error(f"获取图片文件失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500


    @app.route('/api/client-log', methods=['POST'])
    def api_client_log():
        """接收前端日志并写入服务器日志

        请求体JSON示例：
        {
            "level": "info|warn|error|debug",
            "messages": ["msg1", "msg2"],
            "url": "http://.../images",
            "user_agent": "...",
            "timestamp": 1690000000000
        }
        """
        try:
            data = request.get_json(force=True, silent=True) or {}
            level = str(data.get('level', 'info')).lower()
            messages = data.get('messages', []) or []
            url = data.get('url', '')
            ua = data.get('user_agent', '')
            ts = data.get('timestamp', '')

            text = ' | '.join(str(m) for m in messages)
            log_line = f"[CLIENT][{level}] {text} (url={url}, ua={ua}, ts={ts})"

            if level in ('error',):
                app.logger.error(log_line)
            elif level in ('warn', 'warning'):
                app.logger.warning(log_line)
            elif level in ('debug',):
                app.logger.debug(log_line)
            else:
                app.logger.info(log_line)

            return jsonify({
                'success': True
            })
        except Exception as e:
            app.logger.error(f"记录前端日志失败: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.errorhandler(404)
    def not_found(error):
        """404错误处理"""
        return render_template('error.html',
                             error_code=404,
                             error_message='页面未找到'), 404

    @app.errorhandler(500)
    def internal_error(error):
        """500错误处理"""
        return render_template('error.html',
                             error_code=500,
                             error_message='服务器内部错误'), 500

    return app

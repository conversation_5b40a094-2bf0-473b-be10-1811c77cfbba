"""
并发控制和数据同步模块

负责文件锁管理、冲突检测和操作日志记录。
"""

import os
import time
import fcntl
import logging
import threading
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
from .config import ImageManagerConfig


class ConcurrentManager:
    """并发控制管理器
    
    负责文件锁管理、冲突检测和操作日志记录。
    """
    
    def __init__(self):
        """初始化并发控制管理器"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._file_locks = {}
        self._lock = threading.Lock()
        
        # 创建日志文件目录
        self.log_dir = ImageManagerConfig.DATA_ROOT / "logs"
        self.log_dir.mkdir(exist_ok=True)
        
        # 操作日志文件路径
        self.annotation_log_path = self.log_dir / "annotation_log.csv"
        self.user_sessions_path = self.log_dir / "user_sessions.csv"
        
        # 初始化日志文件
        self._init_log_files()
        
        self.logger.info("并发控制管理器初始化完成")
    
    def _init_log_files(self) -> None:
        """初始化日志文件"""
        # 初始化标注操作日志
        if not self.annotation_log_path.exists():
            annotation_df = pd.DataFrame(columns=[
                'timestamp', 'user_ip', 'action', 'image_names', 'count', 'chunk_id'
            ])
            annotation_df.to_csv(self.annotation_log_path, index=False)
            self.logger.info("创建标注操作日志文件")
        
        # 初始化用户会话日志
        if not self.user_sessions_path.exists():
            sessions_df = pd.DataFrame(columns=[
                'user_ip', 'login_time', 'last_activity', 'is_admin', 'assigned_chunks', 'annotated_count'
            ])
            sessions_df.to_csv(self.user_sessions_path, index=False)
            self.logger.info("创建用户会话日志文件")
    
    @contextmanager
    def file_lock(self, file_path: Path):
        """文件锁上下文管理器
        
        Args:
            file_path: 要锁定的文件路径
        """
        lock_file_path = file_path.with_suffix(file_path.suffix + '.lock')
        
        try:
            # 创建锁文件
            lock_file = open(lock_file_path, 'w')
            
            # 获取文件锁
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX)
            self.logger.debug(f"获取文件锁: {file_path}")
            
            yield
            
        except Exception as e:
            self.logger.error(f"文件锁操作失败: {file_path}, 错误: {e}")
            raise
        finally:
            try:
                # 释放文件锁
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
                lock_file.close()
                
                # 删除锁文件
                if lock_file_path.exists():
                    lock_file_path.unlink()
                
                self.logger.debug(f"释放文件锁: {file_path}")
            except Exception as e:
                self.logger.error(f"释放文件锁失败: {file_path}, 错误: {e}")
    
    def safe_csv_operation(self, csv_path: Path, operation_func, *args, **kwargs):
        """安全的CSV文件操作
        
        Args:
            csv_path: CSV文件路径
            operation_func: 操作函数
            *args, **kwargs: 传递给操作函数的参数
            
        Returns:
            操作函数的返回值
        """
        with self.file_lock(csv_path):
            return operation_func(*args, **kwargs)
    
    def check_image_conflicts(self, user_ip: str, image_names: List[str]) -> List[str]:
        """检查图片操作冲突
        
        Args:
            user_ip: 用户IP地址
            image_names: 要操作的图片名称列表
            
        Returns:
            List[str]: 存在冲突的图片名称列表
        """
        conflicts = []
        
        try:
            # 读取最近的操作日志
            if self.annotation_log_path.exists():
                df = pd.read_csv(self.annotation_log_path)
                
                # 检查最近5分钟内的操作
                current_time = time.time()
                recent_time = current_time - 300  # 5分钟
                
                recent_ops = df[df['timestamp'] > recent_time]
                
                for image_name in image_names:
                    # 检查是否有其他用户最近操作过这张图片
                    image_ops = recent_ops[
                        (recent_ops['image_names'].str.contains(image_name, na=False)) &
                        (recent_ops['user_ip'] != user_ip)
                    ]
                    
                    if len(image_ops) > 0:
                        conflicts.append(image_name)
                        
        except Exception as e:
            self.logger.error(f"检查图片冲突失败: {e}")
        
        return conflicts
    
    def log_annotation_operation(self, user_ip: str, action: str, image_names: List[str], 
                                chunk_id: Optional[int] = None) -> None:
        """记录标注操作日志
        
        Args:
            user_ip: 用户IP地址
            action: 操作类型 (mark_valid, soft_delete, etc.)
            image_names: 操作的图片名称列表
            chunk_id: 分片ID（可选）
        """
        try:
            log_entry = {
                'timestamp': time.time(),
                'user_ip': user_ip,
                'action': action,
                'image_names': ','.join(image_names),
                'count': len(image_names),
                'chunk_id': chunk_id or ''
            }
            
            # 使用文件锁安全地写入日志
            def append_log():
                if self.annotation_log_path.exists():
                    df = pd.read_csv(self.annotation_log_path)
                else:
                    df = pd.DataFrame(columns=[
                        'timestamp', 'user_ip', 'action', 'image_names', 'count', 'chunk_id'
                    ])
                
                new_df = pd.concat([df, pd.DataFrame([log_entry])], ignore_index=True)
                new_df.to_csv(self.annotation_log_path, index=False)
            
            self.safe_csv_operation(self.annotation_log_path, append_log)
            self.logger.debug(f"记录标注操作: {user_ip} - {action} - {len(image_names)}张图片")
            
        except Exception as e:
            self.logger.error(f"记录标注操作失败: {e}")
    
    def log_user_session(self, user_info: Dict[str, Any]) -> None:
        """记录用户会话信息
        
        Args:
            user_info: 用户信息字典
        """
        try:
            def update_session():
                if self.user_sessions_path.exists():
                    df = pd.read_csv(self.user_sessions_path)
                else:
                    df = pd.DataFrame(columns=[
                        'user_ip', 'login_time', 'last_activity', 'is_admin', 
                        'assigned_chunks', 'annotated_count'
                    ])
                
                # 更新或添加用户会话记录
                user_ip = user_info['ip']
                mask = df['user_ip'] == user_ip
                
                session_data = {
                    'user_ip': user_ip,
                    'login_time': user_info['login_time'],
                    'last_activity': user_info['last_activity'],
                    'is_admin': user_info['is_admin'],
                    'assigned_chunks': ','.join(map(str, user_info['assigned_chunks'])),
                    'annotated_count': user_info['annotated_count']
                }
                
                if mask.any():
                    # 更新现有记录
                    for col, value in session_data.items():
                        df.loc[mask, col] = value
                else:
                    # 添加新记录
                    new_df = pd.concat([df, pd.DataFrame([session_data])], ignore_index=True)
                    df = new_df
                
                df.to_csv(self.user_sessions_path, index=False)
            
            self.safe_csv_operation(self.user_sessions_path, update_session)
            
        except Exception as e:
            self.logger.error(f"记录用户会话失败: {e}")
    
    def get_annotation_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取标注统计信息
        
        Args:
            hours: 统计时间范围（小时）
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if not self.annotation_log_path.exists():
                return {'total_operations': 0, 'user_stats': {}}
            
            df = pd.read_csv(self.annotation_log_path)
            
            # 过滤时间范围
            current_time = time.time()
            start_time = current_time - (hours * 3600)
            recent_ops = df[df['timestamp'] > start_time]
            
            # 统计信息
            stats = {
                'total_operations': len(recent_ops),
                'total_images': recent_ops['count'].sum(),
                'user_stats': {},
                'action_stats': recent_ops['action'].value_counts().to_dict()
            }
            
            # 按用户统计
            user_groups = recent_ops.groupby('user_ip')
            for user_ip, group in user_groups:
                stats['user_stats'][user_ip] = {
                    'operations': len(group),
                    'images': group['count'].sum(),
                    'actions': group['action'].value_counts().to_dict()
                }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取标注统计失败: {e}")
            return {'total_operations': 0, 'user_stats': {}}
    
    def cleanup_old_logs(self, days: int = 30) -> None:
        """清理旧的日志记录
        
        Args:
            days: 保留天数
        """
        try:
            cutoff_time = time.time() - (days * 24 * 3600)
            
            # 清理标注操作日志
            if self.annotation_log_path.exists():
                def cleanup_annotation_log():
                    df = pd.read_csv(self.annotation_log_path)
                    df_cleaned = df[df['timestamp'] > cutoff_time]
                    df_cleaned.to_csv(self.annotation_log_path, index=False)
                    return len(df) - len(df_cleaned)
                
                removed_count = self.safe_csv_operation(
                    self.annotation_log_path, cleanup_annotation_log
                )
                self.logger.info(f"清理标注日志，删除 {removed_count} 条记录")
            
        except Exception as e:
            self.logger.error(f"清理日志失败: {e}")

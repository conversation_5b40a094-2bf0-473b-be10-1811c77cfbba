"""
CSV数据管理模块

负责管理query_results.csv文件的读取、写入和valid列的操作。
"""

import pandas as pd
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from .config import ImageManagerConfig


class DataManager:
    """CSV数据管理器
    
    负责管理query_results.csv文件的所有操作，包括读取、写入、
    状态更新和数据验证等功能。
    """
    
    def __init__(self, csv_path: Optional[Path] = None):
        """初始化数据管理器
        
        Args:
            csv_path: CSV文件路径，默认使用配置中的路径
        """
        self.csv_path = csv_path or ImageManagerConfig.QUERY_RESULTS_CSV
        self.logger = logging.getLogger(self.__class__.__name__)
        self._df = None
        self._ensure_status_column()
    
    def _ensure_status_column(self) -> None:
        """确保CSV文件中存在status列，并处理从valid列的迁移"""
        try:
            df = pd.read_csv(self.csv_path)

            # 处理列名迁移：将'valid'列重命名为'status'
            if 'valid' in df.columns and 'status' not in df.columns:
                df = df.rename(columns={'valid': 'status'})
                df.to_csv(self.csv_path, index=False)
                self.logger.info("已将'valid'列重命名为'status'列")
            elif 'status' not in df.columns:
                df['status'] = ''  # 添加空的status列
                df.to_csv(self.csv_path, index=False)
                self.logger.info("已在CSV文件中添加status列")
        except FileNotFoundError:
            self.logger.error(f"CSV文件不存在: {self.csv_path}")
            raise
        except Exception as e:
            self.logger.error(f"处理CSV文件时出错: {e}")
            raise
    
    def load_data(self, force_reload: bool = False) -> pd.DataFrame:
        """加载CSV数据
        
        Args:
            force_reload: 是否强制重新加载数据
            
        Returns:
            DataFrame: 加载的数据
        """
        if self._df is None or force_reload:
            try:
                self._df = pd.read_csv(self.csv_path)
                self.logger.info(f"成功加载CSV数据，共{len(self._df)}条记录")
            except Exception as e:
                self.logger.error(f"加载CSV数据失败: {e}")
                raise
        return self._df.copy()
    
    def save_data(self, df: pd.DataFrame) -> None:
        """保存数据到CSV文件
        
        Args:
            df: 要保存的DataFrame
        """
        try:
            df.to_csv(self.csv_path, index=False)
            self._df = df.copy()  # 更新缓存
            self.logger.info(f"成功保存CSV数据，共{len(df)}条记录")
        except Exception as e:
            self.logger.error(f"保存CSV数据失败: {e}")
            raise
    
    def get_images_by_status(self, status: str = '', user_range: tuple = None) -> List[Dict[str, Any]]:
        """根据状态获取图片记录

        Args:
            status: 状态筛选条件，空字符串表示获取所有记录
            user_range: 用户分片范围 (start_index, end_index)

        Returns:
            List[Dict]: 图片记录列表
        """
        df = self.load_data()

        # 应用用户分片过滤
        if user_range:
            start_index, end_index = user_range
            df = df.iloc[start_index:end_index]

        if status == '':
            # 获取所有记录
            filtered_df = df
        elif status == ImageManagerConfig.STATUS_VALID:
            # 获取标记为valid的记录
            filtered_df = df[df['status'] == ImageManagerConfig.STATUS_VALID]
        elif status == 'empty':
            # 获取未标记的记录（status列为空或NaN）
            filtered_df = df[df['status'].isna() | (df['status'] == '')]
        else:
            # 获取指定状态的记录
            filtered_df = df[df['status'] == status]

        records = filtered_df.to_dict('records')
        # 处理NaN值
        for record in records:
            for key, value in record.items():
                if pd.isna(value):
                    record[key] = ''
        return records
    
    def update_image_status(self, initfile_names: List[str], status: str) -> int:
        """更新图片状态
        
        Args:
            initfile_names: 要更新的图片文件名列表
            status: 新的状态值
            
        Returns:
            int: 成功更新的记录数
        """
        df = self.load_data()
        
        # 找到匹配的记录
        mask = df['initfile_name'].isin(initfile_names)
        matched_count = mask.sum()
        
        if matched_count > 0:
            # 更新状态
            df.loc[mask, 'status'] = status
            self.save_data(df)
            self.logger.info(f"成功更新{matched_count}条记录的状态为: {status}")
        else:
            self.logger.warning(f"未找到匹配的记录: {initfile_names}")
        
        return int(matched_count)  # 转换为Python原生int类型，避免JSON序列化错误
    
    def get_image_info(self, initfile_name: str) -> Optional[Dict[str, Any]]:
        """获取单个图片的信息
        
        Args:
            initfile_name: 图片文件名
            
        Returns:
            Optional[Dict]: 图片信息，如果不存在则返回None
        """
        df = self.load_data()
        matches = df[df['initfile_name'] == initfile_name]
        
        if len(matches) > 0:
            record = matches.iloc[0].to_dict()
            # 处理NaN值
            for key, value in record.items():
                if pd.isna(value):
                    record[key] = ''
            return record
        return None
    
    def get_deleted_images(self) -> Set[str]:
        """获取已删除的图片文件名集合

        Returns:
            Set[str]: 已删除的图片文件名集合
        """
        df = self.load_data()
        deleted_df = df[df['status'] == ImageManagerConfig.STATUS_INVALID]
        return set(deleted_df['initfile_name'].tolist())
    
    def get_statistics(self) -> Dict[str, int]:
        """获取数据统计信息
        
        Returns:
            Dict[str, int]: 统计信息
        """
        df = self.load_data()
        
        total = len(df)
        valid_count = len(df[df['status'] == ImageManagerConfig.STATUS_VALID])
        invalid_count = len(df[df['status'] == ImageManagerConfig.STATUS_INVALID])
        deleted_count = len(df[df['status'] == ImageManagerConfig.STATUS_DELETED])
        empty_count = len(df[df['status'].isna() | (df['status'] == '')])

        return {
            'total': total,
            'valid': valid_count,
            'invalid': invalid_count,
            'deleted': deleted_count,
            'empty': empty_count
        }
    
    def backup_csv(self, backup_path: Optional[Path] = None) -> Path:
        """备份CSV文件
        
        Args:
            backup_path: 备份文件路径，默认在同目录下添加时间戳
            
        Returns:
            Path: 备份文件路径
        """
        if backup_path is None:
            timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            backup_path = self.csv_path.parent / f"query_results_backup_{timestamp}.csv"
        
        try:
            df = self.load_data()
            df.to_csv(backup_path, index=False)
            self.logger.info(f"CSV文件已备份到: {backup_path}")
            return backup_path
        except Exception as e:
            self.logger.error(f"备份CSV文件失败: {e}")
            raise

"""
图片管理Web应用配置模块

定义Web应用的配置参数和常量。
"""

import os
from pathlib import Path
from typing import Dict, Any


class ImageManagerConfig:
    """图片管理应用配置类"""
    
    # 项目根目录
    PROJECT_ROOT = Path(__file__).parent.parent.parent
    
    # 数据目录配置
    DATA_ROOT = PROJECT_ROOT / "data" / "raw" / "backup"
    INITFILE_DIR = DATA_ROOT / "initfile"  # 原始图片目录
    INTEFILE_DIR = DATA_ROOT / "intefile"  # 标注图片目录
    QUERY_RESULTS_CSV = DATA_ROOT / "query_results.csv"  # 记录文件
    
    # 缩略图配置
    THUMBNAIL_SIZE = (200, 200)  # 缩略图尺寸
    THUMBNAIL_QUALITY = 85  # 缩略图质量
    
    # 分页配置
    DEFAULT_PAGE_SIZE = 20
    PAGE_SIZE_OPTIONS = [20, 30, 50]
    
    # Web应用配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'
    HOST = os.environ.get('HOST', '0.0.0.0')  # 支持局域网访问
    PORT = int(os.environ.get('PORT', 5000))

    # 多用户配置
    CHUNK_SIZE = 200  # 每个分片的图片数量
    SESSION_TIMEOUT = 300  # 用户会话超时时间（秒）
    ADMIN_IP = os.environ.get('ADMIN_IP', '*************')  # 管理员IP地址
    MAX_CONCURRENT_USERS = 10  # 最大并发用户数
    
    # 图片文件扩展名
    ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    # 状态常量
    STATUS_VALID = 'valid'
    STATUS_INVALID = 'invalid'
    STATUS_DELETED = 'deleted'
    STATUS_EMPTY = ''
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    @classmethod
    def get_config_dict(cls) -> Dict[str, Any]:
        """获取配置字典
        
        Returns:
            配置参数字典
        """
        return {
            'data_root': str(cls.DATA_ROOT),
            'initfile_dir': str(cls.INITFILE_DIR),
            'intefile_dir': str(cls.INTEFILE_DIR),
            'query_results_csv': str(cls.QUERY_RESULTS_CSV),
            'thumbnail_size': cls.THUMBNAIL_SIZE,
            'thumbnail_quality': cls.THUMBNAIL_QUALITY,
            'default_page_size': cls.DEFAULT_PAGE_SIZE,
            'page_size_options': cls.PAGE_SIZE_OPTIONS,
            'secret_key': cls.SECRET_KEY,
            'debug': cls.DEBUG,
            'host': cls.HOST,
            'port': cls.PORT,
            'allowed_extensions': cls.ALLOWED_EXTENSIONS,
            'status_valid': cls.STATUS_VALID,
            'status_invalid': cls.STATUS_INVALID,
            'status_deleted': cls.STATUS_DELETED,
            'status_empty': cls.STATUS_EMPTY,
            'log_level': cls.LOG_LEVEL,
            'log_format': cls.LOG_FORMAT,
            'chunk_size': cls.CHUNK_SIZE,
            'session_timeout': cls.SESSION_TIMEOUT,
            'admin_ip': cls.ADMIN_IP,
            'max_concurrent_users': cls.MAX_CONCURRENT_USERS
        }
    
    @classmethod
    def validate_directories(cls) -> bool:
        """验证必要目录是否存在
        
        Returns:
            bool: 目录是否都存在
        """
        required_dirs = [cls.DATA_ROOT, cls.INITFILE_DIR, cls.INTEFILE_DIR]
        for dir_path in required_dirs:
            if not dir_path.exists():
                return False
        return True
    
    @classmethod
    def validate_files(cls) -> bool:
        """验证必要文件是否存在
        
        Returns:
            bool: 文件是否都存在
        """
        return cls.QUERY_RESULTS_CSV.exists()

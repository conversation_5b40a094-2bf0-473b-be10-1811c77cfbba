"""
图片文件管理模块

负责图片文件的读取、缩略图生成、软删除和永久删除等操作。
"""

import os
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from PIL import Image, ImageOps
import base64
from io import BytesIO
import pandas as pd

from .config import ImageManagerConfig
from .data_manager import DataManager


class ImageManager:
    """图片文件管理器
    
    负责管理图片文件的所有操作，包括读取、缩略图生成、
    软删除、永久删除等功能。
    """
    
    def __init__(self, data_manager: Optional[DataManager] = None):
        """初始化图片管理器
        
        Args:
            data_manager: 数据管理器实例，默认创建新实例
        """
        self.config = ImageManagerConfig
        self.data_manager = data_manager or DataManager()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 创建回收站目录
        self.trash_dir = self.config.DATA_ROOT / "trash"
        self.trash_dir.mkdir(exist_ok=True)
        
        # 创建缩略图缓存目录
        self.thumbnail_cache_dir = self.config.DATA_ROOT / "thumbnails"
        self.thumbnail_cache_dir.mkdir(exist_ok=True)
    
    def get_image_list(self, status_filter: str = '', page: int = 1,
                      page_size: int = None, user_range: tuple = None) -> Dict[str, Any]:
        """获取图片列表

        Args:
            status_filter: 状态筛选条件
            page: 页码（从1开始）
            page_size: 每页大小
            user_range: 用户分片范围 (start_index, end_index)

        Returns:
            Dict: 包含图片列表和分页信息的字典
        """
        if page_size is None:
            page_size = self.config.DEFAULT_PAGE_SIZE

        # 获取图片记录（应用用户分片过滤）
        image_records = self.data_manager.get_images_by_status(status_filter, user_range)
        
        # 过滤存在的图片文件（检查标注图片是否存在）
        existing_images = []
        for record in image_records:
            intefile_name = record.get('intefile_name', '')
            if intefile_name:
                intefile_path = self.config.INTEFILE_DIR / intefile_name
                if intefile_path.exists():
                    existing_images.append(record)
            else:
                # 如果没有标注文件名，检查原始文件是否存在
                initfile_path = self.config.INITFILE_DIR / record['initfile_name']
                if initfile_path.exists():
                    existing_images.append(record)

        self.logger.info(f"总记录数: {len(image_records)}, 存在的图片数: {len(existing_images)}")
        
        # 分页处理
        total_count = len(existing_images)
        total_pages = (total_count + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_images = existing_images[start_idx:end_idx]
        
        return {
            'images': page_images,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'page_size': page_size,
                'total_count': total_count,
                'has_prev': page > 1,
                'has_next': page < total_pages
            }
        }
    
    def generate_thumbnail(self, image_path: Path, force_regenerate: bool = False) -> str:
        """生成图片缩略图并返回base64编码
        
        Args:
            image_path: 图片文件路径
            force_regenerate: 是否强制重新生成缩略图
            
        Returns:
            str: base64编码的缩略图数据
        """
        # 生成缩略图缓存文件名
        cache_filename = f"{image_path.stem}_thumb.jpg"
        cache_path = self.thumbnail_cache_dir / cache_filename
        
        # 检查缓存是否存在且有效
        if cache_path.exists() and not force_regenerate:
            if cache_path.stat().st_mtime >= image_path.stat().st_mtime:
                try:
                    with open(cache_path, 'rb') as f:
                        return base64.b64encode(f.read()).decode('utf-8')
                except Exception as e:
                    self.logger.warning(f"读取缓存缩略图失败: {e}")
        
        # 生成新的缩略图
        try:
            with Image.open(image_path) as img:
                # 转换为RGB模式（处理RGBA等格式）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 生成缩略图，保持宽高比
                img.thumbnail(self.config.THUMBNAIL_SIZE, Image.Resampling.LANCZOS)
                
                # 保存到缓存
                img.save(cache_path, 'JPEG', quality=self.config.THUMBNAIL_QUALITY)
                
                # 转换为base64
                buffer = BytesIO()
                img.save(buffer, 'JPEG', quality=self.config.THUMBNAIL_QUALITY)
                thumbnail_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                return thumbnail_data
                
        except Exception as e:
            self.logger.error(f"生成缩略图失败 {image_path}: {e}")
            return ""
    
    def get_image_info(self, initfile_name: str) -> Optional[Dict[str, Any]]:
        """获取图片详细信息
        
        Args:
            initfile_name: 图片文件名
            
        Returns:
            Optional[Dict]: 图片信息，包含文件信息和缩略图
        """
        # 获取数据库记录
        record = self.data_manager.get_image_info(initfile_name)
        if not record:
            return None
        
        # 获取文件路径
        initfile_path = self.config.INITFILE_DIR / initfile_name
        intefile_name = record.get('intefile_name', '')
        intefile_path = self.config.INTEFILE_DIR / intefile_name if intefile_name else None

        # 优先使用标注图片，如果不存在则使用原始图片
        display_path = None
        if intefile_path and intefile_path.exists():
            display_path = intefile_path
        elif initfile_path.exists():
            display_path = initfile_path
        else:
            return None

        # 获取文件信息
        file_stat = display_path.stat()

        # 生成缩略图（使用要显示的图片）
        thumbnail = self.generate_thumbnail(display_path)
        
        # 处理NaN值
        clean_record = {}
        for key, value in record.items():
            if pd.isna(value):
                clean_record[key] = ''
            else:
                clean_record[key] = value

        return {
            **clean_record,
            'file_size': file_stat.st_size,
            'file_mtime': file_stat.st_mtime,
            'initfile_exists': initfile_path.exists(),
            'intefile_exists': intefile_path.exists() if intefile_path else False,
            'display_path': str(display_path),  # 添加实际显示的文件路径
            'is_intefile': display_path == intefile_path,  # 标记是否显示的是标注图片
            'thumbnail': thumbnail
        }
    
    def soft_delete_images(self, initfile_names: List[str]) -> Dict[str, Any]:
        """软删除图片（移动到回收站）
        
        Args:
            initfile_names: 要删除的图片文件名列表
            
        Returns:
            Dict: 删除结果统计
        """
        success_count = 0
        failed_files = []
        
        for initfile_name in initfile_names:
            try:
                # 获取文件路径
                initfile_path = self.config.INITFILE_DIR / initfile_name
                record = self.data_manager.get_image_info(initfile_name)
                
                if not record:
                    failed_files.append(f"{initfile_name}: 记录不存在")
                    continue
                
                intefile_name = record.get('intefile_name', '')
                intefile_path = self.config.INTEFILE_DIR / intefile_name if intefile_name else None
                
                # 移动文件到回收站
                if initfile_path.exists():
                    trash_initfile = self.trash_dir / f"init_{initfile_name}"
                    shutil.move(str(initfile_path), str(trash_initfile))
                
                if intefile_path and intefile_path.exists():
                    trash_intefile = self.trash_dir / f"inte_{intefile_name}"
                    shutil.move(str(intefile_path), str(trash_intefile))
                
                success_count += 1
                
            except Exception as e:
                failed_files.append(f"{initfile_name}: {str(e)}")
                self.logger.error(f"软删除图片失败 {initfile_name}: {e}")
        
        return {
            'success_count': success_count,
            'failed_count': len(failed_files),
            'failed_files': failed_files
        }
    
    def permanent_delete_images(self, initfile_names: List[str]) -> Dict[str, Any]:
        """永久删除图片
        
        Args:
            initfile_names: 要删除的图片文件名列表
            
        Returns:
            Dict: 删除结果统计
        """
        success_count = 0
        failed_files = []
        
        for initfile_name in initfile_names:
            try:
                record = self.data_manager.get_image_info(initfile_name)
                if not record:
                    failed_files.append(f"{initfile_name}: 记录不存在")
                    continue
                
                intefile_name = record.get('intefile_name', '')
                
                # 删除回收站中的文件
                trash_initfile = self.trash_dir / f"init_{initfile_name}"
                trash_intefile = self.trash_dir / f"inte_{intefile_name}" if intefile_name else None
                
                if trash_initfile.exists():
                    trash_initfile.unlink()
                
                if trash_intefile and trash_intefile.exists():
                    trash_intefile.unlink()
                
                # 删除缩略图缓存
                cache_filename = f"{Path(initfile_name).stem}_thumb.jpg"
                cache_path = self.thumbnail_cache_dir / cache_filename
                if cache_path.exists():
                    cache_path.unlink()
                
                success_count += 1
                
            except Exception as e:
                failed_files.append(f"{initfile_name}: {str(e)}")
                self.logger.error(f"永久删除图片失败 {initfile_name}: {e}")
        
        # 更新数据库状态为deleted（不删除CSV记录）
        if success_count > 0:
            updated_names = [name for name in initfile_names
                           if name not in [f.split(':')[0] for f in failed_files]]
            self.data_manager.update_image_status(updated_names,
                                                self.config.STATUS_DELETED)
        
        return {
            'success_count': success_count,
            'failed_count': len(failed_files),
            'failed_files': failed_files
        }
    
    def get_trash_images(self, page: int = 1, page_size: int = None) -> Dict[str, Any]:
        """获取回收站中的图片列表
        
        Args:
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 回收站图片列表和分页信息
        """
        if page_size is None:
            page_size = self.config.DEFAULT_PAGE_SIZE
        
        # 获取回收站中的图片文件
        trash_files = []
        for file_path in self.trash_dir.glob("init_*"):
            if file_path.is_file():
                initfile_name = file_path.name[5:]  # 移除"init_"前缀
                trash_files.append(initfile_name)
        
        # 分页处理
        total_count = len(trash_files)
        total_pages = (total_count + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_files = trash_files[start_idx:end_idx]
        
        # 获取详细信息
        images = []
        for initfile_name in page_files:
            record = self.data_manager.get_image_info(initfile_name)
            if record:
                # 生成回收站文件的缩略图
                trash_file_path = self.trash_dir / f"init_{initfile_name}"
                thumbnail = self.generate_thumbnail(trash_file_path) if trash_file_path.exists() else ""
                
                images.append({
                    **record,
                    'thumbnail': thumbnail,
                    'in_trash': True
                })
        
        return {
            'images': images,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'page_size': page_size,
                'total_count': total_count,
                'has_prev': page > 1,
                'has_next': page < total_pages
            }
        }

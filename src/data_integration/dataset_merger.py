#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据集合并器
负责合并多个数据集并应用类别映射
"""

import sys
import shutil
from pathlib import Path
from typing import Dict, List, Tuple
from tqdm import tqdm
import hashlib
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.basic.logger import get_logger
from src.basic.exceptions import DatasetMergeError
from src.utils.file_utils import ensure_dir, copy_file, get_image_files, get_label_files
from .class_mapper import ClassMapper

logger = get_logger(__name__)


class DatasetMerger:
    """数据集合并器"""
    
    def __init__(self, config: Dict, class_mapper: ClassMapper):
        """
        初始化数据集合并器
        
        Args:
            config: 数据整合配置
            class_mapper: 类别映射器实例
        """
        self.config = config
        self.class_mapper = class_mapper
        self.merger_config = config.get('dataset_merger', {})
        
        # 配置参数
        self.output_dir = Path(self.merger_config.get('output_dir', 'data/integrated'))
        self.file_naming = self.merger_config.get('file_naming', {})
        self.conflict_resolution = self.merger_config.get('conflict_resolution', {})
        self.progress_bar = self.merger_config.get('progress_bar', True)
        self.verbose = self.merger_config.get('verbose', True)
        self.backup_config = self.merger_config.get('backup', {})
        
        # 运行时状态
        self.merged_files: Dict[str, List[str]] = {'images': [], 'labels': []}
        self.file_mapping: Dict[str, str] = {}  # 原始文件 -> 新文件名映射
        self.statistics: Dict = {}
        
        # 标签处理统计
        self.label_stats: Dict[str, int] = {'deleted': 0, 'modified': 0}
        
        # 类别样本统计 - 记录每个样本的详细信息
        self.class_sample_tracking: Dict[str, List[Dict]] = {}  # class_id -> [sample_info]
        self.dataset_class_stats: Dict[str, Dict] = {}  # dataset_name -> class_stats
        
        # 空标签文件处理
        self.empty_label_files: List[Tuple[Path, Path, Path]] = []  # (original_image, target_image, target_label)
        self.empty_label_keep_ratio = self.merger_config.get('empty_label_keep_ratio', 0.1)  # 保留10%
        
    def merge_datasets(self, dataset_configs: List[Dict]) -> bool:
        """
        合并多个数据集
        
        Args:
            dataset_configs: 数据集配置列表（已经过滤掉已处理的数据集）
            
        Returns:
            bool: 是否成功
        """
        logger.info(f"开始合并 {len(dataset_configs)} 个数据集")
        
        if not dataset_configs:
            logger.info("没有需要合并的数据集，跳过合并步骤")
            return True
        
        # 准备输出目录
        self._prepare_output_directory()
        
        # 创建备份（如果启用）
        if self.backup_config.get('enabled', False):
            self._create_backup()
            
        try:
            # 处理每个数据集
            for i, dataset_config in enumerate(dataset_configs):
                if not dataset_config.get('enabled', True):
                    logger.info(f"跳过未启用的数据集: {dataset_config.get('name', '')}")
                    continue
                    
                logger.info(f"处理数据集 {i+1}/{len(dataset_configs)}: {dataset_config.get('name', '')}")
                success = self._process_single_dataset(dataset_config)
                
                if not success:
                    raise DatasetMergeError(
                        [cfg.get('name', '') for cfg in dataset_configs],
                        f"处理数据集 {dataset_config.get('name', '')} 失败"
                    )
                    
            # 处理空标签文件
            self._process_empty_label_files()
            
            # 生成统计信息
            self._generate_statistics()
            
            logger.info("数据集合并完成")
            return True
            
        except Exception as e:
            logger.error(f"数据集合并失败: {e}")
            # 清理不完整的输出
            self._cleanup_on_failure()
            return False
            
    def _prepare_output_directory(self):
        """准备输出目录"""
        # 创建主输出目录
        ensure_dir(self.output_dir)
        
        # 创建子目录
        for subdir in ['images', 'labels']:
            ensure_dir(self.output_dir / subdir)
            
        logger.info(f"输出目录准备完成: {self.output_dir}")
        
    def _create_backup(self):
        """创建备份"""
        backup_dir = Path(self.backup_config.get('backup_dir', 'data/backup/integration'))
        
        if self.output_dir.exists():
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = backup_dir / f"integration_backup_{timestamp}"
            
            ensure_dir(backup_path.parent)
            shutil.copytree(self.output_dir, backup_path)
            logger.info(f"创建备份: {backup_path}")
            
    def _process_single_dataset(self, dataset_config: Dict) -> bool:
        """
        处理单个数据集
        
        Args:
            dataset_config: 数据集配置
            
        Returns:
            bool: 是否成功
        """
        dataset_name = dataset_config.get('name', '')
        dataset_path = Path(dataset_config.get('path', ''))
        
        if not dataset_path.exists():
            logger.error(f"数据集路径不存在: {dataset_path}")
            return False
            
        # 获取类别映射信息
        class_mapping = self.class_mapper.get_class_mapping(dataset_name)
        if not class_mapping:
            logger.error(f"未找到数据集 {dataset_name} 的类别映射信息")
            return False
            
        # 查找图像和标签文件
        image_files = self._find_dataset_files(dataset_path, 'images')
        label_files = self._find_dataset_files(dataset_path, 'labels')
        
        # 统计每个类别的样本数量
        class_sample_counts = self._count_class_samples(label_files, class_mapping)
        
        # 输出文件数量和类别样本统计
        logger.info(f"找到 {len(image_files)} 个图像文件, {len(label_files)} 个标签文件")
        
        # 输出每个最终类别的样本数量
        mapped_classes = class_mapping.get('mapped_classes', {})
        for source_id, mapping_info in mapped_classes.items():
            target_name = mapping_info.get('target_name', '')
            target_id = mapping_info.get('target_id', -1)
            sample_count = class_sample_counts.get(int(source_id), 0)
            logger.info(f"  类别 {target_name} (ID: {target_id}): {sample_count} 个样本")
        
        # 处理文件对
        file_pairs = self._match_image_label_pairs(image_files, label_files)
        
        # 重置当前数据集的标签处理统计
        self.label_stats = {'deleted': 0, 'modified': 0}
        
        if self.progress_bar:
            file_pairs = tqdm(file_pairs, desc=f"处理 {dataset_name}")
            
        success_count = 0
        total_deleted = 0
        total_modified = 0
        
        for image_file, label_file in file_pairs:
            result = self._process_file_pair(dataset_name, image_file, label_file, class_mapping)
            if result:
                success_count += 1
                # 如果需要获取标签处理统计，可以修改_process_file_pair返回更多信息
                
        logger.info(f"数据集 {dataset_name} 处理完成: {success_count}/{len(file_pairs)} 个文件对成功")
        
        # 输出标签处理统计汇总
        if self.label_stats['deleted'] > 0 or self.label_stats['modified'] > 0:
            logger.info(f"标签处理统计: 删除 {self.label_stats['deleted']} 个标签, 修改 {self.label_stats['modified']} 个标签")
            
        # 生成当前数据集的类别统计
        self._generate_dataset_class_stats(dataset_name, class_mapping)
            
        return success_count > 0
        
    def _find_dataset_files(self, dataset_path: Path, file_type: str) -> List[Path]:
        """
        查找数据集中的文件
        
        Args:
            dataset_path: 数据集路径
            file_type: 文件类型 ('images' 或 'labels')
            
        Returns:
            List[Path]: 文件路径列表
        """
        files = []
        
        # 标准目录结构
        type_dir = dataset_path / file_type
        if type_dir.exists():
            if file_type == 'images':
                files.extend(get_image_files(type_dir, recursive=True))
            else:
                files.extend(get_label_files(type_dir, recursive=True))
                
        return sorted(files)
        
    def _match_image_label_pairs(self, image_files: List[Path], 
                                label_files: List[Path]) -> List[Tuple[Path, Path]]:
        """
        匹配图像和标签文件对
        
        Args:
            image_files: 图像文件列表
            label_files: 标签文件列表
            
        Returns:
            List[Tuple[Path, Path]]: 文件对列表
        """
        # 创建标签文件的字典（按文件名stem）
        label_dict = {label_file.stem: label_file for label_file in label_files}
        
        pairs = []
        for image_file in image_files:
            image_stem = image_file.stem
            if image_stem in label_dict:
                pairs.append((image_file, label_dict[image_stem]))
            else:
                logger.warning(f"图像文件 {image_file} 没有对应的标签文件")
                
        return pairs
        
    def _count_class_samples(self, label_files: List[Path], class_mapping: Dict) -> Dict[int, int]:
        """
        统计每个类别的样本数量
        
        Args:
            label_files: 标签文件列表
            class_mapping: 类别映射信息
            
        Returns:
            Dict[int, int]: 类别ID到样本数量的映射
        """
        class_counts = {}
        mapped_classes = class_mapping.get('mapped_classes', {})
        
        # 初始化计数器
        for source_id in mapped_classes.keys():
            class_counts[int(source_id)] = 0
            
        # 统计每个标签文件中的类别
        for label_file in label_files:
            try:
                with open(label_file, 'r') as f:
                    for line in f:
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            class_id = int(parts[0])
                            if class_id in class_counts:
                                class_counts[class_id] += 1
            except Exception as e:
                logger.warning(f"读取标签文件失败: {label_file}, 错误: {e}")
                
        return class_counts
        
    def _process_file_pair(self, dataset_name: str, image_file: Path, 
                          label_file: Path, class_mapping: Dict) -> bool:
        """
        处理图像和标签文件对
        
        Args:
            dataset_name: 数据集名称
            image_file: 图像文件路径
            label_file: 标签文件路径
            class_mapping: 类别映射信息
            
        Returns:
            bool: 是否成功
        """
        try:
            # 生成新的文件名
            new_image_name = self._generate_filename(dataset_name, image_file)
            new_label_name = self._generate_filename(dataset_name, label_file)
            
            # 目标路径
            target_image_path = self.output_dir / 'images' / new_image_name
            target_label_path = self.output_dir / 'labels' / new_label_name
            
            # 处理文件名冲突
            target_image_path = self._resolve_filename_conflict(target_image_path)
            target_label_path = self._resolve_filename_conflict(target_label_path)
            
            # 处理标签文件
            success, is_empty = self._process_label_file(label_file, target_label_path, class_mapping)
            if not success:
                logger.error(f"处理标签文件失败: {label_file}")
                return False
                
            # 如果是空标签文件，记录到空标签列表中，但不立即复制图像
            if is_empty:
                self.empty_label_files.append((image_file, target_image_path, target_label_path))
                return True  # 暂时返回成功，稍后会处理空标签文件
                
            # 复制图像文件（只有非空标签文件才立即复制图像）
            if not copy_file(image_file, target_image_path):
                logger.error(f"复制图像文件失败: {image_file}")
                return False
                
            # 记录文件映射
            self.file_mapping[str(image_file)] = str(target_image_path)
            self.file_mapping[str(label_file)] = str(target_label_path)
            
            # 更新统计
            self.merged_files['images'].append(str(target_image_path))
            self.merged_files['labels'].append(str(target_label_path))
            
            return True
            
        except Exception as e:
            logger.error(f"处理文件对失败: {image_file}, {label_file}, 错误: {e}")
            return False
            
    def _generate_filename(self, dataset_name: str, file_path: Path) -> str:
        """
        生成新的文件名
        
        Args:
            dataset_name: 数据集名称
            file_path: 原始文件路径
            
        Returns:
            str: 新文件名
        """
        strategy = self.file_naming.get('strategy', 'dataset_prefix')
        separator = self.file_naming.get('prefix_separator', '_')
        include_original = self.file_naming.get('include_original_name', True)
        
        if strategy == 'dataset_prefix':
            if include_original:
                return f"{dataset_name}{separator}{file_path.name}"
            else:
                # 使用哈希生成唯一名称
                hash_value = hashlib.md5(str(file_path).encode()).hexdigest()[:8]
                return f"{dataset_name}{separator}{hash_value}{file_path.suffix}"
                
        elif strategy == 'sequential':
            # 生成序列号
            existing_count = len([f for f in self.merged_files['images'] + self.merged_files['labels'] 
                                if f.startswith(dataset_name)])
            return f"{dataset_name}{separator}{existing_count:06d}{file_path.suffix}"
            
        elif strategy == 'hash':
            # 使用文件内容哈希
            hash_value = hashlib.md5(str(file_path).encode()).hexdigest()[:12]
            return f"{hash_value}{file_path.suffix}"
            
        else:
            return file_path.name
            
    def _resolve_filename_conflict(self, file_path: Path) -> Path:
        """
        解决文件名冲突
        
        Args:
            file_path: 文件路径
            
        Returns:
            Path: 解决冲突后的文件路径
        """
        conflict_strategy = self.conflict_resolution.get('duplicate_files', 'rename')
        
        if not file_path.exists():
            return file_path
            
        if conflict_strategy == 'skip':
            return file_path
        elif conflict_strategy == 'overwrite':
            return file_path
        elif conflict_strategy == 'rename':
            # 添加数字后缀
            counter = 1
            stem = file_path.stem
            suffix = file_path.suffix
            parent = file_path.parent
            
            while file_path.exists():
                new_name = f"{stem}_{counter:03d}{suffix}"
                file_path = parent / new_name
                counter += 1
                
            return file_path
        else:
            return file_path
            
    def _process_label_file(self, label_file: Path, target_path: Path, 
                           class_mapping: Dict) -> Tuple[bool, bool]:
        """
        处理标签文件，应用类别映射
        
        Args:
            label_file: 原始标签文件路径
            target_path: 目标标签文件路径
            class_mapping: 类别映射信息
            
        Returns:
            Tuple[bool, bool]: (是否成功, 标签文件是否为空)
        """
        try:
            mapped_classes = class_mapping.get('mapped_classes', {})
            
            # 读取原始标签
            with open(label_file, 'r') as f:
                lines = f.readlines()
                
            # 处理每一行
            processed_lines = []
            deleted_count = 0
            modified_count = 0
            
            # 记录当前文件的样本信息
            file_samples = []
            
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 5:
                    old_class_id = int(parts[0])
                    
                    # 查找映射
                    if old_class_id in mapped_classes:
                        new_class_id = mapped_classes[old_class_id]['target_id']
                        target_class_name = mapped_classes[old_class_id]['target_name']
                        
                        # 替换类别ID，保持其他坐标不变
                        parts[0] = str(new_class_id)
                        processed_lines.append(' '.join(parts) + '\n')
                        modified_count += 1
                        
                        # 记录样本信息
                        sample_info = {
                            'image_file': str(target_path).replace('/labels/', '/images/').replace('.txt', '.jpg'),
                            'label_file': str(target_path),
                            'class_id': new_class_id,
                            'class_name': target_class_name,
                            'bbox': parts[1:5]  # x_center, y_center, width, height
                        }
                        file_samples.append(sample_info)
                        
                        # 添加到类别跟踪
                        class_key = str(new_class_id)
                        if class_key not in self.class_sample_tracking:
                            self.class_sample_tracking[class_key] = []
                        self.class_sample_tracking[class_key].append(sample_info)
                        
                    else:
                        # 删除不相关的标签
                        deleted_count += 1
                        
            # 判断是否为空标签文件
            is_empty = len(processed_lines) == 0
            
            # 写入新标签文件
            ensure_dir(target_path.parent)
            with open(target_path, 'w') as f:
                f.writelines(processed_lines)
                
            # 累加到总统计中
            self.label_stats['deleted'] += deleted_count
            self.label_stats['modified'] += modified_count
                
            return True, is_empty
            
        except Exception as e:
            logger.error(f"处理标签文件失败: {e}")
            return False, False
            
    def _generate_dataset_class_stats(self, dataset_name: str, class_mapping: Dict):
        """生成数据集的类别统计"""
        mapped_classes = class_mapping.get('mapped_classes', {})
        dataset_stats = {}
        
        # 统计每个类别的样本数量
        for source_id, mapping_info in mapped_classes.items():
            target_id = mapping_info.get('target_id', -1)
            target_name = mapping_info.get('target_name', '')
            
            # 从样本跟踪中统计该类别的样本数量
            class_key = str(target_id)
            sample_count = len([s for s in self.class_sample_tracking.get(class_key, []) 
                              if dataset_name in s.get('label_file', '')])
            
            dataset_stats[target_name] = {
                'target_id': target_id,
                'sample_count': sample_count,
                'samples': [s for s in self.class_sample_tracking.get(class_key, []) 
                           if dataset_name in s.get('label_file', '')]
            }
            
        self.dataset_class_stats[dataset_name] = dataset_stats
        
    def _process_empty_label_files(self):
        """处理空标签文件，只保留指定比例"""
        if not self.empty_label_files:
            logger.info("没有空标签文件需要处理")
            return
            
        total_empty = len(self.empty_label_files)
        keep_count = max(1, int(total_empty * self.empty_label_keep_ratio))
        
        logger.info(f"发现 {total_empty} 个空标签文件，将保留 {keep_count} 个 ({self.empty_label_keep_ratio*100:.1f}%)")
        
        # 随机选择要保留的空标签文件
        import random
        random.seed(42)  # 确保结果可重现
        random.shuffle(self.empty_label_files)
        
        files_to_keep = self.empty_label_files[:keep_count]
        files_to_remove = self.empty_label_files[keep_count:]
        
        # 处理要保留的文件
        for original_image, target_image_path, target_label_path in files_to_keep:
            # 复制图像文件
            if original_image.exists():
                if copy_file(original_image, target_image_path):
                    # 记录文件映射和统计
                    self.file_mapping[str(original_image)] = str(target_image_path)
                    self.file_mapping[str(target_label_path).replace('/labels/', '/images/').replace('.txt', '.jpg')] = str(target_label_path)
                    self.merged_files['images'].append(str(target_image_path))
                    self.merged_files['labels'].append(str(target_label_path))
                    
        # 删除不保留的空标签文件
        removed_count = 0
        for original_image, target_image_path, target_label_path in files_to_remove:
            try:
                if target_label_path.exists():
                    target_label_path.unlink()
                    removed_count += 1
            except Exception as e:
                logger.warning(f"删除空标签文件失败: {target_label_path}, 错误: {e}")
                
        logger.info(f"已删除 {removed_count} 个多余的空标签文件")
        
    def _generate_statistics(self):
        """生成合并统计信息"""
        # 计算空标签文件统计
        total_empty = len(self.empty_label_files)
        kept_empty = max(1, int(total_empty * self.empty_label_keep_ratio)) if total_empty > 0 else 0
        removed_empty = total_empty - kept_empty
        
        self.statistics = {
            'total_images': len(self.merged_files['images']),
            'total_labels': len(self.merged_files['labels']),
            'file_mapping_count': len(self.file_mapping),
            'output_directory': str(self.output_dir),
            'global_classes': self.class_mapper.get_global_class_mapping(),
            'dataset_class_stats': self.dataset_class_stats,
            'class_sample_tracking': self.class_sample_tracking,
            'label_processing_stats': self.label_stats,
            'empty_label_stats': {
                'total_empty': total_empty,
                'kept_empty': kept_empty,
                'removed_empty': removed_empty,
                'keep_ratio': self.empty_label_keep_ratio
            }
        }
        
        logger.info(f"合并统计: 图像 {self.statistics['total_images']} 个, "
                   f"标签 {self.statistics['total_labels']} 个")
        if total_empty > 0:
            logger.info(f"空标签处理: 总计 {total_empty} 个, 保留 {kept_empty} 个, 删除 {removed_empty} 个")
                   
    def _cleanup_on_failure(self):
        """失败时清理"""
        if self.output_dir.exists():
            try:
                shutil.rmtree(self.output_dir)
                logger.info(f"已清理不完整的输出目录: {self.output_dir}")
            except Exception as e:
                logger.error(f"清理输出目录失败: {e}")
                
    def get_statistics(self) -> Dict:
        """获取合并统计信息"""
        return self.statistics.copy()
        
    def get_file_mapping(self) -> Dict[str, str]:
        """获取文件映射信息"""
        return self.file_mapping.copy() 
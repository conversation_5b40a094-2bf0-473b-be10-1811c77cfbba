#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终数据集构建器
负责构建最终的训练数据集，包括数据分割、YOLO格式转换、统计信息生成等
"""

import sys
import json
import yaml
import shutil
import random
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict
from datetime import datetime
import numpy as np
from tqdm import tqdm

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.basic.logger import get_logger
from src.basic.exceptions import DataIntegrationError
from src.utils.file_utils import ensure_dir, copy_file, get_image_files, get_label_files
from .class_mapper import ClassMapper
from .dataset_merger import DatasetMerger

logger = get_logger(__name__)


class FinalDatasetBuilder:
    """最终数据集构建器"""
    
    def __init__(self, config: Dict, class_mapper: ClassMapper, dataset_merger: DatasetMerger):
        """
        初始化最终数据集构建器
        
        Args:
            config: 数据整合配置
            class_mapper: 类别映射器实例
            dataset_merger: 数据集合并器实例
        """
        self.config = config
        self.class_mapper = class_mapper
        self.dataset_merger = dataset_merger
        
        # 配置参数
        self.final_config = config.get('final_dataset', {})
        self.split_config = config.get('data_split', {})
        self.yolo_config = self.final_config.get('yolo_config', {})
        self.validation_config = self.final_config.get('validation', {})
        self.dataset_info = self.final_config.get('dataset_info', {})
        
        # 输出目录
        self.output_dir = Path(self.final_config.get('output_dir', 'data/final'))
        
        # 运行时状态
        self.split_statistics: Dict[str, int] = {}
        self.final_statistics: Dict = {}
        self.existing_statistics: Dict = {}
        
        # 数据集来源跟踪
        self.dataset_sources: Dict[str, Dict] = {}
        
        # 文件分割映射
        self.file_split_mapping: Dict[str, str] = {}
        
    def build_final_dataset(self, integrated_dir: Path) -> bool:
        """
        构建最终数据集
        
        Args:
            integrated_dir: 整合数据集目录
            
        Returns:
            bool: 是否成功
        """
        logger.info(f"开始构建最终数据集，输入目录: {integrated_dir}")
        
        # 检查现有数据集
        if not self._check_existing_dataset():
            return False
            
        # 准备输出目录
        self._prepare_output_directory()
        
        # 查找数据文件
        image_files = get_image_files(integrated_dir / 'images', recursive=False)
        label_files = get_label_files(integrated_dir / 'labels', recursive=False)
        
        if not image_files:
            logger.error(f"未找到图像文件: {integrated_dir / 'images'}")
            return False
            
        logger.info(f"找到 {len(image_files)} 个图像文件, {len(label_files)} 个标签文件")
        
        # 匹配文件对
        file_pairs = self._match_files(image_files, label_files)
        
        if not file_pairs:
            logger.error("未找到匹配的文件对")
            return False
            
        # 检查是否为增量更新
        is_incremental = bool(self.existing_statistics.get('dataset_sources'))
        
        if is_incremental:
            # 增量更新：只添加新文件，不重新分割现有数据
            success = self._incremental_update(file_pairs)
        else:
            # 首次构建：完整的数据分割和复制
            success = self._full_build(file_pairs)
            
        if not success:
            return False
            
        # 生成YOLO配置文件
        self._generate_dataset_yaml()
        
        # 生成统计信息
        self._generate_statistics()
        
        # 数据验证
        if self.validation_config.get('enabled', True):
            self._validate_dataset()
            
        logger.info("最终数据集构建完成")
        return True
        
    def _full_build(self, file_pairs: List[Tuple[Path, Path]]) -> bool:
        """
        完整构建最终数据集
        
        Args:
            file_pairs: 文件对列表
            
        Returns:
            bool: 是否成功
        """
        logger.info("执行完整数据集构建")
        
        # 数据分割
        split_data = self._split_data(file_pairs)
        
        # 复制文件到最终目录
        return self._copy_files_to_final_directory(split_data)
        
    def _incremental_update(self, file_pairs: List[Tuple[Path, Path]]) -> bool:
        """
        增量更新最终数据集
        
        Args:
            file_pairs: 新增的文件对列表
            
        Returns:
            bool: 是否成功
        """
        logger.info("执行增量数据集更新")
        
        # 获取现有的分割统计
        existing_split_stats = self.existing_statistics.get('split_statistics', {})
        existing_total = sum(existing_split_stats.values())
        
        logger.info(f"现有数据集样本数: {existing_total}")
        logger.info(f"新增样本数: {len(file_pairs)}")
        
        # 使用相同的随机种子和分割比例
        split_ratios = self.split_config.get('split_ratios', {
            'train': 0.7, 'val': 0.2, 'test': 0.1
        })
        
        random_seed = self.split_config.get('random_seed', 42)
        random.seed(random_seed)
        
        # 随机打乱新文件
        file_pairs_shuffled = file_pairs.copy()
        random.shuffle(file_pairs_shuffled)
        
        # 按比例分割新文件
        total_count = len(file_pairs_shuffled)
        train_count = int(total_count * split_ratios['train'])
        val_count = int(total_count * split_ratios['val'])
        test_count = total_count - train_count - val_count
        
        split_data = {
            'train': file_pairs_shuffled[:train_count],
            'val': file_pairs_shuffled[train_count:train_count + val_count],
            'test': file_pairs_shuffled[train_count + val_count:]
        }
        
        # 更新分割统计信息（增量）
        self.split_statistics = {
            'train': train_count,
            'val': val_count,
            'test': test_count
        }
        
        # 记录每个文件的分割信息，用于后续统计
        self.file_split_mapping = {}
        for split_name, pairs in split_data.items():
            for image_file, label_file in pairs:
                self.file_split_mapping[str(label_file)] = split_name
                
        logger.info(f"新增数据分割: train={train_count}, val={val_count}, test={test_count}")
        
        # 增量复制文件到最终目录
        return self._copy_files_to_final_directory(split_data)
        
    def _check_existing_dataset(self) -> bool:
        """检查现有数据集统计信息"""
        statistics_file = self.output_dir / 'statistics.json'
        
        if not statistics_file.exists():
            logger.info("未找到现有统计文件，将创建新的数据集")
            self.existing_statistics = {}
            return True
            
        try:
            with open(statistics_file, 'r', encoding='utf-8') as f:
                self.existing_statistics = json.load(f)
                
            # 检查现有数据集来源
            existing_sources = self.existing_statistics.get('dataset_sources', {})
            
            if existing_sources:
                logger.info(f"找到现有数据集，包含数据来源: {list(existing_sources.keys())}")
                logger.info("将进行增量更新")
                            
            return True
            
        except Exception as e:
            logger.error(f"读取现有统计文件失败: {e}")
            self.existing_statistics = {}
            return True
            
    def _prepare_output_directory(self):
        """准备输出目录"""
        ensure_dir(self.output_dir)
        
        # 创建分割目录
        splits = ['train', 'val', 'test']
        for split in splits:
            for subdir in ['images', 'labels']:
                ensure_dir(self.output_dir / split / subdir)
                
        logger.info(f"输出目录准备完成: {self.output_dir}")
        
    def _match_files(self, image_files: List[Path], label_files: List[Path]) -> List[Tuple[Path, Path]]:
        """匹配图像和标签文件"""
        label_dict = {label_file.stem: label_file for label_file in label_files}
        
        pairs = []
        for image_file in image_files:
            if image_file.stem in label_dict:
                pairs.append((image_file, label_dict[image_file.stem]))
                
        return pairs
        
    def _split_data(self, file_pairs: List[Tuple[Path, Path]]) -> Dict[str, List[Tuple[Path, Path]]]:
        """数据分割"""
        split_ratios = self.split_config.get('split_ratios', {
            'train': 0.7, 'val': 0.2, 'test': 0.1
        })
        
        random_seed = self.split_config.get('random_seed', 42)
        
        # 设置随机种子
        random.seed(random_seed)
        
        # 随机打乱
        file_pairs_shuffled = file_pairs.copy()
        random.shuffle(file_pairs_shuffled)
        
        # 计算分割点
        total_count = len(file_pairs_shuffled)
        train_count = int(total_count * split_ratios['train'])
        val_count = int(total_count * split_ratios['val'])
        test_count = total_count - train_count - val_count
        
        # 分割数据
        split_data = {
            'train': file_pairs_shuffled[:train_count],
            'val': file_pairs_shuffled[train_count:train_count + val_count],
            'test': file_pairs_shuffled[train_count + val_count:]
        }
        
        # 更新统计信息
        self.split_statistics = {
            'train': len(split_data['train']),
            'val': len(split_data['val']),
            'test': len(split_data['test'])
        }
        
        # 记录每个文件的分割信息，用于后续统计
        self.file_split_mapping = {}
        for split_name, pairs in split_data.items():
            for image_file, label_file in pairs:
                self.file_split_mapping[str(label_file)] = split_name
                
        logger.info(f"数据分割完成: train={train_count}, val={val_count}, test={test_count}")
        
        return split_data
        
    def _copy_files_to_final_directory(self, split_data: Dict[str, List[Tuple[Path, Path]]]) -> bool:
        """复制文件到最终目录"""
        logger.info("复制文件到最终目录")
        
        for split, file_pairs in split_data.items():
            if not file_pairs:
                continue
                
            logger.info(f"复制 {split} 数据: {len(file_pairs)} 个文件对")
            
            for image_file, label_file in tqdm(file_pairs, desc=f"复制 {split} 数据"):
                # 目标路径
                target_image_path = self.output_dir / split / 'images' / image_file.name
                target_label_path = self.output_dir / split / 'labels' / label_file.name
                
                # 复制图像文件
                if not copy_file(image_file, target_image_path):
                    logger.error(f"复制图像文件失败: {image_file}")
                    return False
                    
                # 复制标签文件
                if not copy_file(label_file, target_label_path):
                    logger.error(f"复制标签文件失败: {label_file}")
                    return False
                    
        return True
        
    def _generate_dataset_yaml(self):
        """生成 dataset.yaml 配置文件"""
        if not self.yolo_config.get('generate_yaml', True):
            return
            
        logger.info("生成 dataset.yaml 配置文件")
        
        # 获取全局类别映射
        global_mapping = self.class_mapper.get_global_class_mapping()
        
        # 构建类别名称列表（按ID排序）
        class_names = [''] * len(global_mapping)
        for class_name, class_id in global_mapping.items():
            class_names[class_id] = class_name
            
        # 构建YAML配置
        yaml_config = {
            'path': str(self.output_dir.absolute()),
            'train': 'train/images',
            'val': 'val/images',
            'test': 'test/images',
            'nc': len(class_names),
            'names': class_names
        }
        
        # 添加数据集信息
        dataset_info = self.dataset_info
        if dataset_info:
            yaml_config.update({
                'dataset_name': dataset_info.get('name', 'Custom Detection Dataset'),
                'description': dataset_info.get('description', ''),
                'version': dataset_info.get('version', '1.0.0'),
                'authors': dataset_info.get('authors', []),
                'license': dataset_info.get('license', ''),
                'created': datetime.now().isoformat()
            })
            
        # 添加类别颜色（如果启用）
        if self.yolo_config.get('include_class_colors', True):
            colors = self._generate_class_colors(len(class_names))
            yaml_config['colors'] = colors
            
        # 保存YAML文件
        yaml_filename = self.yolo_config.get('yaml_filename', 'dataset.yaml')
        yaml_path = self.output_dir / yaml_filename
        
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(yaml_config, f, default_flow_style=False, allow_unicode=True)
            
        logger.info(f"dataset.yaml 已生成: {yaml_path}")
        
    def _generate_class_colors(self, num_classes: int) -> List[List[int]]:
        """
        生成类别颜色
        
        Args:
            num_classes: 类别数量
            
        Returns:
            List[List[int]]: 颜色列表
        """
        colors = []
        for i in range(num_classes):
            # 生成均匀分布的颜色
            hue = (i * 360 / num_classes) % 360
            saturation = 70 + (i * 30) % 30
            value = 80 + (i * 20) % 20
            
            # 简单的HSV到RGB转换
            import colorsys
            r, g, b = colorsys.hsv_to_rgb(hue/360, saturation/100, value/100)
            colors.append([int(r*255), int(g*255), int(b*255)])
            
        return colors
        
    def _collect_dataset_sources(self) -> Dict[str, Dict]:
        """收集数据集来源信息"""
        sources = {}
        
        # 获取当前启用的数据集
        dataset_configs = self.config.get('datasets', [])
        enabled_datasets = [cfg for cfg in dataset_configs if cfg.get('enabled', True)]
        
        for dataset_config in enabled_datasets:
            dataset_name = dataset_config.get('name', '')
            if not dataset_name:
                continue
                
            # 获取类别映射信息
            class_mapping = self.class_mapper.get_class_mapping(dataset_name)
            
            if class_mapping:
                mapped_classes = class_mapping.get('mapped_classes', {})
                
                # 统计每个类别的数据量（需要从合并后的数据中统计）
                class_stats = self._calculate_dataset_class_statistics(dataset_name, mapped_classes)
                
                sources[dataset_name] = {
                    'path': dataset_config.get('path', ''),
                    'format': dataset_config.get('format', ''),
                    'selected_classes': dataset_config.get('selected_classes', []),
                    'mapped_classes': mapped_classes,
                    'class_statistics': class_stats,
                    'processed_time': datetime.now().isoformat()
                }
                
        return sources
        
    def _calculate_dataset_class_statistics(self, dataset_name: str, mapped_classes: Dict) -> Dict:
        """计算数据集类别统计信息"""
        # 从合并器获取数据集统计信息
        merger_stats = self.dataset_merger.get_statistics()
        dataset_class_stats = merger_stats.get('dataset_class_stats', {})
        
        if dataset_name not in dataset_class_stats:
            logger.warning(f"未找到数据集 {dataset_name} 的统计信息")
            return {}
            
        dataset_stats = dataset_class_stats[dataset_name]
        result = {}
        
        # 为每个类别计算分割统计
        for class_name, class_info in dataset_stats.items():
            target_id = class_info.get('target_id', -1)
            samples = class_info.get('samples', [])
            
            # 初始化统计结构
            result[class_name] = {
                'target_id': target_id,
                'samples': {'train': 0, 'val': 0, 'test': 0},
                'total_samples': len(samples)
            }
            
            # 计算每个分割的样本数量
            for sample in samples:
                label_file = sample.get('label_file', '')
                split_name = self.file_split_mapping.get(label_file, 'unknown')
                if split_name in result[class_name]['samples']:
                    result[class_name]['samples'][split_name] += 1
                    
        return result
        
    def _is_file_from_dataset(self, label_file: Path, dataset_name: str, file_mapping: Dict) -> bool:
        """检查文件是否来自指定数据集"""
        # 通过文件名前缀判断
        if label_file.name.startswith(f"{dataset_name}_"):
            return True
            
        # 通过文件映射判断
        for original_path, mapped_path in file_mapping.items():
            if Path(mapped_path) == label_file:
                # 检查原始路径是否包含数据集名称
                if dataset_name in original_path:
                    return True
                    
        return False
        
    def _generate_statistics(self):
        """生成统计信息"""
        if not self.final_config.get('generate_statistics', True):
            return
            
        logger.info("生成统计信息")
        
        # 收集数据集来源信息
        current_sources = self._collect_dataset_sources()
        
        # 合并现有的数据集来源信息
        all_sources = self.existing_statistics.get('dataset_sources', {})
        all_sources.update(current_sources)
        
        # 按类别统计
        current_class_stats = self._calculate_class_statistics()
        
        # 合并现有的类别统计
        existing_class_stats = self.existing_statistics.get('class_statistics', {})
        
        # 创建完整的类别统计字典
        class_stats = {}
        
        # 首先复制现有的统计信息
        for class_id, stats in existing_class_stats.items():
            class_stats[class_id] = stats.copy()
            
        # 然后更新或添加当前批次的统计信息
        for class_id, stats in current_class_stats.items():
            if class_id in class_stats:
                # 合并统计数据
                for split in ['train', 'val', 'test']:
                    if split in stats:
                        class_stats[class_id][split] = class_stats[class_id].get(split, 0) + stats[split]
            else:
                # 新类别，直接添加
                class_stats[class_id] = stats.copy()
                        
        # 收集详细统计
        self.final_statistics = {
            'dataset_info': self.dataset_info,
            'split_statistics': self.split_statistics,
            'class_mapping': self.class_mapper.get_global_class_mapping(),
            'total_samples': sum(self.split_statistics.values()),
            'created_time': datetime.now().isoformat(),
            'output_directory': str(self.output_dir),
            'class_statistics': class_stats,
            'dataset_sources': all_sources
        }
        
        # 更新总样本数（包括现有的）
        if self.existing_statistics:
            existing_total = self.existing_statistics.get('total_samples', 0)
            self.final_statistics['total_samples'] += existing_total
            
            # 更新分割统计
            existing_split_stats = self.existing_statistics.get('split_statistics', {})
            for split, count in existing_split_stats.items():
                if split in self.final_statistics['split_statistics']:
                    self.final_statistics['split_statistics'][split] += count
                else:
                    self.final_statistics['split_statistics'][split] = count
        
        # 保存统计信息
        formats = self.final_config.get('statistics_format', ['json'])
        
        if 'json' in formats:
            stats_path = self.output_dir / 'statistics.json'
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(self.final_statistics, f, indent=2, ensure_ascii=False)
            logger.info(f"统计信息已保存: {stats_path}")
            
        if 'txt' in formats:
            self._save_statistics_txt()
            
    def _calculate_class_statistics(self) -> Dict:
        """计算类别统计信息"""
        class_stats = defaultdict(lambda: defaultdict(int))
        
        # 从合并器获取样本跟踪信息
        merger_stats = self.dataset_merger.get_statistics()
        class_sample_tracking = merger_stats.get('class_sample_tracking', {})
        
        # 使用样本跟踪信息和文件分割映射来计算统计
        for class_id, samples in class_sample_tracking.items():
            for sample in samples:
                label_file = sample.get('label_file', '')
                # 查找该文件被分配到哪个分割
                split_name = self.file_split_mapping.get(label_file, 'unknown')
                if split_name != 'unknown':
                    class_stats[class_id][split_name] += 1
                    
        # 转换为普通字典以避免序列化问题
        result = {}
        for class_id, stats in class_stats.items():
            result[class_id] = dict(stats)  # 将内层defaultdict转换为普通dict
            
        return result
        
    def _save_statistics_txt(self):
        """保存TXT格式统计信息"""
        stats_path = self.output_dir / 'statistics.txt'
        
        with open(stats_path, 'w', encoding='utf-8') as f:
            f.write("数据集统计信息\n")
            f.write("=" * 50 + "\n\n")
            
            # 基本信息
            f.write(f"数据集名称: {self.dataset_info.get('name', 'N/A')}\n")
            f.write(f"描述: {self.dataset_info.get('description', 'N/A')}\n")
            f.write(f"版本: {self.dataset_info.get('version', 'N/A')}\n")
            f.write(f"创建时间: {self.final_statistics.get('created_time', 'N/A')}\n")
            f.write(f"总样本数: {self.final_statistics.get('total_samples', 0)}\n\n")
            
            # 分割统计
            f.write("分割统计:\n")
            for split, count in self.split_statistics.items():
                f.write(f"  {split}: {count}\n")
            f.write("\n")
            
            # 类别映射
            f.write("类别映射:\n")
            global_mapping = self.class_mapper.get_global_class_mapping()
            for class_name, class_id in global_mapping.items():
                f.write(f"  {class_id}: {class_name}\n")
            f.write("\n")
            
            # 类别统计
            f.write("类别统计:\n")
            class_stats = self.final_statistics.get('class_statistics', {})
            for class_id, stats in class_stats.items():
                f.write(f"  类别 {class_id}:\n")
                for split, count in stats.items():
                    f.write(f"    {split}: {count}\n")
                f.write("\n")
                
        logger.info(f"TXT统计信息已保存: {stats_path}")
        
    def _validate_dataset(self):
        """验证数据集"""
        logger.info("验证数据集")
        
        validation_config = self.validation_config
        
        # 检查文件完整性
        if validation_config.get('check_file_integrity', True):
            self._check_file_integrity()
            
        # 验证类别分布
        if validation_config.get('verify_class_distribution', True):
            self._verify_class_distribution()
            
        # 生成样本图像
        if validation_config.get('generate_sample_images', True):
            self._generate_sample_images()
            
    def _check_file_integrity(self):
        """检查文件完整性"""
        logger.info("检查文件完整性")
        
        for split in ['train', 'val', 'test']:
            images_dir = self.output_dir / split / 'images'
            labels_dir = self.output_dir / split / 'labels'
            
            if not images_dir.exists() or not labels_dir.exists():
                continue
                
            image_files = get_image_files(images_dir)
            label_files = get_label_files(labels_dir)
            
            image_stems = {f.stem for f in image_files}
            label_stems = {f.stem for f in label_files}
            
            # 检查匹配情况
            missing_labels = image_stems - label_stems
            missing_images = label_stems - image_stems
            
            if missing_labels:
                logger.warning(f"{split} 分割中缺少标签文件: {len(missing_labels)} 个")
                
            if missing_images:
                logger.warning(f"{split} 分割中缺少图像文件: {len(missing_images)} 个")
                
            logger.info(f"{split} 分割完整性检查完成: {len(image_files)} 图像, {len(label_files)} 标签")
            
    def _verify_class_distribution(self):
        """验证类别分布"""
        logger.info("验证类别分布")
        
        class_stats = self.final_statistics.get('class_statistics', {})
        global_mapping = self.class_mapper.get_global_class_mapping()
        
        for class_name, class_id in global_mapping.items():
            class_id_str = str(class_id)
            if class_id_str in class_stats:
                total_samples = sum(class_stats[class_id_str].values())
                logger.info(f"类别 {class_name} (ID: {class_id}): {total_samples} 个样本")
            else:
                logger.warning(f"类别 {class_name} (ID: {class_id}): 未找到样本")
                
    def _generate_sample_images(self):
        """生成样本图像"""
        sample_count = self.validation_config.get('sample_count', 10)
        
        logger.info(f"生成 {sample_count} 个样本图像")
        
        # 简单实现：从每个分割中随机选择几个图像
        samples_dir = self.output_dir / 'samples'
        ensure_dir(samples_dir)
        
        for split in ['train', 'val', 'test']:
            images_dir = self.output_dir / split / 'images'
            if not images_dir.exists():
                continue
                
            image_files = get_image_files(images_dir)
            if not image_files:
                continue
                
            # 随机选择样本
            sample_size = min(sample_count // 3, len(image_files))
            if sample_size > 0:
                np.random.seed(42)  # 确保结果可重现
                indices = np.random.choice(len(image_files), size=sample_size, replace=False)
                selected_images = [image_files[i] for i in indices]
                
                for i, image_file in enumerate(selected_images):
                    target_path = samples_dir / f"{split}_{i:03d}_{image_file.name}"
                    copy_file(image_file, target_path)
                    
        logger.info(f"样本图像已生成: {samples_dir}")
        
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return self.final_statistics.copy() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据整合模块
提供数据集整合相关的功能，包括类别映射、数据合并、数据平衡和最终数据集构建
"""

from .class_mapper import ClassMapper
from .dataset_merger import DatasetMerger
from .data_balancer import DataBalancer
from .final_dataset_builder import FinalDatasetBuilder

__all__ = [
    'ClassMapper',
    'DatasetMerger', 
    'DataBalancer',
    'FinalDatasetBuilder'
]

__version__ = '1.0.0'
__author__ = 'Yolo Utils Team'
__description__ = '数据集整合模块，提供完整的多源数据集整合解决方案'

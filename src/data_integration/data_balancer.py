#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据平衡器
处理类别不平衡问题
"""

import os
import sys
import shutil
import random
from pathlib import Path
from typing import Dict, List, Set, Optional, Union, Tuple
from collections import defaultdict
from tqdm import tqdm

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.basic.logger import get_logger
from src.basic.exceptions import DataIntegrationError
from src.utils.file_utils import ensure_dir, copy_file, get_image_files, get_label_files

logger = get_logger(__name__)


class DataBalancer:
    """数据平衡器"""
    
    def __init__(self, config: Dict):
        """
        初始化数据平衡器
        
        Args:
            config: 数据整合配置
        """
        self.config = config
        self.balancing_config = config.get('data_balancing', {})
        
        # 配置参数
        self.enabled = self.balancing_config.get('enabled', False)
        self.strategy = self.balancing_config.get('strategy', 'undersample')
        self.target_type = self.balancing_config.get('target_type', 'min_class')
        self.custom_target_count = self.balancing_config.get('custom_target_count', 1000)
        self.oversample_config = self.balancing_config.get('oversample', {})
        self.undersample_config = self.balancing_config.get('undersample', {})
        
        # 运行时状态
        self.class_statistics: Dict[int, int] = {}
        self.balanced_files: Dict[str, List[str]] = {'images': [], 'labels': []}
        
    def balance_dataset(self, dataset_dir: Path, global_class_mapping: Dict[str, int]) -> bool:
        """
        平衡数据集
        
        Args:
            dataset_dir: 数据集目录
            global_class_mapping: 全局类别映射
            
        Returns:
            bool: 是否成功
        """
        if not self.enabled:
            logger.info("数据平衡功能未启用，跳过")
            return True
            
        logger.info(f"开始平衡数据集: {dataset_dir}")
        
        # 分析类别分布
        self._analyze_class_distribution(dataset_dir)
        
        if not self.class_statistics:
            logger.warning("未找到类别分布信息，跳过数据平衡")
            return True
            
        # 计算目标数量
        target_count = self._calculate_target_count()
        logger.info(f"目标样本数量: {target_count}")
        
        # 执行平衡策略
        success = self._apply_balancing_strategy(dataset_dir, target_count)
        
        if success:
            logger.info("数据平衡完成")
        else:
            logger.error("数据平衡失败")
            
        return success
        
    def _analyze_class_distribution(self, dataset_dir: Path):
        """
        分析类别分布
        
        Args:
            dataset_dir: 数据集目录
        """
        logger.info("分析类别分布")
        
        labels_dir = dataset_dir / 'labels'
        if not labels_dir.exists():
            logger.warning(f"标签目录不存在: {labels_dir}")
            return
            
        self.class_statistics = defaultdict(int)
        
        # 统计每个类别的样本数量
        label_files = get_label_files(labels_dir, recursive=True)
        
        for label_file in tqdm(label_files, desc="分析类别分布"):
            try:
                with open(label_file, 'r') as f:
                    for line in f:
                        parts = line.strip().split()
                        if parts and parts[0].isdigit():
                            class_id = int(parts[0])
                            self.class_statistics[class_id] += 1
            except Exception as e:
                logger.warning(f"读取标签文件失败: {label_file}, 错误: {e}")
                
        # 转换为普通字典并记录统计信息
        self.class_statistics = dict(self.class_statistics)
        
        logger.info("类别分布统计:")
        for class_id, count in sorted(self.class_statistics.items()):
            logger.info(f"  类别 {class_id}: {count} 个样本")
            
    def _calculate_target_count(self) -> int:
        """
        计算目标样本数量
        
        Returns:
            int: 目标样本数量
        """
        if self.target_type == 'min_class':
            return min(self.class_statistics.values())
        elif self.target_type == 'max_class':
            return max(self.class_statistics.values())
        elif self.target_type == 'median':
            sorted_counts = sorted(self.class_statistics.values())
            n = len(sorted_counts)
            if n % 2 == 0:
                return (sorted_counts[n//2 - 1] + sorted_counts[n//2]) // 2
            else:
                return sorted_counts[n//2]
        elif self.target_type == 'custom':
            return self.custom_target_count
        else:
            return min(self.class_statistics.values())
            
    def _apply_balancing_strategy(self, dataset_dir: Path, target_count: int) -> bool:
        """
        应用平衡策略
        
        Args:
            dataset_dir: 数据集目录
            target_count: 目标样本数量
            
        Returns:
            bool: 是否成功
        """
        if self.strategy == 'undersample':
            return self._undersample(dataset_dir, target_count)
        elif self.strategy == 'oversample':
            return self._oversample(dataset_dir, target_count)
        elif self.strategy == 'mixed':
            return self._mixed_sampling(dataset_dir, target_count)
        else:
            logger.error(f"不支持的平衡策略: {self.strategy}")
            return False
            
    def _undersample(self, dataset_dir: Path, target_count: int) -> bool:
        """
        欠采样平衡
        
        Args:
            dataset_dir: 数据集目录
            target_count: 目标样本数量
            
        Returns:
            bool: 是否成功
        """
        logger.info(f"执行欠采样，目标数量: {target_count}")
        
        # 收集每个类别的文件
        class_files = self._collect_files_by_class(dataset_dir)
        
        # 创建平衡后的目录
        balanced_dir = dataset_dir.parent / f"{dataset_dir.name}_balanced"
        ensure_dir(balanced_dir / 'images')
        ensure_dir(balanced_dir / 'labels')
        
        try:
            for class_id, files in class_files.items():
                current_count = len(files)
                
                if current_count <= target_count:
                    # 样本数量不足，全部保留
                    selected_files = files
                    logger.info(f"类别 {class_id}: 保留全部 {current_count} 个样本")
                else:
                    # 随机选择样本
                    method = self.undersample_config.get('method', 'random')
                    if method == 'random':
                        selected_files = random.sample(files, target_count)
                    elif method == 'diverse':
                        selected_files = self._select_diverse_samples(files, target_count)
                    else:
                        selected_files = files[:target_count]
                        
                    logger.info(f"类别 {class_id}: 从 {current_count} 个样本中选择 {target_count} 个")
                
                # 复制选中的文件
                self._copy_selected_files(selected_files, balanced_dir)
                
            # 替换原目录
            self._replace_directory(dataset_dir, balanced_dir)
            
            return True
            
        except Exception as e:
            logger.error(f"欠采样失败: {e}")
            return False
            
    def _oversample(self, dataset_dir: Path, target_count: int) -> bool:
        """
        过采样平衡
        
        Args:
            dataset_dir: 数据集目录
            target_count: 目标样本数量
            
        Returns:
            bool: 是否成功
        """
        logger.info(f"执行过采样，目标数量: {target_count}")
        
        # 收集每个类别的文件
        class_files = self._collect_files_by_class(dataset_dir)
        
        # 创建平衡后的目录
        balanced_dir = dataset_dir.parent / f"{dataset_dir.name}_balanced"
        ensure_dir(balanced_dir / 'images')
        ensure_dir(balanced_dir / 'labels')
        
        try:
            for class_id, files in class_files.items():
                current_count = len(files)
                
                if current_count >= target_count:
                    # 样本数量足够，随机选择
                    selected_files = random.sample(files, target_count)
                    logger.info(f"类别 {class_id}: 从 {current_count} 个样本中选择 {target_count} 个")
                else:
                    # 样本数量不足，需要过采样
                    method = self.oversample_config.get('method', 'duplicate')
                    max_duplicates = self.oversample_config.get('max_duplicates', 3)
                    
                    if method == 'duplicate':
                        selected_files = self._duplicate_samples(files, target_count, max_duplicates)
                    elif method == 'augment':
                        selected_files = self._augment_samples(files, target_count)
                    else:
                        selected_files = files * (target_count // current_count + 1)
                        selected_files = selected_files[:target_count]
                        
                    logger.info(f"类别 {class_id}: 从 {current_count} 个样本过采样到 {len(selected_files)} 个")
                
                # 复制选中的文件
                self._copy_selected_files(selected_files, balanced_dir)
                
            # 替换原目录
            self._replace_directory(dataset_dir, balanced_dir)
            
            return True
            
        except Exception as e:
            logger.error(f"过采样失败: {e}")
            return False
            
    def _mixed_sampling(self, dataset_dir: Path, target_count: int) -> bool:
        """
        混合采样平衡
        
        Args:
            dataset_dir: 数据集目录
            target_count: 目标样本数量
            
        Returns:
            bool: 是否成功
        """
        logger.info(f"执行混合采样，目标数量: {target_count}")
        
        # 收集每个类别的文件
        class_files = self._collect_files_by_class(dataset_dir)
        
        # 创建平衡后的目录
        balanced_dir = dataset_dir.parent / f"{dataset_dir.name}_balanced"
        ensure_dir(balanced_dir / 'images')
        ensure_dir(balanced_dir / 'labels')
        
        try:
            for class_id, files in class_files.items():
                current_count = len(files)
                
                if current_count == target_count:
                    # 数量刚好，全部保留
                    selected_files = files
                elif current_count > target_count: # 数据偏多
                    # 欠采样
                    selected_files = random.sample(files, target_count)
                else:
                    # 过采样
                    max_duplicates = self.oversample_config.get('max_duplicates', 3)
                    selected_files = self._duplicate_samples(files, target_count, max_duplicates)
                    
                logger.info(f"类别 {class_id}: {current_count} -> {len(selected_files)} 个样本")
                
                # 复制选中的文件
                self._copy_selected_files(selected_files, balanced_dir)
                
            # 替换原目录
            self._replace_directory(dataset_dir, balanced_dir)
            
            return True
            
        except Exception as e:
            logger.error(f"混合采样失败: {e}")
            return False
            
    def _collect_files_by_class(self, dataset_dir: Path) -> Dict[int, List[Tuple[Path, Path]]]:
        """
        按类别收集文件
        
        Args:
            dataset_dir: 数据集目录
            
        Returns:
            Dict[int, List[Tuple[Path, Path]]]: 类别ID到文件对列表的映射
        """
        class_files = defaultdict(list)
        
        images_dir = dataset_dir / 'images'
        labels_dir = dataset_dir / 'labels'
        
        # 获取所有标签文件
        label_files = get_label_files(labels_dir, recursive=True)
        
        for label_file in label_files:
            # 查找对应的图像文件
            image_file = self._find_corresponding_image(label_file, images_dir)
            
            if image_file and image_file.exists():
                # 读取标签文件获取类别
                try:
                    with open(label_file, 'r') as f:
                        for line in f:
                            parts = line.strip().split()
                            if parts and parts[0].isdigit():
                                class_id = int(parts[0])
                                class_files[class_id].append((image_file, label_file))
                                break  # 只取第一个类别
                except Exception as e:
                    logger.warning(f"读取标签文件失败: {label_file}, 错误: {e}")
                    
        return dict(class_files)
        
    def _find_corresponding_image(self, label_file: Path, images_dir: Path) -> Optional[Path]:
        """
        查找对应的图像文件
        
        Args:
            label_file: 标签文件路径
            images_dir: 图像目录
            
        Returns:
            Optional[Path]: 对应的图像文件路径
        """
        stem = label_file.stem
        
        # 尝试不同的图像扩展名
        for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            image_file = images_dir / f"{stem}{ext}"
            if image_file.exists():
                return image_file
                
        return None
        
    def _select_diverse_samples(self, files: List[Tuple[Path, Path]], 
                               target_count: int) -> List[Tuple[Path, Path]]:
        """
        选择多样化的样本
        
        Args:
            files: 文件对列表
            target_count: 目标数量
            
        Returns:
            List[Tuple[Path, Path]]: 选中的文件对
        """
        # 简单实现：随机选择
        # 可以在这里实现更复杂的多样性选择算法
        return random.sample(files, min(target_count, len(files)))
        
    def _duplicate_samples(self, files: List[Tuple[Path, Path]], 
                          target_count: int, max_duplicates: int) -> List[Tuple[Path, Path]]:
        """
        复制样本
        
        Args:
            files: 文件对列表
            target_count: 目标数量
            max_duplicates: 最大复制次数
            
        Returns:
            List[Tuple[Path, Path]]: 复制后的文件对列表
        """
        if not files:
            return []
            
        result = []
        current_count = len(files)
        
        # 首先添加所有原始文件
        result.extend(files)
        
        # 计算需要复制的数量
        needed = target_count - current_count
        
        if needed > 0:
            # 限制复制次数
            max_total = current_count * (max_duplicates + 1)
            actual_target = min(target_count, max_total)
            actual_needed = actual_target - current_count
            
            # 随机复制文件
            for _ in range(actual_needed):
                selected_file = random.choice(files)
                result.append(selected_file)
                
        return result[:target_count]
        
    def _augment_samples(self, files: List[Tuple[Path, Path]], 
                        target_count: int) -> List[Tuple[Path, Path]]:
        """
        增强样本（简单实现，实际可以集成图像增强）
        
        Args:
            files: 文件对列表
            target_count: 目标数量
            
        Returns:
            List[Tuple[Path, Path]]: 增强后的文件对列表
        """
        # 简单实现：等同于复制
        # 实际实现可以在这里集成图像增强技术
        return self._duplicate_samples(files, target_count, 3)
        
    def _copy_selected_files(self, selected_files: List[Tuple[Path, Path]], 
                           target_dir: Path):
        """
        复制选中的文件
        
        Args:
            selected_files: 选中的文件对列表
            target_dir: 目标目录
        """
        for i, (image_file, label_file) in enumerate(selected_files):
            # 生成新文件名（处理重复）
            base_name = image_file.stem
            image_ext = image_file.suffix
            label_ext = label_file.suffix
            
            # 检查文件名重复
            counter = 1
            new_image_name = f"{base_name}{image_ext}"
            new_label_name = f"{base_name}{label_ext}"
            
            while (target_dir / 'images' / new_image_name).exists():
                new_image_name = f"{base_name}_{counter:03d}{image_ext}"
                new_label_name = f"{base_name}_{counter:03d}{label_ext}"
                counter += 1
                
            # 复制文件
            target_image = target_dir / 'images' / new_image_name
            target_label = target_dir / 'labels' / new_label_name
            
            copy_file(image_file, target_image)
            copy_file(label_file, target_label)
            
    def _replace_directory(self, original_dir: Path, balanced_dir: Path):
        """
        用平衡后的目录替换原目录
        
        Args:
            original_dir: 原目录
            balanced_dir: 平衡后的目录
        """
        # 备份原目录
        backup_dir = original_dir.parent / f"{original_dir.name}_backup"
        if backup_dir.exists():
            shutil.rmtree(backup_dir)
        shutil.move(str(original_dir), str(backup_dir))
        
        # 移动平衡后的目录到原位置
        shutil.move(str(balanced_dir), str(original_dir))
        
        logger.info(f"原目录已备份到: {backup_dir}")
        
    def get_class_statistics(self) -> Dict[int, int]:
        """获取类别统计信息"""
        # 确保返回的是普通字典，不是defaultdict
        if isinstance(self.class_statistics, defaultdict):
            return dict(self.class_statistics)
        return self.class_statistics.copy() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
类别映射管理器
负责分析和管理全局类别映射关系
"""

import sys
import yaml
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.basic.logger import get_logger
from src.basic.exceptions import DataIntegrationError
from src.utils.file_utils import ensure_dir

logger = get_logger(__name__)


class ClassMapper:
    """类别映射管理器"""
    
    def __init__(self, config: Dict):
        """
        初始化类别映射器
        
        Args:
            config: 数据整合配置
        """
        self.config = config
        self.class_mapping_config = config.get('class_mapping', {})
        self.global_classes = self.class_mapping_config.get('global_classes', [])
        self.mapping_rules = self.class_mapping_config.get('mapping_rules', {})
        self.auto_mapping = self.class_mapping_config.get('auto_mapping', {})
        self.validation = self.class_mapping_config.get('validation', {})
        
        # 初始化映射表
        self.global_class_to_id: Dict[str, int] = {}
        self.id_to_global_class: Dict[int, str] = {}
        self.source_to_target: Dict[str, str] = {}
        self.dataset_class_info: Dict[str, Dict] = {}
        
        # 初始化映射表
        self._initialize_mappings()
        
    def _initialize_mappings(self):
        """初始化映射表"""
        # 构建全局类别到ID的映射
        for i, class_name in enumerate(self.global_classes):
            self.global_class_to_id[class_name] = i
            self.id_to_global_class[i] = class_name
            
        # 构建源类别到目标类别的映射
        self.source_to_target = self.mapping_rules.copy()
        
        logger.info(f"初始化类别映射器完成，全局类别数: {len(self.global_classes)}")
        
    def analyze_dataset_classes(self, dataset_config: Dict) -> Dict:
        """
        分析数据集的类别信息
        
        Args:
            dataset_config: 数据集配置
            
        Returns:
            Dict: 数据集类别分析结果
        """
        dataset_name = dataset_config.get('name', '')
        dataset_path = Path(dataset_config.get('path', ''))
        
        logger.info(f"分析数据集类别: {dataset_name}")
        
        # 读取数据集的类别信息
        dataset_classes = self._read_dataset_classes(dataset_path)
        selected_classes = dataset_config.get('selected_classes', [])
        
        # 如果指定了选择的类别，只保留这些类别
        if selected_classes:
            dataset_classes = {k: v for k, v in dataset_classes.items() if v in selected_classes}
            
        # 分析映射关系
        mapped_classes = {}
        unmapped_classes = []
        
        for class_id, class_name in dataset_classes.items():
            if class_name in self.source_to_target:
                # 统一为小写字母
                # class_name = class_name.lower()
                target_class = self.source_to_target[class_name]
                if target_class in self.global_class_to_id:
                    mapped_classes[class_id] = {
                        'source_name': class_name,
                        'target_name': target_class,
                        'target_id': self.global_class_to_id[target_class]
                    }
                else:
                    logger.warning(f"目标类别 '{target_class}' 不在全局类别列表中")
                    unmapped_classes.append(class_name)
            else:
                unmapped_classes.append(class_name)
                
        # 验证映射结果
        if unmapped_classes:
            if self.validation.get('strict_mode', True):
                raise DataIntegrationError(f"数据集 {dataset_name} 存在未映射的类别: {unmapped_classes}")
            elif self.validation.get('log_unmapped_classes', True):
                logger.warning(f"数据集 {dataset_name} 存在未映射的类别: {unmapped_classes}")
                
        analysis_result = {
            'dataset_name': dataset_name,
            'dataset_path': str(dataset_path),
            'total_classes': len(dataset_classes),
            'mapped_classes': mapped_classes,
            'unmapped_classes': unmapped_classes,
            'mapping_success_rate': len(mapped_classes) / len(dataset_classes) if dataset_classes else 0
        }
        
        self.dataset_class_info[dataset_name] = analysis_result
        logger.info(f"数据集 {dataset_name} 类别分析完成，映射成功率: {analysis_result['mapping_success_rate']:.2%}")
        
        return analysis_result
        
    def _read_dataset_classes(self, dataset_path: Path) -> Dict[int, str]:
        """
        读取数据集的类别信息
        
        Args:
            dataset_path: 数据集路径
            
        Returns:
            Dict[int, str]: 类别ID到类别名称的映射
        """
        classes = {}
        
        # 尝试读取 classes.txt 文件
        classes_file = dataset_path / 'classes.txt'
        if classes_file.exists():
            try:
                with open(classes_file, 'r', encoding='utf-8') as f:
                    for i, line in enumerate(f):
                        class_name = line.strip()
                        if class_name:
                            classes[i] = class_name
                logger.info(f"从 classes.txt 读取了 {len(classes)} 个类别")
                return classes
            except Exception as e:
                logger.warning(f"读取 classes.txt 失败: {e}")
                
        # 尝试读取 dataset.yaml 文件
        yaml_file = dataset_path / 'dataset.yaml'
        if yaml_file.exists():
            try:
                with open(yaml_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    if 'names' in data:
                        if isinstance(data['names'], list):
                            for i, name in enumerate(data['names']):
                                classes[i] = name
                        elif isinstance(data['names'], dict):
                            classes = data['names']
                logger.info(f"从 dataset.yaml 读取了 {len(classes)} 个类别")
                return classes
            except Exception as e:
                logger.warning(f"读取 dataset.yaml 失败: {e}")
                
        # 如果没有找到类别文件，尝试从标签文件分析
        logger.warning(f"未找到类别文件，尝试从标签文件分析类别")
        classes = self._analyze_classes_from_labels(dataset_path)
        
        return classes
        
    def _analyze_classes_from_labels(self, dataset_path: Path) -> Dict[int, str]:
        """
        从标签文件分析类别
        
        Args:
            dataset_path: 数据集路径
            
        Returns:
            Dict[int, str]: 类别ID到类别名称的映射
        """
        class_ids = set()
        
        # 查找所有标签文件
        labels_dir = dataset_path / 'labels'
        if labels_dir.exists():
            for txt_file in labels_dir.rglob('*.txt'):
                try:
                    with open(txt_file, 'r') as f:
                        for line in f:
                            parts = line.strip().split()
                            if parts and parts[0].isdigit():
                                class_ids.add(int(parts[0]))
                except Exception as e:
                    logger.warning(f"读取标签文件 {txt_file} 失败: {e}")
                    
        # 生成默认类别名称
        classes = {class_id: f"class_{class_id}" for class_id in sorted(class_ids)}
        logger.info(f"从标签文件分析出 {len(classes)} 个类别")
        
        return classes
        
    def get_class_mapping(self, dataset_name: str) -> Optional[Dict]:
        """
        获取数据集的类别映射信息
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            Optional[Dict]: 类别映射信息
        """
        return self.dataset_class_info.get(dataset_name)
        
    def get_global_class_mapping(self) -> Dict[str, int]:
        """
        获取全局类别映射
        
        Returns:
            Dict[str, int]: 全局类别名称到ID的映射
        """
        return self.global_class_to_id.copy()
        
    def get_target_class_id(self, source_class: str) -> Optional[int]:
        """
        获取源类别对应的目标类别ID
        
        Args:
            source_class: 源类别名称
            
        Returns:
            Optional[int]: 目标类别ID
        """
        if source_class in self.source_to_target:
            target_class = self.source_to_target[source_class]
            return self.global_class_to_id.get(target_class)
        return None
        
    def validate_mapping(self, dataset_configs: List[Dict]) -> bool:
        """
        验证所有数据集的映射关系
        
        Args:
            dataset_configs: 数据集配置列表
            
        Returns:
            bool: 验证是否通过
        """
        logger.info("开始验证类别映射关系")
        
        all_valid = True
        
        for dataset_config in dataset_configs:
            if not dataset_config.get('enabled', True):
                continue
                
            try:
                analysis_result = self.analyze_dataset_classes(dataset_config)
                if analysis_result['unmapped_classes']:
                    all_valid = False
                    logger.error(f"数据集 {dataset_config['name']} 存在未映射的类别")
            except Exception as e:
                all_valid = False
                logger.error(f"验证数据集 {dataset_config['name']} 失败: {e}")
                
        logger.info(f"类别映射验证完成，结果: {'通过' if all_valid else '失败'}")
        return all_valid
        
    def generate_mapping_report(self, output_path: Optional[Path] = None) -> Dict:
        """
        生成映射报告
        
        Args:
            output_path: 输出路径
            
        Returns:
            Dict: 映射报告
        """
        logger.info("生成类别映射报告")
        
        report = {
            'global_classes': self.global_classes,
            'mapping_rules': self.mapping_rules,
            'dataset_analysis': self.dataset_class_info,
            'summary': {
                'total_global_classes': len(self.global_classes),
                'total_mapping_rules': len(self.mapping_rules),
                'analyzed_datasets': len(self.dataset_class_info)
            }
        }
        
        # 计算总体统计
        total_classes = 0
        total_mapped = 0
        
        for dataset_info in self.dataset_class_info.values():
            total_classes += dataset_info['total_classes']
            total_mapped += len(dataset_info['mapped_classes'])
            
        report['summary']['total_source_classes'] = total_classes
        report['summary']['total_mapped_classes'] = total_mapped
        report['summary']['overall_mapping_rate'] = total_mapped / total_classes if total_classes > 0 else 0
        
        # 保存报告
        if output_path:
            output_path = Path(output_path)
            ensure_dir(output_path.parent)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(report, f, default_flow_style=False, allow_unicode=True)
                
            logger.info(f"映射报告已保存至: {output_path}")
            
        return report 
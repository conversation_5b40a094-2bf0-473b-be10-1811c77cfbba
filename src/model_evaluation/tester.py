"""
模型测试器

基于ultralytics框架的YOLO模型测试器，提供模型评估和测试功能。
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
import yaml
import json
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.basic.config_manager import ConfigManager
from src.basic.logger import get_logger
from src.basic.exceptions import TrainingError, ConfigurationError
from src.model_training.training_utils.model_manager import ModelManager

try:
    from ultralytics import YOLO
except ImportError:
    raise ImportError("请安装ultralytics库: pip install ultralytics")


class YOLOTester:
    """YOLO模型测试器
    
    基于ultralytics框架的统一测试接口，支持多种测试模式。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化测试器
        
        Args:
            config_path: 配置文件路径，默认使用config/model_config.yaml
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config_manager = ConfigManager()
        
        # 设置配置路径
        if config_path is None:
            config_path = "config/model_config.yaml"
        self.config_path = Path(config_path)
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化组件
        self.model_manager = ModelManager()
        
        # 测试状态
        self.test_results = None
        self.current_test_dir = None
        
        self.logger.info("YOLOTester初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载测试配置
        
        Returns:
            Dict[str, Any]: 配置字典
            
        Raises:
            ConfigurationError: 配置加载失败
        """
        try:
            if not self.config_path.exists():
                raise ConfigurationError(f"配置文件不存在: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if not config:
                raise ConfigurationError("配置文件为空")
            
            self.logger.info(f"成功加载配置文件: {self.config_path}")
            return config
            
        except Exception as e:
            raise ConfigurationError(f"加载配置文件失败: {e}")
    
    def test_model(self, model_path: Optional[str] = None, test_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """测试模型
        
        Args:
            model_path: 模型文件路径，如果为None则自动查找
            test_config: 测试配置，如果为None则使用默认配置
            
        Returns:
            Dict[str, Any]: 测试结果
            
        Raises:
            TrainingError: 测试失败
        """
        try:
            self.logger.info("开始模型测试")
            
            # 确定要测试的模型
            if model_path is None:
                model_path = self._find_model_to_test()
                if model_path is None:
                    raise TrainingError("未找到可测试的模型文件")
            
            model_path_obj = Path(model_path)
            if not model_path_obj.exists():
                raise TrainingError(f"模型文件不存在: {model_path_obj}")
            
            self.logger.info(f"测试模型: {model_path_obj}")
            
            # 设置测试环境
            test_dir = self._setup_test_environment(model_path_obj.stem)
            
            # 加载模型
            model = self._load_model(str(model_path_obj))
            
            # 准备测试参数
            if test_config is None:
                test_config = self.config.get('testing', {})
            
            test_params = self._prepare_test_params(test_config, test_dir)
            
            # 执行测试
            self.logger.info("开始执行模型测试")
            start_time = datetime.now()
            
            results = model.val(**test_params)
            
            test_time = (datetime.now() - start_time).total_seconds()
            
            # 处理测试结果
            self.test_results = self._process_test_results(
                results, test_time, str(model_path_obj), test_dir
            )
            
            # 保存测试结果
            self._save_test_results(self.test_results, test_dir)
            
            # 生成测试报告
            self._generate_test_report(self.test_results, test_dir)
            
            self.logger.info("模型测试完成")
            return self.test_results
            
        except Exception as e:
            if isinstance(e, TrainingError):
                raise
            else:
                raise TrainingError(f"模型测试失败: {e}")
    
    def test_latest_model(self, mode: str = 'fixed_params') -> Dict[str, Any]:
        """测试最新训练的模型
        
        Args:
            mode: 训练模式
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            latest_model = self.model_manager.find_latest_model(mode)
            if latest_model is None:
                raise TrainingError(f"未找到{mode}模式的最新模型")
            
            self.logger.info(f"测试最新模型: {latest_model}")
            return self.test_model(latest_model)
            
        except Exception as e:
            raise TrainingError(f"测试最新模型失败: {e}")
    
    def test_best_model(self, mode: str = 'fixed_params') -> Dict[str, Any]:
        """测试最佳训练的模型
        
        Args:
            mode: 训练模式
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            best_model = self.model_manager.find_best_model(mode)
            if best_model is None:
                raise TrainingError(f"未找到{mode}模式的最佳模型")
            
            self.logger.info(f"测试最佳模型: {best_model}")
            return self.test_model(best_model)
            
        except Exception as e:
            raise TrainingError(f"测试最佳模型失败: {e}")
    
    def batch_test_models(self, model_paths: List[str]) -> Dict[str, Any]:
        """批量测试多个模型
        
        Args:
            model_paths: 模型文件路径列表
            
        Returns:
            Dict[str, Any]: 批量测试结果
        """
        try:
            self.logger.info(f"开始批量测试{len(model_paths)}个模型")
            
            batch_results = {
                'total_models': len(model_paths),
                'test_results': [],
                'comparison': {},
                'start_time': datetime.now().isoformat()
            }
            
            # 逐个测试模型
            for i, model_path in enumerate(model_paths):
                try:
                    self.logger.info(f"测试模型 {i+1}/{len(model_paths)}: {model_path}")
                    
                    result = self.test_model(model_path)
                    result['model_index'] = i
                    batch_results['test_results'].append(result)
                    
                except Exception as e:
                    self.logger.error(f"测试模型失败 {model_path}: {e}")
                    batch_results['test_results'].append({
                        'model_path': model_path,
                        'model_index': i,
                        'success': False,
                        'error': str(e)
                    })
            
            # 生成比较结果
            batch_results['comparison'] = self._compare_test_results(batch_results['test_results'])
            batch_results['end_time'] = datetime.now().isoformat()
            
            self.logger.info("批量测试完成")
            return batch_results
            
        except Exception as e:
            raise TrainingError(f"批量测试失败: {e}")
    
    def _find_model_to_test(self) -> Optional[str]:
        """查找要测试的模型
        
        Returns:
            Optional[str]: 模型路径
        """
        # 优先查找最佳模型
        best_model = self.model_manager.find_best_model()
        if best_model:
            return best_model
        
        # 然后查找最新模型
        latest_model = self.model_manager.find_latest_model()
        if latest_model:
            return latest_model
        
        # 最后在models目录查找
        models_dir = Path("models")
        if models_dir.exists():
            pt_files = list(models_dir.glob("*.pt"))
            if pt_files:
                return str(pt_files[0])
        
        return None
    
    def _setup_test_environment(self, model_name: str) -> str:
        """设置测试环境
        
        Args:
            model_name: 模型名称
            
        Returns:
            str: 测试输出目录路径
        """
        try:
            # 创建测试目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            test_name = f"test_{model_name.replace('.pt', '')}_{timestamp}"
            
            test_config = self.config.get('testing', {})
            project_dir = test_config.get('project', 'runs/test')
            test_dir = Path(project_dir) / test_name
            test_dir.mkdir(parents=True, exist_ok=True)
            
            self.current_test_dir = str(test_dir)
            
            # 保存配置到测试目录
            config_backup_path = test_dir / 'test_config_backup.yaml'
            with open(config_backup_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info(f"测试环境设置完成: {self.current_test_dir}")
            return self.current_test_dir
            
        except Exception as e:
            raise TrainingError(f"设置测试环境失败: {e}")
    
    def _load_model(self, model_path: str) -> YOLO:
        """加载模型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            YOLO: 加载的模型实例
            
        Raises:
            TrainingError: 模型加载失败
        """
        try:
            self.logger.info(f"加载模型: {model_path}")
            
            model = YOLO(model_path)
            
            self.logger.info(f"模型加载完成: {model_path}")
            return model
            
        except Exception as e:
            raise TrainingError(f"模型加载失败: {e}")
    
    def _prepare_test_params(self, test_config: Optional[Dict[str, Any]], test_dir: str) -> Dict[str, Any]:
        """准备测试参数
        
        Args:
            test_config: 测试配置
            test_dir: 测试输出目录
            
        Returns:
            Dict[str, Any]: ultralytics测试参数
        """
        # 确保test_config不为None
        if test_config is None:
            test_config = {}
        
        # 基础配置
        environment_config = self.config.get('environment', {})
        
        # 构建测试参数
        test_params = {
            # 数据集配置
            'data': self.config['dataset']['config_path'],
            
            # 设备配置
            'device': environment_config.get('device', 'auto'),
            
            # 测试参数
            'split': test_config.get('split', 'test'),
            'batch': test_config.get('batch', 32),
            'imgsz': test_config.get('imgsz', 640),
            'workers': test_config.get('workers', 8),
            
            # 检测参数
            'conf': test_config.get('conf', 0.001),
            'iou': test_config.get('iou', 0.6),
            'max_det': test_config.get('max_det', 300),
            'half': test_config.get('half', False),
            'dnn': test_config.get('dnn', False),
            
            # 输出控制
            'save_json': test_config.get('save_json', True),
            'save_hybrid': test_config.get('save_hybrid', False),
            'plots': test_config.get('plots', True),
            'verbose': test_config.get('verbose', True),
            
            # 输出目录 - 直接使用我们创建的测试目录，避免ultralytics再次创建子目录
            'project': str(test_dir),
            'name': ''  # 设置为空字符串，避免ultralytics创建额外的子目录
        }
        
        # 移除None值
        test_params = {k: v for k, v in test_params.items() if v is not None}
        
        self.logger.debug(f"配置的测试参数: {test_params}")
        return test_params
    
    def _process_test_results(self, results: Any, test_time: float, model_path: str, test_dir: str) -> Dict[str, Any]:
        """处理测试结果
        
        Args:
            results: ultralytics测试结果
            test_time: 测试耗时
            model_path: 模型路径
            test_dir: 测试目录
            
        Returns:
            Dict[str, Any]: 处理后的测试结果
        """
        try:
            processed_results = {
                'model_path': model_path,
                'test_time': test_time,
                'test_dir': test_dir,
                'test_timestamp': datetime.now().isoformat(),
                'status': 'completed'
            }
            
            # 提取测试指标
            if hasattr(results, 'results_dict'):
                metrics = results.results_dict
                processed_results['test_metrics'] = metrics
                
                # 提取关键指标
                key_metrics = {}
                for key, value in metrics.items():
                    if 'mAP' in key or 'precision' in key or 'recall' in key or 'F1' in key:
                        try:
                            key_metrics[key] = float(value)
                        except (ValueError, TypeError):
                            key_metrics[key] = value
                
                processed_results['key_metrics'] = key_metrics
            
            # 获取保存的结果路径
            if hasattr(results, 'save_dir'):
                save_dir = Path(results.save_dir)
                processed_results['save_dir'] = str(save_dir)
                
                # 查找结果文件
                confusion_matrix = save_dir / 'confusion_matrix.png'
                if confusion_matrix.exists():
                    processed_results['confusion_matrix_path'] = str(confusion_matrix)
                
                predictions_json = save_dir / 'predictions.json'
                if predictions_json.exists():
                    processed_results['predictions_json_path'] = str(predictions_json)
            
            # 测试成功标志
            processed_results['success'] = True
            
            self.logger.info(f"测试结果处理完成，耗时: {test_time:.2f}秒")
            return processed_results
            
        except Exception as e:
            self.logger.error(f"处理测试结果失败: {e}")
            return {
                'model_path': model_path,
                'test_time': test_time,
                'test_dir': test_dir,
                'test_timestamp': datetime.now().isoformat(),
                'status': 'completed_with_errors',
                'success': False,
                'error': str(e)
            }
    
    def _save_test_results(self, results: Dict[str, Any], test_dir: str) -> None:
        """保存测试结果
        
        Args:
            results: 测试结果
            test_dir: 测试目录
        """
        try:
            test_dir_path = Path(test_dir)
            
            # 保存结果JSON
            results_file = test_dir_path / 'test_results.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"测试结果已保存到: {test_dir_path}")
            
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")
    
    def _generate_test_report(self, results: Dict[str, Any], test_dir: str) -> None:
        """生成测试报告
        
        Args:
            results: 测试结果
            test_dir: 测试目录
        """
        try:
            test_dir_path = Path(test_dir)
            
            # 生成文本报告
            report_file = test_dir_path / 'test_report.txt'
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("YOLO模型测试报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"测试时间: {results.get('test_timestamp', 'N/A')}\n")
                f.write(f"模型路径: {results.get('model_path', 'N/A')}\n")
                f.write(f"测试耗时: {results.get('test_time', 0):.2f}秒\n")
                f.write(f"测试状态: {results.get('status', 'N/A')}\n")
                f.write(f"测试成功: {'是' if results.get('success', False) else '否'}\n")
                f.write("\n")
                
                # 关键指标
                if 'key_metrics' in results:
                    f.write("关键性能指标:\n")
                    f.write("-" * 30 + "\n")
                    for metric, value in results['key_metrics'].items():
                        if isinstance(value, float):
                            f.write(f"{metric}: {value:.4f}\n")
                        else:
                            f.write(f"{metric}: {value}\n")
                    f.write("\n")
                
                # 详细指标
                if 'test_metrics' in results:
                    f.write("详细测试指标:\n")
                    f.write("-" * 30 + "\n")
                    for metric, value in results['test_metrics'].items():
                        if isinstance(value, float):
                            f.write(f"{metric}: {value:.4f}\n")
                        else:
                            f.write(f"{metric}: {value}\n")
                    f.write("\n")
                
                # 输出文件
                if 'save_dir' in results:
                    f.write("输出文件:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"结果目录: {results['save_dir']}\n")
                    
                    if 'confusion_matrix_path' in results:
                        f.write(f"混淆矩阵: {results['confusion_matrix_path']}\n")
                    
                    if 'predictions_json_path' in results:
                        f.write(f"预测结果: {results['predictions_json_path']}\n")
                
                f.write("\n详细结果请查看test_results.json文件\n")
            
            self.logger.info(f"测试报告已生成: {report_file}")
            
        except Exception as e:
            self.logger.error(f"生成测试报告失败: {e}")
    
    def _compare_test_results(self, test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """比较测试结果
        
        Args:
            test_results: 测试结果列表
            
        Returns:
            Dict[str, Any]: 比较结果
        """
        try:
            comparison: Dict[str, Any] = {
                'total_tests': len(test_results),
                'successful_tests': len([r for r in test_results if r.get('success', False)]),
                'failed_tests': len([r for r in test_results if not r.get('success', False)])
            }
            
            # 获取成功的测试结果
            successful_results = [r for r in test_results if r.get('success', False) and 'key_metrics' in r]
            
            if len(successful_results) > 1:
                # 提取公共指标
                all_metrics = set()
                for result in successful_results:
                    all_metrics.update(result['key_metrics'].keys())
                
                # 比较每个指标
                metric_comparison = {}
                for metric in all_metrics:
                    values = []
                    models = []
                    
                    for result in successful_results:
                        if metric in result['key_metrics']:
                            value = result['key_metrics'][metric]
                            if isinstance(value, (int, float)):
                                values.append(value)
                                models.append(result['model_path'])
                    
                    if values:
                        best_idx = values.index(max(values))
                        worst_idx = values.index(min(values))
                        
                        metric_comparison[metric] = {
                            'best_value': max(values),
                            'best_model': models[best_idx],
                            'worst_value': min(values),
                            'worst_model': models[worst_idx],
                            'average': sum(values) / len(values)
                        }
                
                if metric_comparison:
                    comparison['metric_comparison'] = metric_comparison
            
            return comparison
            
        except Exception as e:
            self.logger.error(f"比较测试结果失败: {e}")
            return {'error': str(e)} 
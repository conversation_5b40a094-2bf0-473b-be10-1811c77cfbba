"""
VisDrone2019数据集转换器
将VisDrone2019格式的数据转换为YOLO格式
"""
import os
import random
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import logging
from tqdm import tqdm
from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np

from src.basic.base_classes import BaseConverter
from src.utils.file_utils import ensure_dir, copy_file, get_image_files


class VisDroneToYOLOConverter(BaseConverter):
    """VisDrone2019数据集转换器
    
    将VisDrone2019格式的标注数据转换为YOLO格式，
    包括坐标转换、类别映射、数据过滤等功能。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化转换器
        
        Args:
            config: 转换器配置参数
        """
        super().__init__(config)
        
        # VisDrone类别映射 (只使用前两个类别)
        self.visdrone_classes = {
            0: 'pedestrian',
            1: 'people'
        }
        
        # 处理统计
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.total_annotations = 0
        self.valid_annotations = 0
        
    def convert(self, source_path: str, target_path: str) -> bool:
        """执行格式转换
        
        Args:
            source_path: VisDrone2019数据集根目录路径
            target_path: YOLO格式输出目录路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 检查目标目录是否已存在且有数据
            if self._check_target_exists(target_path):
                self.logger.info(f"目标目录已存在: {target_path}")
                self.logger.info("跳过数据转换，直接生成验证图片...")
                self._generate_validation_images(target_path)

                self.logger.info("生成 dataset.yaml 文件...")
                return self._generate_dataset_yaml(target_path)
            
            self.logger.info(f"开始VisDrone2019数据集转换: {source_path} -> {target_path}")
            
            # 创建输出目录结构
            self._create_output_structure(target_path)
            
            # 处理各个数据集
            datasets = {
                'train': 'VisDrone2019-DET-train',
                'val': 'VisDrone2019-DET-val', 
                'test': 'VisDrone2019-DET-test-dev'
            }
            
            for split_name, source_dir_name in datasets.items():
                source_dir = Path(source_path) / source_dir_name
                target_images_dir = Path(target_path) / 'images' / split_name
                target_labels_dir = Path(target_path) / 'labels' / split_name
                
                if source_dir.exists():
                    self.logger.info(f"处理{split_name}数据集...")
                    self._process_dataset(source_dir, target_images_dir, target_labels_dir)
                else:
                    self.logger.warning(f"源目录不存在: {source_dir}")
            
            self.logger.info(f"转换完成 - 总文件: {self.processed_count}, 成功: {self.success_count}, 错误: {self.error_count}")
            self.logger.info(f"标注统计 - 总标注: {self.total_annotations}, 有效标注: {self.valid_annotations}")
            
            # 生成验证图片
            self.logger.info("生成验证图片...")
            self._generate_validation_images(target_path)
            
            # 生成 dataset.yaml 文件
            self.logger.info("生成 dataset.yaml 文件...")
            self._generate_dataset_yaml(target_path)
            
            return True
            
        except Exception as e:
            self.logger.error(f"转换失败: {e}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表
        
        Returns:
            List[str]: 支持的格式列表
        """
        return ["visdrone"]
    
    def _check_target_exists(self, target_path: str) -> bool:
        """检查目标目录是否存在且有数据
        
        Args:
            target_path: 目标目录路径
            
        Returns:
            bool: 目标目录是否已存在且有数据
        """
        target_path_obj = Path(target_path)
        
        if not target_path_obj.exists():
            return False
        
        # 检查是否有images和labels目录，且包含train/val/test子目录和数据
        images_dir = target_path_obj / 'images'
        labels_dir = target_path_obj / 'labels'
        
        if not (images_dir.exists() and labels_dir.exists()):
            return False
        
        # 检查是否有train/val/test子目录且包含数据
        for split in ['train', 'val', 'test']:
            split_images_dir = images_dir / split
            split_labels_dir = labels_dir / split
            
            if split_images_dir.exists() and split_labels_dir.exists():
                # 检查是否有文件
                if list(split_images_dir.glob('*.jpg')) and list(split_labels_dir.glob('*.txt')):
                    return True
        
        return False
    
    def _create_output_structure(self, target_path: str) -> None:
        """创建输出目录结构
        
        Args:
            target_path: 输出目录路径
        """
        # 创建主目录结构：images和labels在外层，train/val/test在内层
        for file_type in ['images', 'labels']:
            for split in ['train', 'val', 'test']:
                ensure_dir(Path(target_path) / file_type / split)
    
    def _process_dataset(self, source_dir: Path, target_images_dir: Path, target_labels_dir: Path) -> None:
        """处理单个数据集
        
        Args:
            source_dir: 源数据集目录
            target_images_dir: 目标图像目录
            target_labels_dir: 目标标签目录
        """
        images_dir = source_dir / 'images'
        annotations_dir = source_dir / 'annotations'
        
        # 获取所有图像文件
        image_files = get_image_files(images_dir)
        
        if not image_files:
            self.logger.warning(f"在{images_dir}中未找到图像文件")
            return
        
        # 处理每个图像文件
        for img_path in tqdm(image_files, desc=f"处理{source_dir.name}"):
            try:
                # 对应的标注文件
                ann_path = annotations_dir / (img_path.stem + '.txt')
                
                # 复制图像文件
                target_img_path = target_images_dir / img_path.name
                copy_file(img_path, target_img_path)
                
                # 转换标注文件
                target_ann_path = target_labels_dir / (img_path.stem + '.txt')
                success = self._convert_annotation_file(ann_path, img_path, target_ann_path)
                
                self.processed_count += 1
                if success:
                    self.success_count += 1
                else:
                    self.error_count += 1
                    
            except Exception as e:
                self.logger.error(f"处理文件{img_path}时出错: {e}")
                self.error_count += 1
    
    def _convert_annotation_file(self, ann_path: Path, img_path: Path, output_path: Path) -> bool:
        """转换单个标注文件
        
        Args:
            ann_path: 标注文件路径
            img_path: 对应的图像文件路径
            output_path: 输出标注文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 获取图像尺寸
            with Image.open(img_path) as img:
                img_width, img_height = img.size
            
            # 读取标注文件
            if not ann_path.exists():
                # 创建空标注文件
                output_path.write_text('')
                return True
            
            annotations = []
            with open(ann_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        # 解析VisDrone标注格式
                        parts = line.split(',')
                        if len(parts) < 8:
                            continue
                        
                        bbox_left = int(parts[0])
                        bbox_top = int(parts[1])
                        bbox_width = int(parts[2])
                        bbox_height = int(parts[3])
                        score = int(parts[4])
                        object_category = int(parts[5])
                        
                        self.total_annotations += 1
                        
                        # 过滤条件
                        if score != 1:  # 只保留score=1的标注
                            continue
                        
                        if object_category not in self.visdrone_classes:  # 只保留目标类别
                            continue
                        
                        # 转换坐标格式
                        yolo_bbox = self._convert_bbox_format(
                            bbox_left, bbox_top, bbox_width, bbox_height,
                            img_width, img_height
                        )
                        
                        if yolo_bbox is None:  # 无效坐标
                            continue
                        
                        # YOLO格式：class_id x_center y_center width height
                        yolo_annotation = f"0 {yolo_bbox[0]:.6f} {yolo_bbox[1]:.6f} {yolo_bbox[2]:.6f} {yolo_bbox[3]:.6f}"
                        annotations.append(yolo_annotation)
                        
                        self.valid_annotations += 1
                        
                    except (ValueError, IndexError) as e:
                        self.logger.warning(f"解析标注行失败: {line}, 错误: {e}")
                        continue
            
            # 写入转换后的标注文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(annotations))
                if annotations:  # 如果有标注，添加最后的换行符
                    f.write('\n')
            
            return True
            
        except Exception as e:
            self.logger.error(f"转换标注文件{ann_path}失败: {e}")
            return False
    
    def _convert_bbox_format(self, bbox_left: int, bbox_top: int, bbox_width: int, bbox_height: int,
                           img_width: int, img_height: int) -> Optional[Tuple[float, float, float, float]]:
        """转换边界框格式
        
        Args:
            bbox_left: 左上角x坐标
            bbox_top: 左上角y坐标
            bbox_width: 宽度
            bbox_height: 高度
            img_width: 图像宽度
            img_height: 图像高度
            
        Returns:
            Optional[Tuple[float, float, float, float]]: YOLO格式的边界框 (x_center, y_center, width, height)
        """
        try:
            # 检查边界框是否有效
            if bbox_width <= 0 or bbox_height <= 0:
                return None
            
            if bbox_left < 0 or bbox_top < 0:
                return None
            
            if bbox_left + bbox_width > img_width or bbox_top + bbox_height > img_height:
                return None
            
            # 转换为YOLO格式 (归一化的中心坐标和宽高)
            x_center = (bbox_left + bbox_width / 2) / img_width
            y_center = (bbox_top + bbox_height / 2) / img_height
            width = bbox_width / img_width
            height = bbox_height / img_height
            
            # 确保坐标在有效范围内
            if not (0 <= x_center <= 1 and 0 <= y_center <= 1 and 0 < width <= 1 and 0 < height <= 1):
                return None
            
            return (x_center, y_center, width, height)
            
        except Exception as e:
            self.logger.error(f"坐标转换失败: {e}")
            return None
    
    def _generate_validation_images(self, target_path: str) -> bool:
        """生成验证图片
        
        Args:
            target_path: 目标目录路径
            
        Returns:
            bool: 生成是否成功
        """
        try:
            target_path_obj = Path(target_path)
            validation_dir = target_path_obj / 'validation'
            ensure_dir(validation_dir)
            
            # 从每个数据集中随机选择一个有标注的文件
            for split in ['train', 'val', 'test']:
                images_dir = target_path_obj / 'images' / split
                labels_dir = target_path_obj / 'labels' / split
                
                if not (images_dir.exists() and labels_dir.exists()):
                    continue
                
                # 获取所有标注文件
                label_files = list(labels_dir.glob('*.txt'))
                if not label_files:
                    self.logger.warning(f"跳过{split}集合：没有找到标注文件")
                    continue
                
                # 过滤出有内容的标注文件
                valid_label_files = []
                for label_file in label_files:
                    if label_file.stat().st_size > 0:  # 文件大小大于0
                        valid_label_files.append(label_file)
                
                if not valid_label_files:
                    self.logger.warning(f"跳过{split}集合：没有找到有效的标注文件")
                    continue
                
                # 随机选择一个有效的标注文件
                selected_label = random.choice(valid_label_files)
                
                # 查找对应的图像文件
                image_name = selected_label.stem
                selected_image = None
                for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                    potential_image = images_dir / f"{image_name}{ext}"
                    if potential_image.exists():
                        selected_image = potential_image
                        break
                
                if selected_image is None:
                    self.logger.warning(f"跳过{split}集合：找不到对应的图像文件 {image_name}")
                    continue
                
                # 生成验证图片
                output_path = validation_dir / f"{split}_validation.jpg"
                success = self._draw_annotations(selected_image, selected_label, output_path)
                
                if success:
                    self.logger.info(f"生成{split}验证图片: {output_path}")
                else:
                    self.logger.warning(f"生成{split}验证图片失败")
            
            return True
            
        except Exception as e:
            self.logger.error(f"生成验证图片失败: {e}")
            return False
    
    def _generate_dataset_yaml(self, target_path: str) -> bool:
        """生成 dataset.yaml 文件
        
        Args:
            target_path: 目标目录路径
            
        Returns:
            bool: 生成是否成功
        """
        try:
            import yaml
            from datetime import datetime
            
            target_path_obj = Path(target_path)
            
            # 创建 dataset.yaml 配置
            dataset_config = {
                'path': str(target_path_obj.absolute()),
                'train': 'images/train',
                'val': 'images/val', 
                'test': 'images/test',
                'nc': 1,  # 只有一个类别：person
                'names': ['person'],
                
                # 数据集信息
                'dataset_name': 'VisDrone2019-Person',
                'description': 'VisDrone2019数据集转换为YOLO格式，只包含person类别',
                'version': '1.0.0',
                'source': 'VisDrone2019',
                'classes': {
                    0: 'person'
                },
                'created': datetime.now().isoformat(),
                'converter': 'VisDroneToYOLOConverter'
            }
            
            # 保存 dataset.yaml 文件
            yaml_path = target_path_obj / 'dataset.yaml'
            with open(yaml_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
                
            self.logger.info(f"dataset.yaml 文件已生成: {yaml_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成 dataset.yaml 文件失败: {e}")
            return False
    
    def _draw_annotations(self, image_path: Path, label_path: Path, output_path: Path) -> bool:
        """在图像上绘制标注框
        
        Args:
            image_path: 图像文件路径
            label_path: 标签文件路径
            output_path: 输出图像路径
            
        Returns:
            bool: 绘制是否成功
        """
        try:
            # 读取图像
            image = cv2.imread(str(image_path))
            if image is None:
                self.logger.error(f"无法读取图像: {image_path}")
                return False
            
            img_height, img_width = image.shape[:2]
            
            # 读取标注文件
            if not label_path.exists():
                # 如果没有标注文件，直接保存原图
                cv2.imwrite(str(output_path), image)
                return True
            
            with open(label_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        # 解析YOLO格式标注
                        parts = line.split()
                        if len(parts) != 5:
                            continue
                        
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        
                        # 转换为像素坐标
                        x_center_px = x_center * img_width
                        y_center_px = y_center * img_height
                        width_px = width * img_width
                        height_px = height * img_height
                        
                        # 计算边界框坐标
                        x1 = int(x_center_px - width_px / 2)
                        y1 = int(y_center_px - height_px / 2)
                        x2 = int(x_center_px + width_px / 2)
                        y2 = int(y_center_px + height_px / 2)
                        
                        # 绘制边界框
                        cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        
                        # 绘制类别标签
                        label_text = "person"
                        cv2.putText(image, label_text, (x1, y1 - 10), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                        
                    except (ValueError, IndexError) as e:
                        self.logger.warning(f"解析标注行失败: {line}, 错误: {e}")
                        continue
            
            # 保存图像
            cv2.imwrite(str(output_path), image)
            return True
            
        except Exception as e:
            self.logger.error(f"绘制标注失败: {e}")
            return False


def main():
    """主函数，用于测试转换器"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建转换器
    converter = VisDroneToYOLOConverter({})
    
    # 执行转换
    source_path = "data/raw/VisDrone2019"
    target_path = "data/processed/VisDrone2019"
    
    success = converter.convert(source_path, target_path)
    
    if success:
        print("转换成功完成！")
    else:
        print("转换失败！")


if __name__ == "__main__":
    main() 
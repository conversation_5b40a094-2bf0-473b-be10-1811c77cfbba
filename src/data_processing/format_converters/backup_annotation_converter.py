"""
备份标注转换器
将数据库备份的图像数据转换为YOLO训练格式
整合图像比较、颜色检测等核心算法
"""
import os
import csv
import cv2
import numpy as np
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import logging
from tqdm import tqdm
import shutil
from scipy.ndimage import maximum_filter, label
from scipy.spatial.distance import pdist, squareform
try:
    from sklearn.cluster import DBSCAN
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Warning: sklearn not available, corner clustering method will be disabled")

from src.basic.base_classes import BaseConverter
from src.basic.config_manager import ConfigManager
from src.data_processing.annotation_tools.annotation_generator import AnnotationGenerator


class BackupAnnotationConverter(BaseConverter):
    """备份标注转换器
    
    将数据库备份的图像数据转换为YOLO训练格式，
    集成图像比较、颜色检测、轮廓处理等核心算法。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化转换器
        
        Args:
            config: 转换器配置参数
        """
        super().__init__(config)
        self.config_manager = ConfigManager()
        self.annotation_generator = AnnotationGenerator({})
        
        # 处理统计
        self.processed_count = 0
        self.success_count = 0
        self.skipped_count = 0
        self.error_count = 0
        self.skipped_files = []  # 记录跳过的文件详情
        
    def convert(self, source_path: str, target_path: str) -> bool:
        """执行格式转换
        
        Args:
            source_path: CSV数据文件路径
            target_path: 输出目录路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            self.logger.info(f"开始备份标注转换: {source_path} -> {target_path}")
            
            # 加载配置
            config = self.config_manager.get_backup_annotation_config()
            if not config:
                self.logger.error("未找到备份标注配置")
                return False
            
            # 处理CSV数据
            results = self._process_csv_data(source_path, target_path, config)
            
            # 生成验证材料
            if config.get('validation', {}).get('enabled', True):
                self._generate_validation_materials(results, target_path, config)
            
            self.logger.info(f"转换完成 - 成功: {self.success_count}, 跳过: {self.skipped_count}, 错误: {self.error_count}")
            return True
            
        except Exception as e:
            self.logger.error(f"转换失败: {e}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表
        
        Returns:
            List[str]: 支持的格式列表
        """
        return ["csv_backup"]
    
    def _process_csv_data(self, csv_path: str, output_dir: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理CSV数据
        
        Args:
            csv_path: CSV文件路径
            output_dir: 输出目录
            config: 配置参数
            
        Returns:
            List[Dict[str, Any]]: 处理结果列表
        """
        results = []
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        images_dir = os.path.join(output_dir, config.get('output', {}).get('images_dir', 'images'))
        labels_dir = os.path.join(output_dir, config.get('output', {}).get('labels_dir', 'labels'))
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(labels_dir, exist_ok=True)
        
        # 调试目录
        debug_dir = None
        if config.get('debug_mode', False):
            debug_dir = os.path.join(output_dir, config.get('output', {}).get('debug_dir', 'debug'))
            os.makedirs(debug_dir, exist_ok=True)
        
        # 读取CSV数据
        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                data = list(reader)
        except Exception as e:
            self.logger.error(f"读取CSV文件失败: {e}")
            return results
        
        self.logger.info(f"CSV数据加载成功，共 {len(data)} 条记录")
        
        # 处理配置
        processing_config = config.get('processing', {})
        target_classes = processing_config.get('target_classes', [])
        max_count = processing_config.get('max_process_count', -1)
        
        # 类别过滤
        filtered_data = self._filter_csv_data(data, target_classes)
        
        # 优先文件处理
        priority_config = processing_config.get('priority_files', {})
        if priority_config.get('enabled', False):
            filtered_data = self._apply_priority_files_filter(filtered_data, priority_config, max_count)
        elif max_count > 0:
            # 常规数量限制
            filtered_data = filtered_data[:max_count]
            self.logger.debug(f"数量限制后处理 {len(filtered_data)} 条记录")
        
        self.logger.debug(f"开始处理 {len(filtered_data)} 条数据")
        
        # 创建类别到ID的映射
        class_id_mapping = {class_name: idx for idx, class_name in enumerate(target_classes)} if target_classes else {}
        
        for i, row in tqdm(enumerate(filtered_data), total=len(filtered_data), desc="处理数据"):
            try:
                result = self._process_single_image(row, images_dir, labels_dir, debug_dir, processing_config, class_id_mapping)
                results.append(result)
                self.processed_count += 1
                
                if result.get('success', False):
                    self.success_count += 1
                else:
                    self.skipped_count += 1
                    
                if (i + 1) % 10 == 0:
                    self.logger.debug(f"已处理 {i + 1}/{len(filtered_data)} 张图片")
                    
            except Exception as e:
                self.logger.error(f"处理图片失败: {e}")
                self.error_count += 1
                results.append({'success': False, 'error': str(e)})
        
        # 生成dataset.yaml和classes.txt
        if target_classes:
            self._generate_dataset_files(output_dir, target_classes)
        
        return results
    
    def _apply_priority_files_filter(self, data: List[Dict[str, Any]], priority_config: Dict[str, Any], max_count: int) -> List[Dict[str, Any]]:
        """应用优先文件筛选
        
        Args:
            data: 原始数据
            priority_config: 优先文件配置
            max_count: 最大处理数量
            
        Returns:
            List[Dict[str, Any]]: 筛选后的数据
        """
        priority_files = priority_config.get('file_list', [])
        match_field = priority_config.get('match_field', 'intefile_name')
        
        if not priority_files:
            self.logger.debug("优先文件列表为空，跳过优先文件处理")
            return data[:max_count] if max_count > 0 else data
        
        # 转换为DataFrame以便处理
        df = pd.DataFrame(data)
        
        if match_field not in df.columns:
            self.logger.warning(f"匹配字段 '{match_field}' 不存在于CSV数据中")
            return data[:max_count] if max_count > 0 else data
        
        # 分离优先文件和普通文件
        priority_df = df[df[match_field].isin(priority_files)]
        normal_df = df[~df[match_field].isin(priority_files)]
        
        # 检查优先文件匹配状态
        matched_files = set(priority_df[match_field].tolist())
        unmatched_files = [f for f in priority_files if f not in matched_files]
        
        self.logger.debug(f"优先文件配置 {len(priority_files)} 个，匹配到 {len(priority_df)} 条记录")
        if unmatched_files:
            self.logger.debug(f"未匹配到的优先文件 ({len(unmatched_files)} 个):")
            for filename in unmatched_files:
                self.logger.debug(f"  - {filename}")
        else:
            self.logger.debug("所有优先文件都匹配成功")
        
        # 按优先文件列表顺序排序
        if len(priority_df) > 0:
            ordered_dfs = []
            for filename in priority_files:
                matching_rows = priority_df[priority_df[match_field] == filename]
                if len(matching_rows) > 0:
                    ordered_dfs.append(matching_rows)
            if ordered_dfs:
                priority_df = pd.concat(ordered_dfs, ignore_index=True)
        
        # 合并：优先文件在前，普通文件在后
        df = pd.concat([priority_df, normal_df], ignore_index=True)
        
        # 处理数量限制：优先文件不受限制，普通文件补充到上限
        if max_count > 0:
            if len(priority_df) >= max_count:
                df = priority_df.head(max_count)
                self.logger.debug(f"优先文件已达上限，处理 {len(df)} 条记录")
            else:
                remaining_count = max_count - len(priority_df)
                df = pd.concat([
                    priority_df, 
                    normal_df.head(remaining_count)
                ], ignore_index=True)
                self.logger.debug(f"优先文件 {len(priority_df)} 条 + 普通文件 {len(df) - len(priority_df)} 条，共 {len(df)} 条记录")
        
        # 转换回字典列表格式
        return [row.to_dict() for _, row in df.iterrows()]
    
    def _filter_csv_data(self, data: List[Dict[str, Any]], target_classes: List[str]) -> List[Dict[str, Any]]:
        """过滤CSV数据
        
        Args:
            data: 原始数据
            target_classes: 目标类别列表
            
        Returns:
            List[Dict[str, Any]]: 过滤后的数据
        """
        if not target_classes:
            return data
        
        filtered = []
        for row in data:
            # 支持多种字段名
            class_field = row.get('business_type', row.get('class', '')).strip()
            
            # 支持多类别情况，用逗号分隔
            business_types = [bt.strip() for bt in str(class_field).split(',')]
            
            # 检查是否包含任何目标类别
            if any(bt in target_classes for bt in business_types):
                filtered.append(row)
        
        self.logger.debug(f"类别过滤: {len(data)} -> {len(filtered)}")
        return filtered
    
    def _process_single_image(
        self, 
        row: Dict[str, Any], 
        images_dir: str, 
        labels_dir: str, 
        debug_dir: Optional[str],
        config: Dict[str, Any],
        class_id_mapping: Dict[str, int]
    ) -> Dict[str, Any]:
        """处理单张图片
        
        Args:
            row: CSV行数据
            images_dir: 图片输出目录
            labels_dir: 标签输出目录
            debug_dir: 调试输出目录
            config: 处理配置
            class_id_mapping: 类别ID映射
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        # 获取图片路径和类别信息
        original_filename = row.get('initfile_name', '')
        annotated_filename = row.get('intefile_name', '')
        business_type = str(row.get('business_type', ''))
        
        # 解析目标类别 - 支持多类别
        target_class_names = []
        
        # 处理多类别情况
        business_types = [bt.strip() for bt in business_type.split(',')]
        target_classes = list(class_id_mapping.keys())
        
        # 找到所有匹配的目标类别
        for bt in business_types:
            if bt in target_classes:
                target_class_names.append(bt)
        
        if not target_class_names:
            return {
                'success': False, 
                'reason': '未找到目标类别',
                'original_filename': original_filename,
                'annotated_filename': annotated_filename
            }

        # 构建图片路径
        data_source_config = self.config_manager.get_backup_annotation_config().get('data_source', {})
        image_dir = data_source_config.get('image_dir', '')
        
        original_path = os.path.join(image_dir, "initfile_unique", original_filename)
        annotated_path = os.path.join(image_dir, "intefile_unique", annotated_filename)
        
        if not os.path.exists(original_path) or not os.path.exists(annotated_path):
            return {
                'success': False, 
                'reason': '图片文件不存在',
                'original_filename': original_filename,
                'annotated_filename': annotated_filename
            }
        
        # 读取图片
        try:
            original_img = cv2.imread(original_path)
            annotated_img = cv2.imread(annotated_path)
            
            if original_img is None or annotated_img is None:
                return {
                    'success': False, 
                    'reason': '图片读取失败',
                    'original_filename': original_filename,
                    'annotated_filename': annotated_filename
                }
                
            img_height, img_width = original_img.shape[:2]
            
        except Exception as e:
            return {
                'success': False, 
                'reason': f'图片处理错误: {e}',
                'original_filename': original_filename,
                'annotated_filename': annotated_filename
            }
        
        # 执行图像标注检测 - 传入所有目标类别
        detection_result = self._process_image_annotation(
            original_img, annotated_img, target_class_names, class_id_mapping,
            config, debug_dir, annotated_filename
        )
        
        if not detection_result or not detection_result.get('annotations'):
            return {
                'success': False, 
                'reason': '未检测到有效标注',
                'original_filename': original_filename,
                'annotated_filename': annotated_filename
            }
        
        # 复制原图到输出目录
        output_image_path = os.path.join(images_dir, original_filename)
        shutil.copy2(original_path, output_image_path)
        
        # 生成YOLO标注文件
        label_name = os.path.splitext(original_filename)[0] + '.txt'
        label_path = os.path.join(labels_dir, label_name)
        
        try:
            with open(label_path, 'w', encoding='utf-8') as f:
                for annotation in detection_result['annotations']:
                    f.write(annotation + '\n')
        except Exception as e:
            return {
                'success': False, 
                'reason': f'标注文件写入失败: {e}',
                'original_filename': original_filename,
                'annotated_filename': annotated_filename
            }
        
        return {
            'success': True,
            'image_path': output_image_path,
            'label_path': label_path,
            'detections_count': len(detection_result['annotations']),
            'original_data': row,
            'class_names': target_class_names,  # 返回所有匹配的类别
            'class_detection_summary': detection_result.get('class_detection_summary', {})
        }
    
    def _process_image_annotation(
        self, 
        original_img: np.ndarray, 
        annotated_img: np.ndarray, 
        target_class_names: List[str],
        class_id_mapping: Dict[str, int],
        config: Dict[str, Any],
        debug_dir: Optional[str],
        filename: str
    ) -> Optional[Dict[str, Any]]:
        """处理图像标注检测 - 支持多类别识别
        
        Args:
            original_img: 原始图像
            annotated_img: 标注图像
            target_class_names: 目标类别名称列表
            class_id_mapping: 类别ID映射
            config: 配置参数
            debug_dir: 调试目录
            filename: 文件名
            
        Returns:
            Optional[Dict[str, Any]]: 检测结果
        """
        self.logger.info(f"处理图像: {filename}, 目标类别: {target_class_names}")
        img_height, img_width = original_img.shape[:2]
        
        # 获取轮廓合并策略
        strategy = config.get('contour_merge_strategy', 'union')
        
        # 步骤1：图像比较检测
        comparison_contours = []
        if strategy != 'color_only' and config.get('image_comparison', {}).get('enabled', True):
            comparison_contours = self._compare_images_for_annotations(
                original_img, annotated_img, config.get('image_comparison', {})
            )
        
        # 步骤2：目标类别颜色检测 - 只检测目标类别
        all_color_contours = {}
        if strategy != 'comparison_only' and config.get('color_detection', {}).get('enabled', True):
            color_ranges = config.get('color_detection', {}).get('color_ranges', {})
            for class_name in target_class_names:  # 只遍历目标类别
                if class_name in color_ranges:  # 确保类别在配置中存在
                    contours = self._detect_color_annotations(
                        annotated_img, class_name, config.get('color_detection', {})
                    )
                    if contours:
                        all_color_contours[class_name] = contours
        
        # 步骤3：为每个类别生成标注
        all_annotations = []
        all_filtered_contours = []
        class_detection_summary = {}
        
        # 处理所有检测到的类别
        for class_name, color_contours in all_color_contours.items():
            class_id = class_id_mapping.get(class_name, 0)  # 使用传入的class_id_mapping
            
            # 合并检测结果
            final_contours = self._merge_detection_results(comparison_contours, color_contours, strategy)
            
            if not final_contours:
                continue
            
            # 过滤轮廓并生成标注
            min_area = config.get('multi_object', {}).get('min_area_threshold', 500)
            class_annotations = []
            class_filtered_contours = []
            
            for contour in final_contours:
                area = cv2.contourArea(contour)
                if area >= min_area:
                    # 生成YOLO标注 - 使用矩形边界框
                    yolo_annotation = self._generate_yolo_annotation_from_bbox(
                        contour, img_width, img_height, class_id
                    )
                    if yolo_annotation:
                        class_annotations.append(yolo_annotation)
                        class_filtered_contours.append((contour, class_name))
            
            if class_annotations:
                all_annotations.extend(class_annotations)
                all_filtered_contours.extend(class_filtered_contours)
                class_detection_summary[class_name] = {
                    'count': len(class_annotations),
                    'class_id': class_id
                }
        
        if not all_annotations:
            return None
        
        # 后处理：合并和过滤检测结果
        processed_contours, processed_annotations = self._post_process_detections(
            all_filtered_contours, all_annotations, img_width, img_height, config
        )
        
        # 调试模式：生成可视化图像 - 显示所有类别的检测结果
        if debug_dir and processed_annotations:
            self._generate_multi_class_debug_image(
                original_img, annotated_img, processed_contours, 
                debug_dir, filename, config
            )
            
            # 输出每个有效识别对象的边界框坐标
            self._output_detection_coordinates(processed_contours, filename, img_width, img_height)
        
        return {
            'annotations': processed_annotations,
            'contours_count': len(processed_contours),
            'class_detection_summary': class_detection_summary,
            'matched_categories': list(class_detection_summary.keys())
        }
    
    def _compare_images_for_annotations(
        self, 
        original_img: np.ndarray, 
        annotated_img: np.ndarray, 
        config: Dict[str, Any]
    ) -> List[Any]:
        """通过图像比较检测标注区域"""
        try:
            # 确保两图尺寸一致
            if original_img.shape != annotated_img.shape:
                annotated_img = cv2.resize(annotated_img, 
                                         (original_img.shape[1], original_img.shape[0]))
            
            # 计算图片差异
            diff = cv2.absdiff(original_img, annotated_img)
            
            # 转换为灰度图
            gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
            
            # 应用阈值化
            threshold = config.get('threshold', 30)
            _, binary = cv2.threshold(gray_diff, threshold, 255, cv2.THRESH_BINARY)
            
            # 形态学操作去噪声
            kernel = np.ones((3, 3), np.uint8)
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤轮廓
            min_area = config.get('min_contour_area', 100)
            max_area = config.get('max_contour_area', 50000)
            
            filtered_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if min_area <= area <= max_area:
                    filtered_contours.append(contour)
            
            return filtered_contours
            
        except Exception as e:
            self.logger.error(f"图像比较失败: {e}")
            return []
    
    def _detect_color_annotations(
        self, 
        annotated_img: np.ndarray, 
        target_class_name: str,
        config: Dict[str, Any]
    ) -> List[Any]:
        """检测颜色标注 - 改进版本，支持重叠方框分离"""
        try:
            hsv = cv2.cvtColor(annotated_img, cv2.COLOR_BGR2HSV)
            color_ranges = config.get('color_ranges', {})
            
            if target_class_name not in color_ranges:
                return []
            
            color_config = color_ranges[target_class_name]
            
            # 支持新的 hsv_range 格式
            if 'hsv_range' in color_config:
                hsv_range = color_config['hsv_range']
                lower = np.array(hsv_range[0])
                upper = np.array(hsv_range[1])
            else:
                # 兼容旧格式
                lower = np.array(color_config.get('lower', [0, 0, 0]))
                upper = np.array(color_config.get('upper', [255, 255, 255]))
            
            # 创建掩码
            mask = cv2.inRange(hsv, lower, upper)
            
            # 获取重叠分离配置
            overlap_config = self.config_manager.get_backup_annotation_config().get('processing', {}).get('overlap_separation', {})
            
            if overlap_config.get('enabled', False):
                # 使用改进的处理方法
                return self._detect_color_annotations_improved(mask, overlap_config)
            else:
                # 使用原始方法
                return self._detect_color_annotations_original(mask)
            
        except Exception as e:
            self.logger.error(f"颜色检测失败: {e}")
            return []
    
    def _detect_color_annotations_original(self, mask: np.ndarray) -> List[Any]:
        """原始颜色检测方法"""
        # 形态学操作
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        return list(contours)
    
    def _detect_color_annotations_improved(self, mask: np.ndarray, overlap_config: Dict[str, Any]) -> List[Any]:
        """改进的颜色标注检测，处理重叠方框"""
        strategy = overlap_config.get('strategy', 'watershed')
        
        if strategy == 'morphology':
            return self._morphology_separation(mask, overlap_config)
        elif strategy == 'watershed':
            return self._watershed_separation_method(mask, overlap_config)
        elif strategy == 'corner_clustering':
            return self._corner_clustering_method(mask, overlap_config)
        elif strategy == 'template_matching':
            return self._template_matching_method(mask, overlap_config)
        else:
            self.logger.warning(f"未知的重叠分离策略: {strategy}，使用默认方法")
            return self._detect_color_annotations_original(mask)
    
    def _merge_detection_results(
        self, 
        comparison_contours: List[Any], 
        color_contours: List[Any], 
        strategy: str
    ) -> List[Any]:
        """合并检测结果"""
        if strategy == 'comparison_only':
            return comparison_contours
        elif strategy == 'color_only':
            return color_contours
        elif strategy == 'union':
            return comparison_contours + color_contours
        elif strategy == 'intersection':
            # 简化实现：返回两个集合都有检测结果时的联合
            if comparison_contours and color_contours:
                return comparison_contours + color_contours
            else:
                return []
        else:
            return comparison_contours + color_contours
    
    def _generate_yolo_annotation_from_bbox(
        self, 
        contour: np.ndarray, 
        img_width: int, 
        img_height: int, 
        class_id: int
    ) -> Optional[str]:
        """生成YOLO格式标注（使用矩形边界框）"""
        try:
            # 获取边界框 - 使用矩形边界框而不是轮廓
            x, y, w, h = cv2.boundingRect(contour)
            
            # 转换为YOLO格式 (归一化的中心坐标和尺寸)
            center_x = (x + w / 2) / img_width
            center_y = (y + h / 2) / img_height
            norm_width = w / img_width
            norm_height = h / img_height
            
            # 确保坐标在合理范围内
            center_x = max(0, min(1, center_x))
            center_y = max(0, min(1, center_y))
            norm_width = max(0, min(1, norm_width))
            norm_height = max(0, min(1, norm_height))
            
            return f"{class_id} {center_x:.6f} {center_y:.6f} {norm_width:.6f} {norm_height:.6f}"
            
        except Exception as e:
            self.logger.error(f"生成YOLO标注失败: {e}")
            return None
    
    def _generate_multi_class_debug_image(
        self, 
        original_img: np.ndarray, 
        annotated_img: np.ndarray, 
        contours_with_classes: List[Tuple[Any, str]],
        debug_dir: str,
        filename: str,
        config: Dict[str, Any]
    ) -> None:
        """生成多类别调试对比图像
        
        Args:
            contours_with_classes: 轮廓和对应类别名的元组列表
        """
        try:
            # 创建检测结果图
            detection_img = original_img.copy()
            
            # 获取类别对应的颜色配置
            color_map = {
                'excavator': (0, 255, 255),    # 黄色 (BGR格式)
                'hpipe': (0, 0, 255),          # 红色 
                'boat': (255, 0, 0),           # 蓝色
            }
            
            # 按类别统计
            class_counts = {}
            color_ranges = config.get('color_detection', {}).get('color_ranges', {})
            
            for i, (contour, class_name) in enumerate(contours_with_classes):
                # 获取class_code和颜色
                class_code = color_ranges.get(class_name, {}).get('class_code', class_name)
                draw_color = color_map.get(class_code, (0, 255, 0))  # 默认绿色
                
                # 统计每个类别的数量
                if class_code not in class_counts:
                    class_counts[class_code] = 0
                class_counts[class_code] += 1
                
                # 绘制矩形边界框
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(detection_img, (x, y), (x + w, y + h), draw_color, 2)
                
                # 添加标签
                label_text = f"{class_code}_{class_counts[class_code]}"
                cv2.putText(detection_img, label_text, (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, draw_color, 2)
            
            # 调整两图尺寸一致
            h = max(annotated_img.shape[0], detection_img.shape[0])
            w = max(annotated_img.shape[1], detection_img.shape[1])
            
            annotated_resized = cv2.resize(annotated_img, (w, h))
            detection_resized = cv2.resize(detection_img, (w, h))
            
            # 添加标题
            title_height = 30
            total_height = h + title_height
            
            # 创建带标题的图像
            annotated_titled = np.zeros((total_height, w, 3), dtype=np.uint8)
            annotated_titled[title_height:, :] = annotated_resized
            cv2.putText(annotated_titled, "Annotated Image", (10, 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            
            detection_titled = np.zeros((total_height, w, 3), dtype=np.uint8)
            detection_titled[title_height:, :] = detection_resized
            
            # 生成检测摘要文本
            summary_parts = []
            for class_code, count in class_counts.items():
                summary_parts.append(f"{class_code}:{count}")
            summary_text = f"Detection ({', '.join(summary_parts)})"
            
            cv2.putText(detection_titled, summary_text, (10, 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            
            # 水平拼接两张图像
            comparison_img = np.hstack([annotated_titled, detection_titled])
            
            debug_path = os.path.join(debug_dir, f"debug_{filename}")
            cv2.imwrite(debug_path, comparison_img)
            
        except Exception as e:
            self.logger.error(f"生成多类别调试图像失败: {e}")

    def _generate_debug_image(
        self, 
        original_img: np.ndarray, 
        annotated_img: np.ndarray, 
        contours: List[Any],
        debug_dir: str,
        filename: str,
        class_code: str,
        class_name: str
    ) -> None:
        """生成调试对比图像
        
        Args:
            contours: 经过面积过滤后、成功生成YOLO标注的轮廓列表
        """
        try:
            # 创建检测结果图
            detection_img = original_img.copy()
            
            # 获取类别对应的颜色配置
            color_map = {
                'excavator': (0, 255, 255),    # 黄色 (BGR格式)
                'hpipe': (0, 0, 255),          # 红色 
                'boat': (255, 0, 0),           # 蓝色
            }
            # 默认使用绿色
            draw_color = color_map.get(class_code, (0, 255, 0))
            
            for i, contour in enumerate(contours):
                # 绘制矩形边界框而不是轮廓
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(detection_img, (x, y), (x + w, y + h), draw_color, 2)
                
                # 添加标签
                label_text = f"{class_code}_{i+1}"
                cv2.putText(detection_img, label_text, (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, draw_color, 2)
            
            # 调整两图尺寸一致
            h = max(annotated_img.shape[0], detection_img.shape[0])
            w = max(annotated_img.shape[1], detection_img.shape[1])
            
            annotated_resized = cv2.resize(annotated_img, (w, h))
            detection_resized = cv2.resize(detection_img, (w, h))
            
            # 添加标题
            title_height = 30
            total_height = h + title_height
            
            # 创建带标题的图像
            annotated_titled = np.zeros((total_height, w, 3), dtype=np.uint8)
            annotated_titled[title_height:, :] = annotated_resized
            cv2.putText(annotated_titled, "Annotated Image", (10, 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            
            detection_titled = np.zeros((total_height, w, 3), dtype=np.uint8)
            detection_titled[title_height:, :] = detection_resized
            cv2.putText(detection_titled, f"Detection ({len(contours)} objects)", (10, 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            
            # 水平拼接两张图像
            comparison_img = np.hstack([annotated_titled, detection_titled])
            
            debug_path = os.path.join(debug_dir, f"debug_{filename}")
            cv2.imwrite(debug_path, comparison_img)
            
        except Exception as e:
            self.logger.error(f"生成调试图像失败: {e}")
    
    def _generate_dataset_files(self, output_dir: str, target_classes: List[str]) -> None:
        """生成dataset.yaml和classes.txt文件"""
        try:
            # 生成dataset.yaml
            yaml_content = f"""# YOLO数据集配置
path: .
train: images
val: images

# 类别数量
nc: {len(target_classes)}

# 类别名称
names: {target_classes}
"""
            yaml_path = os.path.join(output_dir, 'dataset.yaml')
            with open(yaml_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)
            
            # 生成classes.txt
            classes_path = os.path.join(output_dir, 'classes.txt')
            with open(classes_path, 'w', encoding='utf-8') as f:
                for class_name in target_classes:
                    f.write(f"{class_name}\n")
            
            self.logger.info(f"生成数据集配置文件: {yaml_path}")
            self.logger.info(f"生成类别文件: {classes_path}")
            
        except Exception as e:
            self.logger.error(f"生成数据集文件失败: {e}")
    
    def _generate_validation_materials(
        self, 
        results: List[Dict[str, Any]], 
        output_dir: str, 
        config: Dict[str, Any]
    ) -> None:
        """生成验证材料"""
        try:
            validation_config = config.get('validation', {})
            
            # 生成评估模板
            if validation_config.get('create_evaluation_template', True):
                self._create_evaluation_template(results, output_dir)
            
            # 生成处理日志
            self._create_processing_log(results, output_dir)
            
        except Exception as e:
            self.logger.error(f"生成验证材料失败: {e}")
    
    def _create_evaluation_template(self, results: List[Dict[str, Any]], output_dir: str) -> None:
        """创建评估模板"""
        template_path = os.path.join(output_dir, 'evaluation_template.csv')
        
        with open(template_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['image_path', 'detections_count', 'status', 'evaluation_score', 'comments'])
            
            for result in results:
                if result.get('success', False):
                    writer.writerow([
                        result.get('image_path', ''),
                        result.get('detections_count', 0),
                        'success',
                        '',  # 待评估
                        ''   # 待添加注释
                    ])
        
        self.logger.info(f"生成评估模板: {template_path}")
    
    def _create_processing_log(self, results: List[Dict[str, Any]], output_dir: str) -> None:
        """创建处理日志"""
        log_path = os.path.join(output_dir, 'processing_log.txt')
        
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(f"备份标注转换处理日志\n")
            f.write(f"=" * 50 + "\n")
            f.write(f"总处理数量: {self.processed_count}\n")
            f.write(f"成功数量: {self.success_count}\n")
            f.write(f"跳过数量: {self.skipped_count}\n")
            f.write(f"错误数量: {self.error_count}\n")
            if self.processed_count > 0:
                success_rate = self.success_count / self.processed_count * 100
                f.write(f"成功率: {success_rate:.2f}%\n")
            f.write(f"\n详细结果:\n")
            f.write(f"-" * 30 + "\n")
            
            for i, result in enumerate(results, 1):
                f.write(f"{i}. {result.get('image_path', result.get('original_filename', 'unknown'))}\n")
                f.write(f"   状态: {'成功' if result.get('success', False) else '失败'}\n")
                f.write(f"   检测数量: {result.get('detections_count', 0)}\n")
                if not result.get('success', False):
                    f.write(f"   失败原因: {result.get('reason', result.get('error', 'unknown'))}\n")
                f.write("\n")
        
        self.logger.info(f"生成处理日志: {log_path}")
    
    def _output_detection_coordinates(self, contours_with_classes: List[Tuple[Any, str]], 
                                    filename: str, img_width: int, img_height: int) -> None:
        """输出每个有效识别对象的边界框坐标
        
        Args:
            contours_with_classes: 轮廓和对应类别名的元组列表
            filename: 图片文件名
            img_width: 图片宽度
            img_height: 图片高度
        """
        try:
            if not contours_with_classes:
                self.logger.debug(f"[{filename}] 无有效识别对象")
                return
            
            self.logger.info(f"[{filename}] 检测到 {len(contours_with_classes)} 个有效对象:")
            self.logger.debug(f"[{filename}] 图片尺寸: {img_width} x {img_height}")
            
            for i, (contour, class_name) in enumerate(contours_with_classes, 1):
                # 获取边界框坐标
                x, y, w, h = cv2.boundingRect(contour)
                
                # 计算四个角的坐标
                top_left = (x, y)
                top_right = (x + w, y)
                bottom_left = (x, y + h)
                bottom_right = (x + w, y + h)
                
                # 计算面积
                area = w * h
                
                # 计算相对于图片的比例
                width_ratio = w / img_width
                height_ratio = h / img_height
                area_ratio = area / (img_width * img_height)
                
                self.logger.debug(f"[{filename}] 对象 {i} ({class_name}):")
                self.logger.debug(f"  - 边界框: x={x}, y={y}, w={w}, h={h}")
                self.logger.debug(f"  - 四角坐标: 左上{top_left}, 右上{top_right}, 左下{bottom_left}, 右下{bottom_right}")
                self.logger.debug(f"  - 面积: {area} 像素 ({area_ratio:.4f} 图片比例)")
                self.logger.debug(f"  - 尺寸比例: 宽度 {width_ratio:.4f}, 高度 {height_ratio:.4f}")
                
        except Exception as e:
            self.logger.error(f"输出检测坐标失败: {e}")
    
    def _post_process_detections(self, contours_with_classes: List[Tuple[Any, str]], 
                               annotations: List[str], img_width: int, img_height: int, 
                               config: Dict[str, Any]) -> Tuple[List[Tuple[Any, str]], List[str]]:
        """后处理检测结果：三步处理流程
        
        Args:
            contours_with_classes: 轮廓和类别的元组列表
            annotations: YOLO标注列表
            img_width: 图片宽度
            img_height: 图片高度
            config: 配置参数
            
        Returns:
            Tuple[List[Tuple[Any, str]], List[str]]: 处理后的轮廓和标注列表
        """
        try:
            if not contours_with_classes:
                return contours_with_classes, annotations
            
            # 获取后处理配置
            post_process_config = config.get('post_processing', {})
            original_count = len(contours_with_classes)
            
            # 步骤1：面积过滤（上下限）
            step1_contours, step1_annotations = self._filter_objects_by_area(
                contours_with_classes, annotations, img_width, img_height, post_process_config
            )
            step1_count = len(step1_contours)
            
            # 步骤2：边界扩充合并
            step2_contours, step2_annotations = self._boundary_expansion_merge(
                step1_contours, step1_annotations, img_width, img_height, post_process_config
            )
            step2_count = len(step2_contours)
            
            # 步骤3：重叠合并（包含面积重叠）
            step3_contours, step3_annotations = self._overlap_area_merge(
                step2_contours, step2_annotations, img_width, img_height, post_process_config
            )
            final_count = len(step3_contours)
            
            # 输出处理统计
            if original_count != final_count:
                self.logger.debug(f"后处理完成：{original_count} -> 步骤1:{step1_count} -> 步骤2:{step2_count} -> 最终:{final_count} 个对象")
            
            return step3_contours, step3_annotations
            
        except Exception as e:
            self.logger.error(f"后处理检测结果失败: {e}")
            return contours_with_classes, annotations
    
    def _filter_objects_by_area(self, contours_with_classes: List[Tuple[Any, str]], 
                               annotations: List[str], img_width: int, img_height: int,
                               config: Dict[str, Any]) -> Tuple[List[Tuple[Any, str]], List[str]]:
        """步骤1：面积过滤（上下限）"""
        try:
            max_area_ratio = config.get('max_area_ratio', 0.9)
            min_area_threshold = config.get('min_area_threshold', 500)
            total_area = img_width * img_height
            
            filtered_contours = []
            filtered_annotations = []
            
            for i, (contour, class_name) in enumerate(contours_with_classes):
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                area_ratio = area / total_area
                
                # 检查面积上限
                if area_ratio > max_area_ratio:
                    self.logger.debug(f"过滤异常大对象：面积比例 {area_ratio:.4f} > {max_area_ratio}")
                    continue
                
                # 检查面积下限
                if area < min_area_threshold:
                    self.logger.debug(f"过滤异常小对象：面积 {area} < {min_area_threshold}")
                    continue
                
                filtered_contours.append((contour, class_name))
                if i < len(annotations):
                    filtered_annotations.append(annotations[i])
            
            return filtered_contours, filtered_annotations
            
        except Exception as e:
            self.logger.error(f"面积过滤失败: {e}")
            return contours_with_classes, annotations
    
    def _boundary_expansion_merge(self, contours_with_classes: List[Tuple[Any, str]], 
                                annotations: List[str], img_width: int, img_height: int,
                                config: Dict[str, Any]) -> Tuple[List[Tuple[Any, str]], List[str]]:
        """步骤2：边界扩充合并 - 处理边界重叠的情况"""
        try:
            if len(contours_with_classes) < 2:
                return contours_with_classes, annotations
            
            # 获取边界扩充参数
            boundary_config = config.get('boundary_expansion', {})
            edge_tolerance = boundary_config.get('edge_tolerance', 2)  # 边界容差
            
            # 创建边界框信息列表
            bbox_info = []
            for i, (contour, class_name) in enumerate(contours_with_classes):
                x, y, w, h = cv2.boundingRect(contour)
                bbox_info.append({
                    'index': i,
                    'contour': contour,
                    'class_name': class_name,
                    'x': x, 'y': y, 'w': w, 'h': h,
                    'x2': x + w, 'y2': y + h,
                    'processed': False
                })
            
            # 按类别分组处理
            class_groups = {}
            for bbox in bbox_info:
                class_name = bbox['class_name']
                if class_name not in class_groups:
                    class_groups[class_name] = []
                class_groups[class_name].append(bbox)
            
            result_contours = []
            result_annotations = []
            
            for class_name, class_bboxes in class_groups.items():
                # 第一步：查找一个bbox与另外两个bbox都有重叠的组合
                for i, bbox_a in enumerate(class_bboxes):
                    if bbox_a['processed']:
                        continue
                    
                    # 查找与bbox_a有重叠的所有其他对象
                    overlapping_bboxes = []
                    for j, bbox_other in enumerate(class_bboxes):
                        if i == j or bbox_other['processed']:
                            continue
                        
                        # 检查是否有重叠（包括边界重叠）
                        if self._has_any_overlap(bbox_a, bbox_other, edge_tolerance):
                            overlapping_bboxes.append(bbox_other)
                    
                    # 如果找到至少两个与bbox_a重叠的对象，形成三元组合
                    if len(overlapping_bboxes) >= 2:
                        # 第二步：检查这个组合是否满足扩充边界的条件
                        expansion_group = [bbox_a]
                        
                        for bbox_b in overlapping_bboxes:
                            for bbox_c in overlapping_bboxes:
                                if bbox_b == bbox_c:
                                    continue
                                
                                # 检查三元组合是否满足边界扩充条件
                                if self._should_boundary_expand(bbox_a, bbox_b, bbox_c, edge_tolerance):
                                    if bbox_b not in expansion_group:
                                        expansion_group.append(bbox_b)
                                    if bbox_c not in expansion_group:
                                        expansion_group.append(bbox_c)
                        
                        if len(expansion_group) > 1:
                            # 确保每个bbox都包含class_name信息
                            for bbox in expansion_group:
                                if 'class_name' not in bbox:
                                    bbox['class_name'] = class_name
                            
                            # 执行边界扩充合并
                            expanded_bboxes = self._expand_merge_bboxes(expansion_group)
                            if expanded_bboxes:
                                # 处理返回的多个边界框
                                for expanded_bbox in expanded_bboxes:
                                    result_contours.append((expanded_bbox['contour'], class_name))
                                    
                                    # 生成新的YOLO标注
                                    expanded_annotation = self._generate_yolo_annotation_from_bbox(
                                        expanded_bbox['contour'], img_width, img_height, 
                                        self._get_class_id_from_annotation(annotations[bbox_a['index']])
                                    )
                                    if expanded_annotation:
                                        result_annotations.append(expanded_annotation)
                                
                                self.logger.debug(f"边界扩充合并 {len(expansion_group)} 个{class_name}对象")
                                
                                # 标记为已处理
                                for bbox in expansion_group:
                                    bbox['processed'] = True
                        else:
                            # 不满足扩充条件，保持原样
                            result_contours.append((bbox_a['contour'], class_name))
                            if bbox_a['index'] < len(annotations):
                                result_annotations.append(annotations[bbox_a['index']])
                            bbox_a['processed'] = True
                    else:
                        # 没有足够的重叠对象，保持原样
                        result_contours.append((bbox_a['contour'], class_name))
                        if bbox_a['index'] < len(annotations):
                            result_annotations.append(annotations[bbox_a['index']])
                        bbox_a['processed'] = True
            
            return result_contours, result_annotations
            
        except Exception as e:
            self.logger.error(f"边界扩充合并失败: {e}")
            return contours_with_classes, annotations
    
    def _overlap_area_merge(self, contours_with_classes: List[Tuple[Any, str]], 
                          annotations: List[str], img_width: int, img_height: int,
                          config: Dict[str, Any]) -> Tuple[List[Tuple[Any, str]], List[str]]:
        """步骤3：重叠合并（包含面积重叠）"""
        try:
            if len(contours_with_classes) < 2:
                return contours_with_classes, annotations
            
            # 获取合并参数
            merge_config = config.get('merge_params', {})
            vertical_gap_threshold = merge_config.get('vertical_gap_threshold', 5)  # 垂直间隙阈值
            width_similarity_threshold = merge_config.get('width_similarity_threshold', 0.05)  # 宽度相似度阈值
            
            # 创建边界框信息列表
            bbox_info = []
            for i, (contour, class_name) in enumerate(contours_with_classes):
                x, y, w, h = cv2.boundingRect(contour)
                bbox_info.append({
                    'index': i,
                    'contour': contour,
                    'class_name': class_name,
                    'x': x, 'y': y, 'w': w, 'h': h,
                    'x2': x + w, 'y2': y + h,
                    'area': w * h,
                    'merged': False
                })
            
            # 按类别分组处理
            class_groups = {}
            for bbox in bbox_info:
                class_name = bbox['class_name']
                if class_name not in class_groups:
                    class_groups[class_name] = []
                class_groups[class_name].append(bbox)
            
            merged_contours = []
            merged_annotations = []
            
            for class_name, class_bboxes in class_groups.items():
                # 在同一类别内查找需要合并的对象
                for i, bbox1 in enumerate(class_bboxes):
                    if bbox1['merged']:
                        continue
                    
                    merge_candidates = [bbox1]
                    
                    for j, bbox2 in enumerate(class_bboxes):
                        if i == j or bbox2['merged']:
                            continue
                        
                        # 检查是否需要合并（包含面积重叠检测）
                        if self._should_merge_bboxes_advanced(bbox1, bbox2, vertical_gap_threshold, width_similarity_threshold, merge_config):
                            merge_candidates.append(bbox2)
                            bbox2['merged'] = True
                    
                    if len(merge_candidates) > 1:
                        # 合并多个边界框
                        merged_bbox = self._merge_bboxes(merge_candidates)
                        if merged_bbox:
                            merged_contours.append((merged_bbox['contour'], class_name))
                            
                            # 生成新的YOLO标注
                            merged_annotation = self._generate_yolo_annotation_from_bbox(
                                merged_bbox['contour'], img_width, img_height, 
                                self._get_class_id_from_annotation(annotations[bbox1['index']])
                            )
                            if merged_annotation:
                                merged_annotations.append(merged_annotation)
                            
                            self.logger.debug(f"合并 {len(merge_candidates)} 个{class_name}对象")
                    else:
                        # 不需要合并，保持原样
                        merged_contours.append((bbox1['contour'], class_name))
                        if bbox1['index'] < len(annotations):
                            merged_annotations.append(annotations[bbox1['index']])
                    
                    bbox1['merged'] = True
            
            return merged_contours, merged_annotations
            
        except Exception as e:
            self.logger.error(f"合并重叠对象失败: {e}")
            return contours_with_classes, annotations
    
    def _should_merge_bboxes(self, bbox1: Dict, bbox2: Dict, vertical_gap_threshold: int, 
                           width_similarity_threshold: float) -> bool:
        """判断两个边界框是否应该合并"""
        try:
            # 情况1：内部重叠 - 一个完全在另一个内部
            if self._is_bbox_inside(bbox1, bbox2) or self._is_bbox_inside(bbox2, bbox1):
                return True
            
            # 情况2：垂直相邻且宽度相似
            if self._are_vertically_adjacent(bbox1, bbox2, vertical_gap_threshold) and \
               self._have_similar_width(bbox1, bbox2, width_similarity_threshold):
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"判断边界框合并失败: {e}")
            return False
    
    def _should_merge_bboxes_advanced(self, bbox1: Dict, bbox2: Dict, vertical_gap_threshold: int, 
                                    width_similarity_threshold: float, merge_config: Dict) -> bool:
        """高级边界框合并判断（包含面积重叠检测）"""
        try:
            # 原有的合并条件
            if self._should_merge_bboxes(bbox1, bbox2, vertical_gap_threshold, width_similarity_threshold):
                return True
            
            # 新增：面积重叠检测
            area_overlap_threshold = merge_config.get('area_overlap_threshold', 0.9)
            
            # 计算重叠面积比例
            overlap_ratio1 = self._calculate_overlap_ratio(bbox1, bbox2)
            overlap_ratio2 = self._calculate_overlap_ratio(bbox2, bbox1)
            self.logger.debug(f"overlap_ratio1: {overlap_ratio1}, overlap_ratio2: {overlap_ratio2}")
            
            # 如果任一边界框一定比例的面积都在另一个边界框内，则合并
            if overlap_ratio1 >= area_overlap_threshold or overlap_ratio2 >= area_overlap_threshold:
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"高级边界框合并判断失败: {e}")
            return False
    
    def _calculate_overlap_ratio(self, bbox1: Dict, bbox2: Dict) -> float:
        """计算bbox1与bbox2的重叠面积比例（相对于bbox1的面积）"""
        try:
            # 计算重叠区域
            overlap_x1 = max(bbox1['x'], bbox2['x'])
            overlap_y1 = max(bbox1['y'], bbox2['y'])
            overlap_x2 = min(bbox1['x2'], bbox2['x2'])
            overlap_y2 = min(bbox1['y2'], bbox2['y2'])
            
            # 检查是否有重叠
            if overlap_x1 >= overlap_x2 or overlap_y1 >= overlap_y2:
                return 0.0
            
            # 计算重叠面积
            overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
            bbox1_area = bbox1['w'] * bbox1['h']
            
            if bbox1_area == 0:
                return 0.0
            
            return overlap_area / bbox1_area
            
        except Exception as e:
            self.logger.error(f"计算重叠比例失败: {e}")
            return 0.0
    
    def _is_bbox_inside(self, bbox1: Dict, bbox2: Dict) -> bool:
        """判断bbox1是否完全在bbox2内部"""
        return (bbox1['x'] >= bbox2['x'] and bbox1['y'] >= bbox2['y'] and 
                bbox1['x2'] <= bbox2['x2'] and bbox1['y2'] <= bbox2['y2'])
    
    def _are_vertically_adjacent(self, bbox1: Dict, bbox2: Dict, gap_threshold: int) -> bool:
        """判断两个边界框是否垂直相邻"""
        # 检查垂直间隙
        gap1 = abs(bbox1['y2'] - bbox2['y'])  # bbox1底部到bbox2顶部
        gap2 = abs(bbox2['y2'] - bbox1['y'])  # bbox2底部到bbox1顶部
        
        min_gap = min(gap1, gap2)
        return min_gap <= gap_threshold
    
    def _have_similar_width(self, bbox1: Dict, bbox2: Dict, threshold: float) -> bool:
        """判断两个边界框是否有相似的宽度"""
        width_diff = abs(bbox1['w'] - bbox2['w'])
        avg_width = (bbox1['w'] + bbox2['w']) / 2
        return width_diff / avg_width <= threshold
    
    def _merge_bboxes(self, bboxes: List[Dict]) -> Optional[Dict]:
        """合并多个边界框"""
        if not bboxes:
            return None
        
        # 计算合并后的边界框
        min_x = min(bbox['x'] for bbox in bboxes)
        min_y = min(bbox['y'] for bbox in bboxes)
        max_x2 = max(bbox['x2'] for bbox in bboxes)
        max_y2 = max(bbox['y2'] for bbox in bboxes)
        
        merged_w = max_x2 - min_x
        merged_h = max_y2 - min_y
        
        # 创建合并后的轮廓
        merged_contour = np.array([
            [min_x, min_y],
            [max_x2, min_y],
            [max_x2, max_y2],
            [min_x, max_y2]
        ], dtype=np.int32).reshape(-1, 1, 2)
        
        return {
            'contour': merged_contour,
            'x': min_x, 'y': min_y, 'w': merged_w, 'h': merged_h,
            'x2': max_x2, 'y2': max_y2,
            'area': merged_w * merged_h
        }
    
    def _get_class_id_from_annotation(self, annotation: str) -> int:
        """从YOLO标注中提取类别ID"""
        try:
            return int(annotation.split()[0])
        except:
            return 0
    
    def _should_boundary_expand(self, bbox_a: Dict, bbox_b: Dict, bbox_c: Dict, edge_tolerance: int) -> bool:
        """判断是否需要边界扩充合并
        
        检查bbox_a是否与bbox_b和bbox_c存在边界重叠的情况
        例如：bbox_a的底边与bbox_b的底边一致，且bbox_b完全在bbox_c内部
        """
        try:
            # 情况1：检查bbox_b是否完全在bbox_c内部
            b_in_c = self._is_bbox_inside(bbox_b, bbox_c)
            
            # 情况2：检查bbox_a和bbox_b是否有相同的边界（如底边）
            same_bottom = abs(bbox_a['y2'] - bbox_b['y2']) <= edge_tolerance
            same_top = abs(bbox_a['y'] - bbox_b['y']) <= edge_tolerance
            same_left = abs(bbox_a['x'] - bbox_b['x']) <= edge_tolerance
            same_right = abs(bbox_a['x2'] - bbox_b['x2']) <= edge_tolerance
            
            # 情况3：检查bbox_a和bbox_b是否有边界重叠
            x_overlap = not (bbox_a['x2'] <= bbox_b['x'] or bbox_b['x2'] <= bbox_a['x'])
            y_overlap = not (bbox_a['y2'] <= bbox_b['y'] or bbox_b['y2'] <= bbox_a['y'])
            
            # 如果bbox_b在bbox_c内部，且bbox_a与bbox_b有边界重叠或相同边界
            if b_in_c and (same_bottom or same_top or same_left or same_right or (x_overlap and y_overlap)):
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"判断边界扩充失败: {e}")
            return False
    
    def _expand_merge_bboxes(self, bboxes: List[Dict]) -> Optional[List[Dict]]:
        """扩充合并多个边界框
        
        核心逻辑：
        1. 找到面积最大的两个bbox（A和B），第三个C会被去除
        2. 检查是否需要调整A和B的边界：
           - 如果C完全在A内部，A不调整
           - 如果C完全在B内部，B不调整  
           - 如果C与A或B有重合边界但不完全在内部，则扩充那条重合的边界
        """
        try:
            if not bboxes:
                return None
            
            if len(bboxes) == 1:
                return [bboxes[0]]
            
            if len(bboxes) == 2:
                # 如果只有两个bbox，直接返回
                return bboxes
            
            # 调试输出：显示要合并的对象信息
            self.logger.debug(f"边界扩充合并 - 输入 {len(bboxes)} 个对象:")
            for i, bbox in enumerate(bboxes):
                class_name = bbox.get('class_name', 'unknown')
                self.logger.debug(f"  对象 {i+1} ({class_name}):")
                self.logger.debug(f"    - 四角坐标: 左上({bbox['x']}, {bbox['y']}), 右上({bbox['x2']}, {bbox['y']}), 左下({bbox['x']}, {bbox['y2']}), 右下({bbox['x2']}, {bbox['y2']})")
            
            # 按面积排序，找到面积最大的两个bbox
            sorted_bboxes = sorted(bboxes, key=lambda b: b['w'] * b['h'], reverse=True)
            bbox_a = sorted_bboxes[0]  # 面积最大
            bbox_b = sorted_bboxes[1]  # 面积第二大
            
            self.logger.debug(f"选择面积最大的两个对象:")
            self.logger.debug(f"  - 对象A (面积最大): 面积={bbox_a['w'] * bbox_a['h']}")
            self.logger.debug(f"  - 对象B (面积第二): 面积={bbox_b['w'] * bbox_b['h']}")
            
            # 初始化调整后的边界框
            adjusted_a = bbox_a.copy()
            adjusted_b = bbox_b.copy()
            
            # 处理其他对象（C及后续对象）
            for i, bbox_c in enumerate(sorted_bboxes[2:], start=3):
                self.logger.debug(f"处理对象C{i-2} (将被去除):")
                self.logger.debug(f"  - 面积: {bbox_c['w'] * bbox_c['h']}")
                
                # 检查C是否完全在A内部
                c_in_a = self._is_bbox_inside(bbox_c, adjusted_a)
                self.logger.debug(f"  - C是否完全在A内部: {c_in_a}")
                
                # 检查C是否完全在B内部
                c_in_b = self._is_bbox_inside(bbox_c, adjusted_b)
                self.logger.debug(f"  - C是否完全在B内部: {c_in_b}")
                
                # 如果C完全在A内部，A不需要调整
                if c_in_a:
                    self.logger.debug(f"  - A边界不需要调整（C完全在A内部）")
                else:
                    # 检查C与A是否有重合边界
                    if self._has_boundary_overlap(adjusted_a, bbox_c):
                        self.logger.debug(f"  - C与A有重合边界，扩充A的边界")
                        old_a = adjusted_a.copy()
                        adjusted_a = self._expand_bbox_boundary(adjusted_a, bbox_c)
                        self.logger.debug(f"    - A扩充前: ({old_a['x']}, {old_a['y']}) -> ({old_a['x2']}, {old_a['y2']})")
                        self.logger.debug(f"    - A扩充后: ({adjusted_a['x']}, {adjusted_a['y']}) -> ({adjusted_a['x2']}, {adjusted_a['y2']})")
                    else:
                        self.logger.debug(f"  - C与A无重合边界，A不调整")
                
                # 如果C完全在B内部，B不需要调整
                if c_in_b:
                    self.logger.debug(f"  - B边界不需要调整（C完全在B内部）")
                else:
                    # 检查C与B是否有重合边界
                    if self._has_boundary_overlap(adjusted_b, bbox_c):
                        self.logger.debug(f"  - C与B有重合边界，扩充B的边界")
                        old_b = adjusted_b.copy()
                        adjusted_b = self._expand_bbox_boundary(adjusted_b, bbox_c)
                        self.logger.debug(f"    - B扩充前: ({old_b['x']}, {old_b['y']}) -> ({old_b['x2']}, {old_b['y2']})")
                        self.logger.debug(f"    - B扩充后: ({adjusted_b['x']}, {adjusted_b['y']}) -> ({adjusted_b['x2']}, {adjusted_b['y2']})")
                    else:
                        self.logger.debug(f"  - C与B无重合边界，B不调整")
            
            # 调试输出：显示最终结果 - 返回A和调整后的B
            self.logger.debug(f"边界扩充处理完成:")
            self.logger.debug(f"  - 对象A (保持): x={adjusted_a['x']}, y={adjusted_a['y']}, w={adjusted_a['w']}, h={adjusted_a['h']}")
            self.logger.debug(f"    四角坐标: 左上({adjusted_a['x']}, {adjusted_a['y']}), 右上({adjusted_a['x2']}, {adjusted_a['y']}), 左下({adjusted_a['x']}, {adjusted_a['y2']}), 右下({adjusted_a['x2']}, {adjusted_a['y2']})")
            self.logger.debug(f"  - 对象B (调整后): x={adjusted_b['x']}, y={adjusted_b['y']}, w={adjusted_b['w']}, h={adjusted_b['h']}")
            self.logger.debug(f"    四角坐标: 左上({adjusted_b['x']}, {adjusted_b['y']}), 右上({adjusted_b['x2']}, {adjusted_b['y']}), 左下({adjusted_b['x']}, {adjusted_b['y2']}), 右下({adjusted_b['x2']}, {adjusted_b['y2']})")
            
            # 返回两个独立的边界框
            return [adjusted_a, adjusted_b]
            
        except Exception as e:
            self.logger.error(f"扩充合并边界框失败: {e}")
            return None
    
    def _has_boundary_overlap(self, bbox1: Dict, bbox2: Dict, tolerance: int = 2) -> bool:
        """检查两个边界框是否有边界重叠"""
        try:
            # 检查是否有共同的边界线
            same_top = abs(bbox1['y'] - bbox2['y']) <= tolerance
            same_bottom = abs(bbox1['y2'] - bbox2['y2']) <= tolerance
            same_left = abs(bbox1['x'] - bbox2['x']) <= tolerance
            same_right = abs(bbox1['x2'] - bbox2['x2']) <= tolerance
            
            # 检查是否有区域重叠
            x_overlap = not (bbox1['x2'] <= bbox2['x'] or bbox2['x2'] <= bbox1['x'])
            y_overlap = not (bbox1['y2'] <= bbox2['y'] or bbox2['y2'] <= bbox1['y'])
            
            return (same_top or same_bottom or same_left or same_right) and (x_overlap or y_overlap)
            
        except Exception as e:
            self.logger.error(f"检查边界重叠失败: {e}")
            return False
    
    def _has_any_overlap(self, bbox1: Dict, bbox2: Dict, tolerance: int = 2) -> bool:
        """检查两个边界框是否有任何形式的重叠（包括区域重叠和边界重叠）
        
        Args:
            bbox1: 第一个边界框
            bbox2: 第二个边界框
            tolerance: 边界容差
            
        Returns:
            bool: 是否有重叠
        """
        try:
            # 检查是否有区域重叠
            x_overlap = not (bbox1['x2'] <= bbox2['x'] or bbox2['x2'] <= bbox1['x'])
            y_overlap = not (bbox1['y2'] <= bbox2['y'] or bbox2['y2'] <= bbox1['y'])
            area_overlap = x_overlap and y_overlap
            
            # 检查是否有边界重叠（一条边的一部分重叠）
            boundary_overlap = self._has_boundary_overlap(bbox1, bbox2, tolerance)
            
            return area_overlap or boundary_overlap
            
        except Exception as e:
            self.logger.error(f"检查重叠失败: {e}")
            return False
    
    def _expand_bbox_boundary(self, target_bbox: Dict, reference_bbox: Dict, tolerance: int = 2) -> Dict:
        """扩充目标边界框的边界以包容参考边界框的重合边界
        
        Args:
            target_bbox: 目标边界框（要被扩充的）
            reference_bbox: 参考边界框（用于确定扩充方向）
            tolerance: 边界容差
            
        Returns:
            Dict: 扩充后的边界框
        """
        try:
            # 复制目标边界框
            expanded = target_bbox.copy()
            
            # 检查各个边界的重合情况并扩充
            # 检查顶边重合
            if abs(target_bbox['y'] - reference_bbox['y']) <= tolerance:
                # 顶边重合，可能需要向上扩充
                expanded['y'] = min(target_bbox['y'], reference_bbox['y'])
                self.logger.debug(f"    - 检测到顶边重合，向上扩充")
            
            # 检查底边重合  
            if abs(target_bbox['y2'] - reference_bbox['y2']) <= tolerance:
                # 底边重合，可能需要向下扩充
                expanded['y2'] = max(target_bbox['y2'], reference_bbox['y2'])
                self.logger.debug(f"    - 检测到底边重合，向下扩充")
            
            # 检查左边重合
            if abs(target_bbox['x'] - reference_bbox['x']) <= tolerance:
                # 左边重合，可能需要向左扩充
                expanded['x'] = min(target_bbox['x'], reference_bbox['x'])
                self.logger.debug(f"    - 检测到左边重合，向左扩充")
            
            # 检查右边重合
            if abs(target_bbox['x2'] - reference_bbox['x2']) <= tolerance:
                # 右边重合，可能需要向右扩充
                expanded['x2'] = max(target_bbox['x2'], reference_bbox['x2'])
                self.logger.debug(f"    - 检测到右边重合，向右扩充")
            
            # 如果有部分边界重叠但不是完全重合，也要扩充
            # 检查水平方向的部分重叠
            x_overlap = not (target_bbox['x2'] <= reference_bbox['x'] or reference_bbox['x2'] <= target_bbox['x'])
            if x_overlap:
                # 有水平重叠，扩充到包含两者的范围
                expanded['x'] = min(target_bbox['x'], reference_bbox['x'])
                expanded['x2'] = max(target_bbox['x2'], reference_bbox['x2'])
                self.logger.debug(f"    - 检测到水平重叠，扩充水平边界")
            
            # 检查垂直方向的部分重叠
            y_overlap = not (target_bbox['y2'] <= reference_bbox['y'] or reference_bbox['y2'] <= target_bbox['y'])
            if y_overlap:
                # 有垂直重叠，扩充到包含两者的范围
                expanded['y'] = min(target_bbox['y'], reference_bbox['y'])
                expanded['y2'] = max(target_bbox['y2'], reference_bbox['y2'])
                self.logger.debug(f"    - 检测到垂直重叠，扩充垂直边界")
            
            # 重新计算宽度和高度
            expanded['w'] = expanded['x2'] - expanded['x']
            expanded['h'] = expanded['y2'] - expanded['y']
            expanded['area'] = expanded['w'] * expanded['h']
            
            # 更新轮廓
            expanded['contour'] = np.array([
                [expanded['x'], expanded['y']],
                [expanded['x2'], expanded['y']],
                [expanded['x2'], expanded['y2']],
                [expanded['x'], expanded['y2']]
            ], dtype=np.int32).reshape(-1, 1, 2)
            
            return expanded
            
        except Exception as e:
            self.logger.error(f"扩充边界框失败: {e}")
            return target_bbox
    
    # ==================== 重叠方框分离方法 ====================
    
    def _morphology_separation(self, mask: np.ndarray, overlap_config: Dict[str, Any]) -> List[Any]:
        """策略1: 形态学操作优化"""
        try:
            morph_params = overlap_config.get('morphology_params', {})
            
            # 使用更小的核进行形态学操作，减少合并
            kernel_size = morph_params.get('kernel_size', [2, 2])
            iterations = morph_params.get('iterations', 1)
            use_edge_enhancement = morph_params.get('use_edge_enhancement', True)
            
            kernel_small = np.ones(kernel_size, np.uint8)
            mask_processed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_small, iterations=iterations)
            
            # 结合边缘检测
            if use_edge_enhancement:
                edges = cv2.Canny(mask, 50, 150)
                mask_processed = cv2.bitwise_or(mask_processed, edges)
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask_processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 分析轮廓特征，分离可能的重叠区域
            separated_contours = self._separate_overlapping_contours(list(contours), mask, overlap_config)
            
            return separated_contours
            
        except Exception as e:
            self.logger.error(f"形态学分离失败: {e}")
            return self._detect_color_annotations_original(mask)
    
    def _watershed_separation_method(self, mask: np.ndarray, overlap_config: Dict[str, Any]) -> List[Any]:
        """策略2: 分水岭算法分离"""
        try:
            watershed_params = overlap_config.get('watershed_params', {})
            min_distance = watershed_params.get('min_distance', 10)
            distance_threshold = watershed_params.get('distance_threshold', 0.3)
            
            # 距离变换
            dist_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
            
            # 查找局部最大值作为种子点
            local_maxima = self._find_local_maxima(dist_transform, min_distance, distance_threshold)
            
            if len(local_maxima) < 2:
                # 如果种子点太少，回退到原始方法
                return self._detect_color_annotations_original(mask)
            
            # 创建标记图像
            markers = np.zeros(mask.shape, dtype=np.int32)
            for i, (x, y) in enumerate(local_maxima):
                markers[y, x] = i + 1
            
            # 应用分水岭
            mask_bgr = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
            cv2.watershed(mask_bgr, markers)
            
            # 提取分离后的轮廓
            separated_contours = []
            for i in range(1, len(local_maxima) + 1):
                mask_i = (markers == i).astype(np.uint8) * 255
                contours_i, _ = cv2.findContours(mask_i, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                separated_contours.extend(contours_i)
            
            return separated_contours
            
        except Exception as e:
            self.logger.error(f"分水岭分离失败: {e}")
            return self._detect_color_annotations_original(mask)
    
    def _corner_clustering_method(self, mask: np.ndarray, overlap_config: Dict[str, Any]) -> List[Any]:
        """策略3: 角点检测+聚类"""
        try:
            if not SKLEARN_AVAILABLE:
                self.logger.warning("sklearn不可用，角点聚类方法不可用，使用默认方法")
                return self._detect_color_annotations_original(mask)
            
            corner_params = overlap_config.get('corner_clustering_params', {})
            max_corners = corner_params.get('max_corners', 20)
            quality_level = corner_params.get('quality_level', 0.01)
            min_distance = corner_params.get('min_distance', 10)
            cluster_eps = corner_params.get('cluster_eps', 20)
            cluster_min_samples = corner_params.get('cluster_min_samples', 2)
            
            # 检测角点
            corners = cv2.goodFeaturesToTrack(
                mask, 
                maxCorners=max_corners, 
                qualityLevel=quality_level, 
                minDistance=min_distance
            )
            
            if corners is None or len(corners) < 4:
                return self._detect_color_annotations_original(mask)
            
            # 将角点聚类，每个聚类代表一个可能的方框
            coords = corners.reshape(-1, 2)
            
            # 使用DBSCAN聚类
            if len(coords) >= 4:
                clustering = DBSCAN(eps=cluster_eps, min_samples=cluster_min_samples).fit(coords)
                labels = clustering.labels_
                
                # 为每个聚类生成边界框
                separated_contours = []
                for label in set(labels):
                    if label == -1:  # 噪声点
                        continue
                    
                    cluster_points = coords[labels == label]
                    if len(cluster_points) >= 3:
                        # 生成凸包作为轮廓
                        hull = cv2.convexHull(cluster_points.astype(np.float32))
                        separated_contours.append(hull.astype(np.int32))
                
                return separated_contours if separated_contours else self._detect_color_annotations_original(mask)
            
            return self._detect_color_annotations_original(mask)
            
        except Exception as e:
            self.logger.error(f"角点聚类分离失败: {e}")
            return self._detect_color_annotations_original(mask)
    
    def _template_matching_method(self, mask: np.ndarray, overlap_config: Dict[str, Any]) -> List[Any]:
        """策略4: 模板匹配"""
        try:
            template_params = overlap_config.get('template_matching_params', {})
            
            if not template_params.get('enabled', False):
                return self._detect_color_annotations_original(mask)
            
            threshold = template_params.get('threshold', 0.8)
            templates_config = template_params.get('templates', {})
            
            all_contours = []
            
            # 为每个模板进行匹配
            for class_name, template_config in templates_config.items():
                template = self._create_box_template(template_config)
                
                # 多尺度模板匹配
                result = cv2.matchTemplate(mask, template, cv2.TM_CCOEFF_NORMED)
                
                # 查找匹配位置
                locations = np.where(result >= threshold)
                
                # 转换为轮廓格式
                h, w = template.shape[:2]
                
                for pt in zip(*locations[::-1]):
                    x, y = pt
                    contour = np.array([[x, y], [x+w, y], [x+w, y+h], [x, y+h]], dtype=np.int32)
                    all_contours.append(contour.reshape(-1, 1, 2))
            
            return all_contours if all_contours else self._detect_color_annotations_original(mask)
            
        except Exception as e:
            self.logger.error(f"模板匹配分离失败: {e}")
            return self._detect_color_annotations_original(mask)
    
    def _separate_overlapping_contours(self, contours: List, mask: np.ndarray, overlap_config: Dict[str, Any]) -> List:
        """分离重叠的轮廓"""
        detection_params = overlap_config.get('detection_params', {})
        solidity_threshold = detection_params.get('solidity_threshold', 0.8)
        extent_threshold = detection_params.get('extent_threshold', 0.6)
        min_area_for_check = detection_params.get('min_area_for_check', 1000)
        
        separated = []
        
        for contour in contours:
            # 检查是否可能包含重叠方框
            if self._is_likely_overlapping(contour, solidity_threshold, extent_threshold, min_area_for_check):
                # 获取轮廓的边界框
                x, y, w, h = cv2.boundingRect(contour)
                roi_mask = mask[y:y+h, x:x+w]
                
                # 尝试分离重叠区域
                sub_contours = self._split_overlapping_region(roi_mask, x, y, overlap_config)
                if sub_contours:
                    separated.extend(sub_contours)
                else:
                    separated.append(contour)
            else:
                separated.append(contour)
        
        return separated
    
    def _is_likely_overlapping(self, contour: np.ndarray, solidity_threshold: float, 
                              extent_threshold: float, min_area_for_check: int) -> bool:
        """判断是否可能存在重叠方框"""
        contour_area = cv2.contourArea(contour)
        
        # 小于最小面积阈值的不检查
        if contour_area < min_area_for_check:
            return False
        
        # 计算轮廓的凸包
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        
        # 如果凸包面积显著大于轮廓面积，可能存在凹陷（重叠）
        solidity = contour_area / hull_area if hull_area > 0 else 0
        
        # 计算轮廓的矩形度
        x, y, w, h = cv2.boundingRect(contour)
        rect_area = w * h
        extent = contour_area / rect_area if rect_area > 0 else 0
        
        # 使用多个特征判断
        return solidity < solidity_threshold or extent < extent_threshold
    
    def _split_overlapping_region(self, roi_mask: np.ndarray, offset_x: int, offset_y: int, 
                                 overlap_config: Dict[str, Any]) -> List:
        """分离重叠区域"""
        strategy = overlap_config.get('strategy', 'watershed')
        
        if strategy == 'watershed':
            # 使用分水岭算法分离
            separated_contours = self._watershed_separation_on_roi(roi_mask, offset_x, offset_y, overlap_config)
            if len(separated_contours) > 1:
                return separated_contours
        
        # 基于角点检测分离
        return self._corner_based_separation(roi_mask, offset_x, offset_y, overlap_config)
    
    def _watershed_separation_on_roi(self, roi_mask: np.ndarray, offset_x: int, offset_y: int, 
                                    overlap_config: Dict[str, Any]) -> List:
        """在ROI上使用分水岭算法分离重叠区域"""
        try:
            watershed_params = overlap_config.get('watershed_params', {})
            min_distance = watershed_params.get('min_distance', 10)
            distance_threshold = watershed_params.get('distance_threshold', 0.3)
            
            # 距离变换
            dist_transform = cv2.distanceTransform(roi_mask, cv2.DIST_L2, 5)
            
            # 查找局部最大值作为种子点
            local_maxima = self._find_local_maxima(dist_transform, min_distance, distance_threshold)
            
            if len(local_maxima) < 2:
                return []
            
            # 创建标记图像
            markers = np.zeros(roi_mask.shape, dtype=np.int32)
            for i, (x, y) in enumerate(local_maxima):
                markers[y, x] = i + 1
            
            # 应用分水岭
            roi_bgr = cv2.cvtColor(roi_mask, cv2.COLOR_GRAY2BGR)
            cv2.watershed(roi_bgr, markers)
            
            # 提取分离后的轮廓
            separated_contours = []
            for i in range(1, len(local_maxima) + 1):
                mask_i = (markers == i).astype(np.uint8) * 255
                contours_i, _ = cv2.findContours(mask_i, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours_i:
                    # 调整坐标偏移
                    adjusted_contour = contour + np.array([offset_x, offset_y])
                    separated_contours.append(adjusted_contour)
            
            return separated_contours
            
        except Exception as e:
            self.logger.error(f"ROI分水岭分离失败: {e}")
            return []
    
    def _find_local_maxima(self, dist_transform: np.ndarray, min_distance: int = 10, 
                          distance_threshold: float = 0.3) -> List[Tuple[int, int]]:
        """查找距离变换图像中的局部最大值"""
        try:
            # 应用最大值滤波器
            local_max = maximum_filter(dist_transform, size=min_distance) == dist_transform
            
            # 设置阈值，只保留显著的局部最大值
            threshold = np.max(dist_transform) * distance_threshold
            local_max = local_max & (dist_transform > threshold)
            
            # 获取局部最大值的坐标
            coords = np.where(local_max)
            return list(zip(coords[1], coords[0]))  # (x, y) 格式
            
        except Exception as e:
            self.logger.error(f"查找局部最大值失败: {e}")
            return []
    
    def _corner_based_separation(self, roi_mask: np.ndarray, offset_x: int, offset_y: int, 
                               overlap_config: Dict[str, Any]) -> List:
        """基于角点检测的分离方法"""
        try:
            if not SKLEARN_AVAILABLE:
                return []
            
            corner_params = overlap_config.get('corner_clustering_params', {})
            max_corners = corner_params.get('max_corners', 20)
            quality_level = corner_params.get('quality_level', 0.01)
            min_distance = corner_params.get('min_distance', 10)
            cluster_eps = corner_params.get('cluster_eps', 20)
            cluster_min_samples = corner_params.get('cluster_min_samples', 2)
            
            # 检测角点
            corners = cv2.goodFeaturesToTrack(
                roi_mask, 
                maxCorners=max_corners, 
                qualityLevel=quality_level, 
                minDistance=min_distance
            )
            
            if corners is None or len(corners) < 4:
                return []
            
            # 将角点聚类，每个聚类代表一个可能的方框
            coords = corners.reshape(-1, 2)
            
            # 使用DBSCAN聚类
            if len(coords) >= 4:
                clustering = DBSCAN(eps=cluster_eps, min_samples=cluster_min_samples).fit(coords)
                labels = clustering.labels_
                
                # 为每个聚类生成边界框
                separated_contours = []
                for label in set(labels):
                    if label == -1:  # 噪声点
                        continue
                    
                    cluster_points = coords[labels == label]
                    if len(cluster_points) >= 3:
                        # 生成凸包作为轮廓
                        hull = cv2.convexHull(cluster_points.astype(np.float32))
                        # 调整坐标偏移
                        adjusted_hull = hull + np.array([offset_x, offset_y])
                        separated_contours.append(adjusted_hull.astype(np.int32))
                
                return separated_contours
            
            return []
            
        except Exception as e:
            self.logger.error(f"角点分离失败: {e}")
            return []
    
    def _create_box_template(self, template_config: Dict) -> np.ndarray:
        """创建方框模板"""
        width = template_config.get('width', 50)
        height = template_config.get('height', 50)
        thickness = template_config.get('thickness', 2)
        
        template = np.zeros((height, width), dtype=np.uint8)
        cv2.rectangle(template, (0, 0), (width-1, height-1), 255, thickness)
        
        return template
"""
YOLO标注验证和修复工具

用于检查和修复YOLO数据集中的标注问题，特别是处理
"Box and segment counts should be equal"警告。
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import yaml
import logging
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.basic.logger import get_logger, suppress_pil_debug_logs
from src.basic.exceptions import ValidationError

# 抑制第三方库调试日志
suppress_pil_debug_logs()


class AnnotationValidator:
    """YOLO标注验证器
    
    检查和修复YOLO数据集中的标注问题，包括：
    - 边界框和分割掩码数量不匹配
    - 坐标超出图像边界
    - 重复标注
    - 文件对应关系错误
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化验证器
        
        Args:
            config: 配置参数
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config or {}
        
        # 验证统计
        self.stats = {
            'total_files': 0,
            'valid_files': 0,
            'invalid_files': 0,
            'fixed_files': 0,
            'errors': defaultdict(int)
        }
        
        self.logger.info("AnnotationValidator初始化完成")
    
    def validate_dataset(self, dataset_path: str, auto_fix: bool = True) -> Dict[str, Any]:
        """验证整个数据集
        
        Args:
            dataset_path: 数据集配置文件路径
            auto_fix: 是否自动修复发现的问题
            
        Returns:
            Dict[str, Any]: 验证结果
            
        Raises:
            ValidationError: 验证失败
        """
        try:
            self.logger.info(f"开始验证数据集: {dataset_path}")
            
            # 加载数据集配置
            dataset_config = self._load_dataset_config(dataset_path)
            
            # 获取数据集路径
            dataset_root = Path(dataset_config['path'])
            train_path = dataset_root / dataset_config['train']
            val_path = dataset_root / dataset_config['val']
            
            # 验证训练集
            train_results = self._validate_split(train_path, 'train', auto_fix)
            
            # 验证验证集
            val_results = self._validate_split(val_path, 'val', auto_fix)
            
            # 汇总结果
            results = {
                'dataset_path': dataset_path,
                'train_results': train_results,
                'val_results': val_results,
                'overall_stats': self.stats,
                'success': train_results['success'] and val_results['success']
            }
            
            self.logger.info(f"数据集验证完成: {results['success']}")
            return results
            
        except Exception as e:
            raise ValidationError(str(dataset_path), [f"数据集验证失败: {e}"])
    
    def _load_dataset_config(self, dataset_path: str) -> Dict[str, Any]:
        """加载数据集配置
        
        Args:
            dataset_path: 数据集配置文件路径
            
        Returns:
            Dict[str, Any]: 数据集配置
        """
        dataset_path_obj = Path(dataset_path)
        
        if not dataset_path_obj.exists():
            raise ValidationError(str(dataset_path_obj), [f"数据集配置文件不存在: {dataset_path_obj}"])
        
        with open(dataset_path_obj, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必需字段
        required_fields = ['path', 'train', 'val', 'nc', 'names']
        for field in required_fields:
            if field not in config:
                raise ValidationError(str(dataset_path_obj), [f"数据集配置缺少必需字段: {field}"])
        
        return config
    
    def _validate_split(self, split_path: Path, split_name: str, auto_fix: bool) -> Dict[str, Any]:
        """验证数据集分割
        
        Args:
            split_path: 分割路径
            split_name: 分割名称
            auto_fix: 是否自动修复
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        self.logger.info(f"验证{split_name}数据集: {split_path}")
        
        if not split_path.exists():
            return {
                'success': False,
                'error': f"{split_name}数据目录不存在: {split_path}",
                'files_processed': 0
            }
        
        # 查找图像和标签文件
        image_files = self._find_image_files(split_path)
        label_files = self._find_label_files(split_path)
        
        if not image_files:
            return {
                'success': False,
                'error': f"{split_name}数据集中没有图像文件",
                'files_processed': 0
            }
        
        # 验证文件对应关系
        file_pairs = self._match_files(image_files, label_files)
        
        results = {
            'success': True,
            'files_processed': len(file_pairs),
            'issues_found': [],
            'issues_fixed': []
        }
        
        # 验证每个文件对
        for image_file, label_file in file_pairs:
            try:
                issues = self._validate_file_pair(image_file, label_file)
                
                if issues:
                    results['issues_found'].extend(issues)
                    
                    if auto_fix and label_file is not None:
                        fixed_issues = self._fix_annotation_issues(label_file, issues)
                        results['issues_fixed'].extend(fixed_issues)
                
            except Exception as e:
                self.logger.error(f"验证文件对失败 {image_file}: {e}")
                results['success'] = False
        
        return results
    
    def _find_image_files(self, path: Path) -> List[Path]:
        """查找图像文件
        
        Args:
            path: 搜索路径
            
        Returns:
            List[Path]: 图像文件列表
        """
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(path.glob(f"*{ext}"))
            image_files.extend(path.glob(f"*{ext.upper()}"))
        
        return sorted(image_files)
    
    def _find_label_files(self, path: Path) -> List[Path]:
        """查找标签文件
        
        Args:
            path: 搜索路径
            
        Returns:
            List[Path]: 标签文件列表
        """
        # 检查是否有labels子目录
        labels_dir = path / 'labels'
        if labels_dir.exists():
            return sorted(labels_dir.glob("*.txt"))
        
        # 否则在当前目录查找
        return sorted(path.glob("*.txt"))
    
    def _match_files(self, image_files: List[Path], label_files: List[Path]) -> List[Tuple[Path, Optional[Path]]]:
        """匹配图像和标签文件
        
        Args:
            image_files: 图像文件列表
            label_files: 标签文件列表
            
        Returns:
            List[Tuple[Path, Optional[Path]]]: 文件对列表
        """
        # 创建标签文件映射
        label_map = {f.stem: f for f in label_files}
        
        file_pairs = []
        for image_file in image_files:
            label_file = label_map.get(image_file.stem)
            file_pairs.append((image_file, label_file))
        
        return file_pairs
    
    def _validate_file_pair(self, image_file: Path, label_file: Optional[Path]) -> List[Dict[str, Any]]:
        """验证图像和标签文件对
        
        Args:
            image_file: 图像文件路径
            label_file: 标签文件路径
            
        Returns:
            List[Dict[str, Any]]: 发现的问题列表
        """
        issues = []
        
        # 检查标签文件是否存在
        if label_file is None:
            issues.append({
                'type': 'missing_label',
                'file': image_file,
                'description': f"图像文件 {image_file.name} 缺少对应的标签文件"
            })
            return issues
        
        if not label_file.exists():
            issues.append({
                'type': 'missing_label_file',
                'file': label_file,
                'description': f"标签文件不存在: {label_file}"
            })
            return issues
        
        # 获取图像尺寸
        try:
            from PIL import Image
            with Image.open(image_file) as img:
                img_width, img_height = img.size
        except Exception as e:
            issues.append({
                'type': 'image_load_error',
                'file': image_file,
                'description': f"无法加载图像文件: {e}"
            })
            return issues
        
        # 验证标签文件内容
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            box_count = 0
            segment_count = 0
            line_issues = []
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) < 5:
                    line_issues.append({
                        'line': line_num,
                        'content': line,
                        'issue': 'insufficient_data',
                        'description': f"第{line_num}行数据不足，至少需要5个值 (class_id x y w h)"
                    })
                    continue
                
                try:
                    class_id = int(parts[0])
                    coords = [float(x) for x in parts[1:]]
                    
                    # 检查是否为分割格式（多于4个坐标）
                    if len(coords) == 4:
                        # 边界框格式
                        box_count += 1
                        x, y, w, h = coords
                        
                        # 检查坐标边界
                        if not (0 <= x <= 1 and 0 <= y <= 1 and 0 <= w <= 1 and 0 <= h <= 1):
                            line_issues.append({
                                'line': line_num,
                                'content': line,
                                'issue': 'coordinate_out_of_bounds',
                                'description': f"第{line_num}行坐标超出边界 [0,1]: x={x}, y={y}, w={w}, h={h}"
                            })
                        
                        # 检查边界框有效性
                        if w <= 0 or h <= 0:
                            line_issues.append({
                                'line': line_num,
                                'content': line,
                                'issue': 'invalid_box_size',
                                'description': f"第{line_num}行边界框尺寸无效: w={w}, h={h}"
                            })
                    
                    elif len(coords) > 4 and len(coords) % 2 == 0:
                        # 分割格式
                        segment_count += 1
                        
                        # 检查分割点坐标
                        for i in range(0, len(coords), 2):
                            x, y = coords[i], coords[i+1]
                            if not (0 <= x <= 1 and 0 <= y <= 1):
                                line_issues.append({
                                    'line': line_num,
                                    'content': line,
                                    'issue': 'segment_coordinate_out_of_bounds',
                                    'description': f"第{line_num}行分割点坐标超出边界: x={x}, y={y}"
                                })
                                break
                    
                    else:
                        line_issues.append({
                            'line': line_num,
                            'content': line,
                            'issue': 'invalid_format',
                            'description': f"第{line_num}行格式错误，坐标数量不正确: {len(coords)}"
                        })
                
                except ValueError as e:
                    line_issues.append({
                        'line': line_num,
                        'content': line,
                        'issue': 'parse_error',
                        'description': f"第{line_num}行解析错误: {e}"
                    })
            
            # 检查边界框和分割掩码数量是否匹配
            if box_count > 0 and segment_count > 0 and box_count != segment_count:
                issues.append({
                    'type': 'box_segment_count_mismatch',
                    'file': label_file,
                    'box_count': box_count,
                    'segment_count': segment_count,
                    'description': f"边界框数量({box_count})与分割掩码数量({segment_count})不匹配"
                })
            
            # 添加行级别问题
            if line_issues:
                issues.append({
                    'type': 'line_issues',
                    'file': label_file,
                    'line_issues': line_issues,
                    'description': f"标签文件包含{len(line_issues)}个行级别问题"
                })
        
        except Exception as e:
            issues.append({
                'type': 'label_read_error',
                'file': label_file,
                'description': f"读取标签文件失败: {e}"
            })
        
        return issues
    
    def _fix_annotation_issues(self, label_file: Path, issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """修复标注问题
        
        Args:
            label_file: 标签文件路径
            issues: 问题列表
            
        Returns:
            List[Dict[str, Any]]: 修复的问题列表
        """
        fixed_issues = []
        
        for issue in issues:
            if issue['type'] == 'box_segment_count_mismatch':
                # 修复边界框和分割掩码数量不匹配问题
                if self._fix_box_segment_mismatch(label_file, issue):
                    fixed_issues.append(issue)
            
            elif issue['type'] == 'line_issues':
                # 修复行级别问题
                if self._fix_line_issues(label_file, issue['line_issues']):
                    fixed_issues.append(issue)
        
        return fixed_issues
    
    def _fix_box_segment_mismatch(self, label_file: Path, issue: Dict[str, Any]) -> bool:
        """修复边界框和分割掩码数量不匹配问题
        
        Args:
            label_file: 标签文件路径
            issue: 问题信息
            
        Returns:
            bool: 是否修复成功
        """
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 统计并分离边界框和分割数据
            box_lines = []
            segment_lines = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) >= 5:
                    coords = parts[1:]
                    if len(coords) == 4:
                        box_lines.append(line)
                    elif len(coords) > 4 and len(coords) % 2 == 0:
                        segment_lines.append(line)
            
            # 修复策略：保留数量较多的格式
            if len(box_lines) > len(segment_lines):
                # 保留边界框，移除分割数据
                new_lines = [line + '\n' for line in box_lines]
                self.logger.info(f"修复{label_file.name}: 保留{len(box_lines)}个边界框，移除{len(segment_lines)}个分割掩码")
            else:
                # 保留分割数据，移除边界框
                new_lines = [line + '\n' for line in segment_lines]
                self.logger.info(f"修复{label_file.name}: 保留{len(segment_lines)}个分割掩码，移除{len(box_lines)}个边界框")
            
            # 写回文件
            with open(label_file, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            self.stats['fixed_files'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"修复边界框分割不匹配问题失败 {label_file}: {e}")
            return False
    
    def _fix_line_issues(self, label_file: Path, line_issues: List[Dict[str, Any]]) -> bool:
        """修复行级别问题
        
        Args:
            label_file: 标签文件路径
            line_issues: 行级别问题列表
            
        Returns:
            bool: 是否修复成功
        """
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 记录需要删除的行
            lines_to_remove = set()
            
            for issue in line_issues:
                line_num = issue['line']
                issue_type = issue['issue']
                
                if issue_type in ['insufficient_data', 'parse_error', 'invalid_format']:
                    # 删除无效行
                    lines_to_remove.add(line_num - 1)  # 转换为0索引
                    self.logger.info(f"删除无效行 {line_num}: {issue['description']}")
                
                elif issue_type in ['coordinate_out_of_bounds', 'segment_coordinate_out_of_bounds']:
                    # 修复坐标边界问题
                    line_idx = line_num - 1
                    if line_idx < len(lines):
                        line = lines[line_idx].strip()
                        if self._fix_coordinate_bounds(line):
                            lines[line_idx] = line + '\n'
                            self.logger.info(f"修复坐标边界 {line_num}")
                        else:
                            lines_to_remove.add(line_idx)
                            self.logger.info(f"无法修复坐标边界，删除行 {line_num}")
                
                elif issue_type == 'invalid_box_size':
                    # 删除无效尺寸的边界框
                    lines_to_remove.add(line_num - 1)
                    self.logger.info(f"删除无效尺寸边界框 {line_num}")
            
            # 删除标记的行
            if lines_to_remove:
                new_lines = [line for i, line in enumerate(lines) if i not in lines_to_remove]
                
                with open(label_file, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                
                self.logger.info(f"修复{label_file.name}: 删除{len(lines_to_remove)}个问题行")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"修复行级别问题失败 {label_file}: {e}")
            return False
    
    def _fix_coordinate_bounds(self, line: str) -> bool:
        """修复坐标边界问题
        
        Args:
            line: 标注行
            
        Returns:
            bool: 是否修复成功
        """
        try:
            parts = line.split()
            if len(parts) < 5:
                return False
            
            class_id = parts[0]
            coords = [float(x) for x in parts[1:]]
            
            # 将坐标限制在[0,1]范围内
            fixed_coords = [max(0.0, min(1.0, coord)) for coord in coords]
            
            # 重新构建行
            new_line = f"{class_id} " + " ".join(f"{coord:.6f}" for coord in fixed_coords)
            line = new_line
            
            return True
            
        except Exception:
            return False
    
    def generate_validation_report(self, results: Dict[str, Any], output_path: str) -> None:
        """生成验证报告
        
        Args:
            results: 验证结果
            output_path: 输出路径
        """
        try:
            report_path = Path(output_path)
            report_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("# YOLO数据集验证报告\n\n")
                f.write(f"数据集路径: {results['dataset_path']}\n")
                f.write(f"验证时间: {Path().cwd()}\n\n")
                
                # 总体统计
                f.write("## 总体统计\n\n")
                f.write(f"- 总文件数: {self.stats['total_files']}\n")
                f.write(f"- 有效文件数: {self.stats['valid_files']}\n")
                f.write(f"- 无效文件数: {self.stats['invalid_files']}\n")
                f.write(f"- 修复文件数: {self.stats['fixed_files']}\n\n")
                
                # 训练集结果
                f.write("## 训练集验证结果\n\n")
                train_results = results['train_results']
                f.write(f"- 处理文件数: {train_results['files_processed']}\n")
                f.write(f"- 验证状态: {'通过' if train_results['success'] else '失败'}\n")
                
                if train_results.get('issues_found'):
                    f.write(f"- 发现问题: {len(train_results['issues_found'])}\n")
                if train_results.get('issues_fixed'):
                    f.write(f"- 修复问题: {len(train_results['issues_fixed'])}\n")
                
                # 验证集结果
                f.write("\n## 验证集验证结果\n\n")
                val_results = results['val_results']
                f.write(f"- 处理文件数: {val_results['files_processed']}\n")
                f.write(f"- 验证状态: {'通过' if val_results['success'] else '失败'}\n")
                
                if val_results.get('issues_found'):
                    f.write(f"- 发现问题: {len(val_results['issues_found'])}\n")
                if val_results.get('issues_fixed'):
                    f.write(f"- 修复问题: {len(val_results['issues_fixed'])}\n")
                
                # 详细问题列表
                all_issues = []
                if train_results.get('issues_found'):
                    all_issues.extend(train_results['issues_found'])
                if val_results.get('issues_found'):
                    all_issues.extend(val_results['issues_found'])
                
                if all_issues:
                    f.write("\n## 详细问题列表\n\n")
                    for i, issue in enumerate(all_issues, 1):
                        f.write(f"{i}. **{issue['type']}**: {issue['description']}\n")
                        if 'file' in issue:
                            f.write(f"   文件: {issue['file']}\n")
                        f.write("\n")
            
            self.logger.info(f"验证报告已生成: {report_path}")
            
        except Exception as e:
            self.logger.error(f"生成验证报告失败: {e}")


def main():
    """主函数 - 用于测试"""
    # 示例用法
    validator = AnnotationValidator()
    
    # 验证数据集
    dataset_path = "data/dataset.yaml"  # 替换为实际路径
    
    try:
        results = validator.validate_dataset(dataset_path, auto_fix=True)
        
        # 生成报告
        validator.generate_validation_report(results, "validation_report.md")
        
        print("验证完成！")
        
    except Exception as e:
        print(f"验证失败: {e}")


if __name__ == "__main__":
    main() 
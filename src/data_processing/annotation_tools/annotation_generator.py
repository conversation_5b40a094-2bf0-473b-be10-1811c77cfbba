"""
通用YOLO标注生成器
为各种转换器提供统一的YOLO标注文件生成服务
"""
import os
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import logging
from src.basic.base_classes import BaseProcessor


class AnnotationGenerator(BaseProcessor):
    """通用YOLO标注生成器
    
    提供统一的YOLO格式标注文件生成服务，
    可被各种格式转换器调用使用。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化标注生成器
        
        Args:
            config: 配置参数
        """
        super().__init__(config)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def process(self, input_data: Any) -> Any:
        """处理输入数据生成标注
        
        Args:
            input_data: 输入数据
            
        Returns:
            Any: 处理结果
        """
        if isinstance(input_data, dict):
            return self.generate_from_detections(input_data)
        return None
    
    def validate_input(self, input_data: Any) -> bool:
        """验证输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            bool: 是否有效
        """
        if isinstance(input_data, dict):
            required_keys = ['image_path', 'detections', 'output_path']
            return all(key in input_data for key in required_keys)
        return False
    
    def generate_from_detections(self, detection_data: Dict[str, Any]) -> bool:
        """从检测结果生成YOLO标注文件
        
        Args:
            detection_data: 检测数据，包含：
                - image_path: 图像路径
                - detections: 检测结果列表
                - output_path: 输出路径
                - image_width: 图像宽度
                - image_height: 图像高度
                
        Returns:
            bool: 生成是否成功
        """
        try:
            image_path = detection_data['image_path']
            detections = detection_data['detections']
            output_path = detection_data['output_path']
            img_width = detection_data.get('image_width', 0)
            img_height = detection_data.get('image_height', 0)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 生成YOLO格式标注
            yolo_lines = []
            for detection in detections:
                yolo_line = self._convert_to_yolo_format(
                    detection, img_width, img_height
                )
                if yolo_line:
                    yolo_lines.append(yolo_line)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_lines))
            
            self.logger.info(f"生成YOLO标注文件: {output_path}, 检测数量: {len(yolo_lines)}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成YOLO标注文件失败: {e}")
            return False
    
    def _convert_to_yolo_format(
        self, 
        detection: Dict[str, Any], 
        img_width: int, 
        img_height: int
    ) -> Optional[str]:
        """将检测结果转换为YOLO格式
        
        Args:
            detection: 检测结果，包含class_id, bbox等
            img_width: 图像宽度
            img_height: 图像高度
            
        Returns:
            Optional[str]: YOLO格式字符串
        """
        try:
            class_id = detection.get('class_id', 0)
            bbox = detection.get('bbox', [])
            
            if len(bbox) < 4 or img_width <= 0 or img_height <= 0:
                return None
            
            # 假设bbox格式为 [x, y, width, height] (像素坐标)
            x, y, width, height = bbox
            
            # 转换为YOLO格式 (归一化的中心坐标和尺寸)
            center_x = (x + width / 2) / img_width
            center_y = (y + height / 2) / img_height
            norm_width = width / img_width
            norm_height = height / img_height
            
            # 确保坐标在合理范围内
            center_x = max(0, min(1, center_x))
            center_y = max(0, min(1, center_y))
            norm_width = max(0, min(1, norm_width))
            norm_height = max(0, min(1, norm_height))
            
            return f"{class_id} {center_x:.6f} {center_y:.6f} {norm_width:.6f} {norm_height:.6f}"
            
        except Exception as e:
            self.logger.error(f"转换YOLO格式失败: {e}")
            return None
    
    def generate_from_contours(
        self, 
        contours: List[Any], 
        class_mapping: Dict[str, int],
        image_path: str,
        output_path: str,
        img_width: int,
        img_height: int
    ) -> bool:
        """从轮廓生成YOLO标注文件
        
        Args:
            contours: 轮廓列表
            class_mapping: 类别映射
            image_path: 图像路径
            output_path: 输出路径
            img_width: 图像宽度
            img_height: 图像高度
            
        Returns:
            bool: 生成是否成功
        """
        try:
            import cv2
            import numpy as np
            
            detections = []
            for i, contour in enumerate(contours):
                # 从轮廓计算边界框
                x, y, w, h = cv2.boundingRect(contour)
                
                # 默认类别ID为0，实际使用时可根据需要调整
                class_id = 0
                
                detection = {
                    'class_id': class_id,
                    'bbox': [x, y, w, h]
                }
                detections.append(detection)
            
            detection_data = {
                'image_path': image_path,
                'detections': detections,
                'output_path': output_path,
                'image_width': img_width,
                'image_height': img_height
            }
            
            return self.generate_from_detections(detection_data)
            
        except Exception as e:
            self.logger.error(f"从轮廓生成标注失败: {e}")
            return False 
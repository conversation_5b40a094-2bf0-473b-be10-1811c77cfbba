"""
自定义异常类模块

定义项目中使用的所有自定义异常类，提供明确的错误分类和处理机制。
"""

from typing import Optional, Any, Dict


class YoloUtilsError(Exception):
    """项目基础异常类
    
    所有项目自定义异常的基类。
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详细信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        """返回异常的字符串表示"""
        result = self.message
        if self.error_code:
            result = f"[{self.error_code}] {result}"
        if self.details:
            detail_str = ", ".join(f"{k}={v}" for k, v in self.details.items())
            result = f"{result} (详细信息: {detail_str})"
        return result


# 配置相关异常
class ConfigError(YoloUtilsError):
    """配置错误异常"""
    pass


class ConfigurationError(ConfigError):
    """配置错误异常（兼容性别名）"""
    pass


class ConfigNotFoundError(ConfigError):
    """配置文件未找到异常"""
    
    def __init__(self, config_path: str):
        super().__init__(
            f"配置文件未找到: {config_path}",
            error_code="CONFIG_NOT_FOUND",
            details={"config_path": config_path}
        )


class ConfigValidationError(ConfigError):
    """配置验证错误异常"""
    
    def __init__(self, field: str, value: Any, reason: str):
        super().__init__(
            f"配置验证失败 - {field}: {reason}",
            error_code="CONFIG_VALIDATION_ERROR",
            details={"field": field, "value": value, "reason": reason}
        )


# 数据处理相关异常
class DataProcessingError(YoloUtilsError):
    """数据处理错误异常"""
    pass


class DataFormatError(DataProcessingError):
    """数据格式错误异常"""
    
    def __init__(self, file_path: str, expected_format: str, actual_format: str):
        super().__init__(
            f"数据格式错误: 期望 {expected_format}, 实际 {actual_format}",
            error_code="DATA_FORMAT_ERROR",
            details={
                "file_path": file_path,
                "expected_format": expected_format,
                "actual_format": actual_format
            }
        )


class DataValidationError(DataProcessingError):
    """数据验证错误异常"""
    
    def __init__(self, file_path: str, validation_errors: list):
        super().__init__(
            f"数据验证失败: {len(validation_errors)} 个错误",
            error_code="DATA_VALIDATION_ERROR",
            details={
                "file_path": file_path,
                "validation_errors": validation_errors,
                "error_count": len(validation_errors)
            }
        )


# 兼容性别名
ValidationError = DataValidationError


class DataCorruptionError(DataProcessingError):
    """数据损坏错误异常"""
    
    def __init__(self, file_path: str, corruption_type: str):
        super().__init__(
            f"数据文件损坏: {corruption_type}",
            error_code="DATA_CORRUPTION_ERROR",
            details={
                "file_path": file_path,
                "corruption_type": corruption_type
            }
        )


# 数据集相关异常
class DatasetError(YoloUtilsError):
    """数据集错误异常"""
    pass


class DatasetNotFoundError(DatasetError):
    """数据集未找到异常"""
    
    def __init__(self, dataset_path: str):
        super().__init__(
            f"数据集未找到: {dataset_path}",
            error_code="DATASET_NOT_FOUND",
            details={"dataset_path": dataset_path}
        )


class DatasetEmptyError(DatasetError):
    """数据集为空异常"""
    
    def __init__(self, dataset_path: str):
        super().__init__(
            f"数据集为空: {dataset_path}",
            error_code="DATASET_EMPTY",
            details={"dataset_path": dataset_path}
        )


class DatasetMergeError(DatasetError):
    """数据集合并错误异常"""
    
    def __init__(self, source_datasets: list, reason: str):
        super().__init__(
            f"数据集合并失败: {reason}",
            error_code="DATASET_MERGE_ERROR",
            details={
                "source_datasets": source_datasets,
                "reason": reason,
                "dataset_count": len(source_datasets)
            }
        )


class ClassMappingError(DatasetError):
    """类别映射错误异常"""
    
    def __init__(self, source_class: str, target_class: str, reason: str):
        super().__init__(
            f"类别映射失败: {source_class} -> {target_class}, {reason}",
            error_code="CLASS_MAPPING_ERROR",
            details={
                "source_class": source_class,
                "target_class": target_class,
                "reason": reason
            }
        )


class DataIntegrationError(DatasetError):
    """数据整合错误异常"""
    
    def __init__(self, message: str, dataset_name: str = "", operation: str = ""):
        super().__init__(
            f"数据整合失败: {message}",
            error_code="DATA_INTEGRATION_ERROR",
            details={
                "dataset_name": dataset_name,
                "operation": operation
            }
        )


# 训练相关异常
class TrainingError(YoloUtilsError):
    """训练错误异常"""
    pass


class ModelNotFoundError(TrainingError):
    """模型未找到异常"""
    
    def __init__(self, model_path: str):
        super().__init__(
            f"模型文件未找到: {model_path}",
            error_code="MODEL_NOT_FOUND",
            details={"model_path": model_path}
        )


class ModelLoadError(TrainingError):
    """模型加载错误异常"""
    
    def __init__(self, model_path: str, error_msg: str):
        super().__init__(
            f"模型加载失败: {error_msg}",
            error_code="MODEL_LOAD_ERROR",
            details={
                "model_path": model_path,
                "error_message": error_msg
            }
        )


class TrainingInterruptedError(TrainingError):
    """训练中断异常"""
    
    def __init__(self, epoch: int, total_epochs: int, reason: str):
        super().__init__(
            f"训练在第 {epoch}/{total_epochs} 轮中断: {reason}",
            error_code="TRAINING_INTERRUPTED",
            details={
                "current_epoch": epoch,
                "total_epochs": total_epochs,
                "reason": reason
            }
        )


class TrainingConfigError(TrainingError):
    """训练配置错误异常"""
    
    def __init__(self, config_field: str, reason: str):
        super().__init__(
            f"训练配置错误 - {config_field}: {reason}",
            error_code="TRAINING_CONFIG_ERROR",
            details={
                "config_field": config_field,
                "reason": reason
            }
        )


# 评估相关异常
class EvaluationError(YoloUtilsError):
    """评估错误异常"""
    pass


class MetricCalculationError(EvaluationError):
    """指标计算错误异常"""
    
    def __init__(self, metric_name: str, reason: str):
        super().__init__(
            f"指标计算失败 - {metric_name}: {reason}",
            error_code="METRIC_CALCULATION_ERROR",
            details={
                "metric_name": metric_name,
                "reason": reason
            }
        )


class PredictionError(EvaluationError):
    """预测错误异常"""
    
    def __init__(self, input_data: str, error_msg: str):
        super().__init__(
            f"预测失败: {error_msg}",
            error_code="PREDICTION_ERROR",
            details={
                "input_data": input_data,
                "error_message": error_msg
            }
        )


# 爬虫相关异常
class CrawlerError(YoloUtilsError):
    """爬虫错误异常"""
    pass


class CrawlerTimeoutError(CrawlerError):
    """爬虫超时异常"""
    
    def __init__(self, url: str, timeout: float):
        super().__init__(
            f"爬取超时: {url} (超时时间: {timeout}秒)",
            error_code="CRAWLER_TIMEOUT",
            details={
                "url": url,
                "timeout": timeout
            }
        )


class CrawlerBlockedError(CrawlerError):
    """爬虫被阻止异常"""
    
    def __init__(self, source: str, reason: str):
        super().__init__(
            f"爬虫被阻止 - {source}: {reason}",
            error_code="CRAWLER_BLOCKED",
            details={
                "source": source,
                "reason": reason
            }
        )


class ImageDownloadError(CrawlerError):
    """图片下载错误异常"""
    
    def __init__(self, image_url: str, error_msg: str):
        super().__init__(
            f"图片下载失败: {error_msg}",
            error_code="IMAGE_DOWNLOAD_ERROR",
            details={
                "image_url": image_url,
                "error_message": error_msg
            }
        )


# 文件系统相关异常
class FileSystemError(YoloUtilsError):
    """文件系统错误异常"""
    pass


class DirectoryNotFoundError(FileSystemError):
    """目录未找到异常"""
    
    def __init__(self, directory_path: str):
        super().__init__(
            f"目录未找到: {directory_path}",
            error_code="DIRECTORY_NOT_FOUND",
            details={"directory_path": directory_path}
        )


class InsufficientSpaceError(FileSystemError):
    """磁盘空间不足异常"""
    
    def __init__(self, required_space: int, available_space: int):
        super().__init__(
            f"磁盘空间不足: 需要 {required_space} 字节, 可用 {available_space} 字节",
            error_code="INSUFFICIENT_SPACE",
            details={
                "required_space": required_space,
                "available_space": available_space
            }
        )


class PermissionDeniedError(FileSystemError):
    """权限拒绝异常"""
    
    def __init__(self, file_path: str, operation: str):
        super().__init__(
            f"权限拒绝: 无法{operation} {file_path}",
            error_code="PERMISSION_DENIED",
            details={
                "file_path": file_path,
                "operation": operation
            }
        )


# 网络相关异常
class NetworkError(YoloUtilsError):
    """网络错误异常"""
    pass


class ConnectionError(NetworkError):
    """连接错误异常"""
    
    def __init__(self, url: str, error_msg: str):
        super().__init__(
            f"连接失败: {url} - {error_msg}",
            error_code="CONNECTION_ERROR",
            details={
                "url": url,
                "error_message": error_msg
            }
        )


class APIError(NetworkError):
    """API错误异常"""
    
    def __init__(self, api_name: str, status_code: int, response_msg: str):
        super().__init__(
            f"API调用失败 - {api_name}: HTTP {status_code} - {response_msg}",
            error_code="API_ERROR",
            details={
                "api_name": api_name,
                "status_code": status_code,
                "response_message": response_msg
            }
        )


# 工具函数
def format_exception_chain(exception: Exception) -> str:
    """格式化异常链
    
    Args:
        exception: 异常对象
        
    Returns:
        str: 格式化的异常信息
    """
    lines = []
    current = exception
    
    while current:
        if isinstance(current, YoloUtilsError):
            lines.append(f"  {current.__class__.__name__}: {current}")
        else:
            lines.append(f"  {current.__class__.__name__}: {str(current)}")
        
        current = current.__cause__
    
    return "\n".join(lines)


def create_error_report(exception: Exception) -> Dict[str, Any]:
    """创建错误报告
    
    Args:
        exception: 异常对象
        
    Returns:
        Dict[str, Any]: 错误报告
    """
    import traceback
    import datetime
    
    report: Dict[str, Any] = {
        "timestamp": datetime.datetime.now().isoformat(),
        "exception_type": exception.__class__.__name__,
        "message": str(exception),
        "traceback": traceback.format_exc()
    }
    
    if isinstance(exception, YoloUtilsError):
        report["error_code"] = exception.error_code
        report["details"] = exception.details
    
    return report 
"""
基础抽象类定义模块

定义项目中所有模块继承的基础抽象类，确保接口统一性。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Tuple, Union, Optional
import logging


class BaseProcessor(ABC):
    """数据处理器基类
    
    为所有数据处理器提供统一的接口规范。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化处理器
        
        Args:
            config: 处理器配置参数
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def process(self, input_data: Any) -> Any:
        """处理数据的主方法
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理后的数据
        """
        pass
    
    @abstractmethod
    def validate_input(self, input_data: Any) -> bool:
        """验证输入数据是否有效
        
        Args:
            input_data: 待验证的输入数据
            
        Returns:
            bool: 数据是否有效
        """
        pass
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置参数
        
        Args:
            key: 配置键名
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(key, default)
    
    def log_info(self, message: str) -> None:
        """记录信息日志"""
        self.logger.info(message)
    
    def log_error(self, message: str) -> None:
        """记录错误日志"""
        self.logger.error(message)


class BaseConverter(ABC):
    """格式转换器基类
    
    为所有格式转换器提供统一的接口规范。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化转换器
        
        Args:
            config: 转换器配置参数
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def convert(self, source_path: str, target_path: str) -> bool:
        """执行格式转换
        
        Args:
            source_path: 源文件路径
            target_path: 目标文件路径
            
        Returns:
            bool: 转换是否成功
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表
        
        Returns:
            List[str]: 支持的格式列表
        """
        pass
    
    def validate_format(self, format_name: str) -> bool:
        """验证格式是否支持
        
        Args:
            format_name: 格式名称
            
        Returns:
            bool: 是否支持该格式
        """
        return format_name.lower() in [fmt.lower() for fmt in self.get_supported_formats()]
    
    def log_conversion(self, source: str, target: str, success: bool) -> None:
        """记录转换日志
        
        Args:
            source: 源路径
            target: 目标路径
            success: 是否成功
        """
        status = "成功" if success else "失败"
        self.logger.info(f"格式转换{status}: {source} -> {target}")


class BaseTrainer(ABC):
    """训练器基类
    
    为所有训练器提供统一的接口规范。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化训练器
        
        Args:
            config: 训练器配置参数
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.model = None
        self.training_history = []
    
    @abstractmethod
    def train(self, config: Dict[str, Any]) -> str:
        """执行训练
        
        Args:
            config: 训练配置参数
            
        Returns:
            str: 训练结果保存路径
        """
        pass
    
    @abstractmethod
    def validate(self, model_path: str) -> Dict[str, Any]:
        """验证模型性能
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        pass
    
    def load_model(self, model_path: str) -> bool:
        """加载模型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 子类实现具体的模型加载逻辑
            return True
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False
    
    def save_model(self, model_path: str) -> bool:
        """保存模型
        
        Args:
            model_path: 保存路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 子类实现具体的模型保存逻辑
            return True
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False
    
    def log_training_step(self, epoch: int, loss: float, metrics: Dict[str, float]) -> None:
        """记录训练步骤
        
        Args:
            epoch: 训练轮次
            loss: 损失值
            metrics: 评估指标
        """
        self.training_history.append({
            'epoch': epoch,
            'loss': loss,
            'metrics': metrics
        })
        self.logger.info(f"Epoch {epoch}: loss={loss:.4f}, metrics={metrics}")


class BaseEvaluator(ABC):
    """评估器基类
    
    为所有评估器提供统一的接口规范。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化评估器
        
        Args:
            config: 评估器配置参数
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def evaluate(self, model_path: str, dataset_path: str) -> Dict[str, Any]:
        """执行评估
        
        Args:
            model_path: 模型文件路径
            dataset_path: 数据集路径
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        pass
    
    def calculate_metrics(self, predictions: List[Any], ground_truth: List[Any]) -> Dict[str, float]:
        """计算评估指标
        
        Args:
            predictions: 预测结果
            ground_truth: 真实标签
            
        Returns:
            Dict[str, float]: 评估指标字典
        """
        # 基础实现，子类可以重写
        return {"accuracy": 0.0}
    
    def save_results(self, results: Dict[str, Any], output_path: str) -> bool:
        """保存评估结果
        
        Args:
            results: 评估结果
            output_path: 输出路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            self.logger.info(f"评估结果已保存到: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"保存评估结果失败: {e}")
            return False


class BaseDatasetHandler(ABC):
    """数据集处理器基类
    
    为所有数据集处理器提供统一的接口规范。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化数据集处理器
        
        Args:
            config: 处理器配置参数
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def load_dataset(self, path: str) -> Any:
        """加载数据集
        
        Args:
            path: 数据集路径
            
        Returns:
            Any: 加载的数据集对象
        """
        pass
    
    @abstractmethod
    def save_dataset(self, dataset: Any, path: str) -> bool:
        """保存数据集
        
        Args:
            dataset: 数据集对象
            path: 保存路径
            
        Returns:
            bool: 保存是否成功
        """
        pass
    
    def validate_dataset(self, dataset_path: str) -> Tuple[bool, List[str]]:
        """验证数据集完整性
        
        Args:
            dataset_path: 数据集路径
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            import os
            if not os.path.exists(dataset_path):
                errors.append(f"数据集路径不存在: {dataset_path}")
                return False, errors
            
            # 子类可以重写此方法添加更多验证逻辑
            return True, errors
            
        except Exception as e:
            errors.append(f"数据集验证失败: {e}")
            return False, errors
    
    def get_dataset_info(self, dataset_path: str) -> Dict[str, Any]:
        """获取数据集信息
        
        Args:
            dataset_path: 数据集路径
            
        Returns:
            Dict[str, Any]: 数据集信息
        """
        info = {
            "path": dataset_path,
            "exists": False,
            "size": 0,
            "file_count": 0
        }
        
        try:
            import os
            if os.path.exists(dataset_path):
                info["exists"] = True
                if os.path.isfile(dataset_path):
                    info["size"] = os.path.getsize(dataset_path)
                    info["file_count"] = 1
                elif os.path.isdir(dataset_path):
                    total_size = 0
                    file_count = 0
                    for root, dirs, files in os.walk(dataset_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            total_size += os.path.getsize(file_path)
                            file_count += 1
                    info["size"] = total_size
                    info["file_count"] = file_count
        except Exception as e:
            self.logger.error(f"获取数据集信息失败: {e}")
        
        return info


class BaseCrawler(ABC):
    """爬虫基类
    
    为所有爬虫提供统一的接口规范。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化爬虫
        
        Args:
            config: 爬虫配置参数
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.crawled_count = 0
        self.failed_count = 0
    
    @abstractmethod
    def crawl(self, keywords: List[str], max_images: int = 100) -> List[str]:
        """执行爬取
        
        Args:
            keywords: 搜索关键词列表
            max_images: 最大爬取数量
            
        Returns:
            List[str]: 成功爬取的图片路径列表
        """
        pass
    
    def filter_image(self, image_path: str) -> bool:
        """过滤图片
        
        Args:
            image_path: 图片路径
            
        Returns:
            bool: 是否通过过滤
        """
        try:
            try:
                from PIL import Image
            except ImportError:
                self.logger.warning("PIL库未安装，跳过图片过滤")
                return True
            
            import os
            
            # 检查文件是否存在
            if not os.path.exists(image_path):
                return False
            
            # 检查文件大小
            file_size = os.path.getsize(image_path)
            min_size = self.config.get('min_file_size', 1024)  # 1KB
            max_size = self.config.get('max_file_size', 10*1024*1024)  # 10MB
            
            if file_size < min_size or file_size > max_size:
                return False
            
            # 检查图片格式和尺寸
            with Image.open(image_path) as img:
                width, height = img.size
                min_width = self.config.get('min_width', 224)
                min_height = self.config.get('min_height', 224)
                
                if width < min_width or height < min_height:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"图片过滤失败: {image_path}, 错误: {e}")
            return False
    
    def get_crawl_statistics(self) -> Dict[str, Union[int, float]]:
        """获取爬取统计信息
        
        Returns:
            Dict[str, Union[int, float]]: 统计信息
        """
        return {
            "crawled_count": self.crawled_count,
            "failed_count": self.failed_count,
            "success_rate": self.crawled_count / (self.crawled_count + self.failed_count) 
                           if (self.crawled_count + self.failed_count) > 0 else 0.0
        } 
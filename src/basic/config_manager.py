"""
配置管理器模块

统一管理项目中的所有配置文件，提供配置加载、验证和动态更新功能。
"""

import os
import yaml
import json
from typing import Dict, Any, Optional, Union
import logging
from pathlib import Path


class ConfigManager:
    """配置管理器
    
    负责加载、验证和管理项目中的所有配置文件。
    """
    
    def __init__(self, config_dir: str = "config"):
        """初始化配置管理器
        
        Args:
            config_dir: 配置文件目录路径
        """
        self.config_dir = Path(config_dir)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.configs = {}
        self._default_configs = {}
        
        # 配置文件名映射
        self.config_files = {
            'main': 'config.yaml',
            'dataset': 'dataset_config.yaml',
            'training': 'training_config.yaml',
            'crawler': 'crawler_config.yaml'
        }
        
        # 初始化默认配置
        self._init_default_configs()
    
    def _init_default_configs(self) -> None:
        """初始化默认配置"""
        self._default_configs = {
            'main': {
                'project': {
                    'name': 'Yolo_Utils',
                    'version': '1.0.0'
                },
                'logging': {
                    'level': 'INFO',
                    'file_enabled': True,
                    'console_enabled': True
                },
                'device': {
                    'use_gpu': True,
                    'gpu_ids': [0]
                }
            },
            'dataset': {
                'data_sources': {},
                'class_mapping': {
                    'global_classes': []
                }
            },
            'training': {
                'training_strategy': {
                    'active_mode': 'direct'
                }
            },
            'crawler': {
                'crawling_parameters': {
                    'basic': {
                        'max_images_per_keyword': 1000
                    }
                }
            }
        }
    
    def load_config(self, config_name: str, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载指定配置文件
        
        Args:
            config_name: 配置名称 (main, dataset, training, crawler)
            config_path: 自定义配置文件路径
            
        Returns:
            Dict[str, Any]: 配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            ValueError: 配置文件格式错误
        """
        if config_path is None:
            if config_name not in self.config_files:
                raise ValueError(f"未知的配置名称: {config_name}")
            config_file_path = self.config_dir / self.config_files[config_name]
        else:
            config_file_path = Path(config_path)
        
        if not config_file_path.exists():
            self.logger.warning(f"配置文件不存在: {config_file_path}，使用默认配置")
            config = self._default_configs.get(config_name, {}).copy()
        else:
            try:
                with open(config_file_path, 'r', encoding='utf-8') as f:
                    if config_file_path.suffix.lower() in ['.yaml', '.yml']:
                        config = yaml.safe_load(f) or {}
                    elif config_file_path.suffix.lower() == '.json':
                        config = json.load(f)
                    else:
                        raise ValueError(f"不支持的配置文件格式: {config_file_path.suffix}")
                
                # 合并默认配置
                default_config = self._default_configs.get(config_name, {})
                config = self._merge_configs(default_config, config)
                
                self.logger.info(f"成功加载配置文件: {config_file_path}")
                
            except Exception as e:
                self.logger.error(f"加载配置文件失败: {config_file_path}, 错误: {e}")
                raise
        
        # 验证配置
        self._validate_config(config_name, config)
        
        # 缓存配置
        self.configs[config_name] = config
        
        return config
    
    def _merge_configs(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并默认配置和用户配置
        
        Args:
            default: 默认配置
            user: 用户配置
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        merged = default.copy()
        
        for key, value in user.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value
        
        return merged
    
    def _validate_config(self, config_name: str, config: Dict[str, Any]) -> None:
        """验证配置文件
        
        Args:
            config_name: 配置名称
            config: 配置字典
            
        Raises:
            ValueError: 配置验证失败
        """
        try:
            if config_name == 'main':
                self._validate_main_config(config)
            elif config_name == 'dataset':
                self._validate_dataset_config(config)
            elif config_name == 'training':
                self._validate_training_config(config)
            elif config_name == 'crawler':
                self._validate_crawler_config(config)
                
        except Exception as e:
            raise ValueError(f"配置验证失败 ({config_name}): {e}")
    
    def _validate_main_config(self, config: Dict[str, Any]) -> None:
        """验证主配置文件"""
        required_sections = ['project', 'logging', 'device']
        for section in required_sections:
            if section not in config:
                raise ValueError(f"缺少必需的配置段: {section}")
        
        # 验证日志级别
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        log_level = config.get('logging', {}).get('level', 'INFO')
        if log_level not in valid_log_levels:
            raise ValueError(f"无效的日志级别: {log_level}")
    
    def _validate_dataset_config(self, config: Dict[str, Any]) -> None:
        """验证数据集配置文件"""
        if 'data_sources' not in config:
            raise ValueError("缺少数据源配置")
        
        # 验证类别映射
        if 'class_mapping' in config:
            class_mapping = config['class_mapping']
            if 'global_classes' in class_mapping:
                global_classes = class_mapping['global_classes']
                if not isinstance(global_classes, list):
                    raise ValueError("global_classes必须是列表类型")
    
    def _validate_training_config(self, config: Dict[str, Any]) -> None:
        """验证训练配置文件"""
        if 'training_strategy' not in config:
            raise ValueError("缺少训练策略配置")
        
        strategy = config['training_strategy']
        active_mode = strategy.get('active_mode')
        if active_mode not in ['direct', 'incremental']:
            raise ValueError(f"无效的训练模式: {active_mode}")
    
    def _validate_crawler_config(self, config: Dict[str, Any]) -> None:
        """验证爬虫配置文件"""
        if 'crawling_parameters' not in config:
            raise ValueError("缺少爬取参数配置")
    
    def get_config(self, config_name: str, key_path: Optional[str] = None, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            config_name: 配置名称
            key_path: 配置键路径，支持点号分隔的多级路径，如 'logging.level'
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        if config_name not in self.configs:
            self.load_config(config_name)
        
        config = self.configs[config_name]
        
        if key_path is None:
            return config
        
        keys = key_path.split('.')
        value = config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_config(self, config_name: str, key_path: str, value: Any) -> None:
        """设置配置值
        
        Args:
            config_name: 配置名称
            key_path: 配置键路径
            value: 配置值
        """
        if config_name not in self.configs:
            self.load_config(config_name)
        
        config = self.configs[config_name]
        keys = key_path.split('.')
        
        # 创建嵌套字典路径
        current = config
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
        self.logger.info(f"配置已更新: {config_name}.{key_path} = {value}")
    
    def save_config(self, config_name: str, config_path: Optional[str] = None) -> None:
        """保存配置文件
        
        Args:
            config_name: 配置名称
            config_path: 保存路径，如果为None则保存到原路径
        """
        if config_name not in self.configs:
            raise ValueError(f"配置未加载: {config_name}")
        
        if config_path is None:
            save_path = self.config_dir / self.config_files[config_name]
        else:
            save_path = Path(config_path)
        
        # 确保目录存在
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.configs[config_name], f, 
                         default_flow_style=False, 
                         allow_unicode=True, 
                         indent=2)
            
            self.logger.info(f"配置已保存: {save_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {save_path}, 错误: {e}")
            raise
    
    def reload_config(self, config_name: str) -> Dict[str, Any]:
        """重新加载配置文件
        
        Args:
            config_name: 配置名称
            
        Returns:
            Dict[str, Any]: 重新加载的配置
        """
        self.logger.info(f"重新加载配置: {config_name}")
        return self.load_config(config_name)
    
    def load_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """加载所有配置文件
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有配置的字典
        """
        all_configs = {}
        for config_name in self.config_files.keys():
            try:
                all_configs[config_name] = self.load_config(config_name)
            except Exception as e:
                self.logger.error(f"加载配置失败: {config_name}, 错误: {e}")
                all_configs[config_name] = self._default_configs.get(config_name, {})
        
        return all_configs
    
    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有已加载的配置
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有配置的字典
        """
        return self.configs.copy()
    
    def create_config_template(self, config_name: str, output_path: Optional[str] = None) -> None:
        """创建配置文件模板
        
        Args:
            config_name: 配置名称
            output_path: 输出路径
        """
        if config_name not in self._default_configs:
            raise ValueError(f"未知的配置名称: {config_name}")
        
        if output_path is None:
            template_path = self.config_dir / f"{config_name}_template.yaml"
        else:
            template_path = Path(output_path)
        
        template_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(template_path, 'w', encoding='utf-8') as f:
            yaml.dump(self._default_configs[config_name], f,
                     default_flow_style=False,
                     allow_unicode=True,
                     indent=2)
        
        self.logger.info(f"配置模板已创建: {template_path}")


    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置
        
        Returns:
            Dict[str, Any]: 数据库配置字典
        """
        return self.get_config('main', 'database', {})
    
    def get_image_backup_config(self) -> Dict[str, Any]:
        """获取图片备份配置
        
        Returns:
            Dict[str, Any]: 图片备份配置字典
        """
        return self.get_config('main', 'image_backup', {})

    def get_backup_annotation_config(self) -> Dict[str, Any]:
        """获取备份标注配置
        
        Returns:
            Dict[str, Any]: 备份标注配置字典
        """
        return self.get_config('dataset', 'backup_annotation', {})


# 全局配置管理器实例
config_manager = ConfigManager() 
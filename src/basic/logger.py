"""
日志管理模块

统一管理项目中的日志系统，提供灵活的日志配置和输出控制。
"""

import logging
import os
import sys
from typing import Optional, Dict, Any
from pathlib import Path
from logging.handlers import RotatingFileHandler
import datetime


class LoggerManager:
    """日志管理器
    
    负责配置和管理项目中的日志系统。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化日志管理器
        
        Args:
            config: 日志配置字典
        """
        # 如果没有提供配置，使用默认配置（包含调试检测）
        if config is None:
            self.config = self._get_default_config()
        else:
            # 如果提供了配置，但没有指定level，则检测调试模式
            self.config = config.copy()
            if 'level' not in self.config:
                self.config['level'] = self._detect_log_level()
            else:
                # 如果指定了level，但检测到调试模式，优先使用DEBUG
                detected_level = self._detect_log_level()
                if detected_level == 'DEBUG':
                    self.config['level'] = 'DEBUG'
        
        self.loggers: Dict[str, logging.Logger] = {}
        self._setup_root_logger()
    
    def _detect_log_level(self) -> str:
        """检测合适的日志级别
        
        Returns:
            str: 日志级别字符串
        """
        # 1. 环境变量优先级最高
        env_log_level = os.getenv('YOLO_LOG_LEVEL', '').upper()
        if env_log_level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            return env_log_level
        print(f"env_log_level: {env_log_level}")
        # 2. 检测调试模式
        debug_mode = (
            # 检测IDE调试模式 - debugpy是VSCode Python调试器
            'debugpy' in sys.modules or
            # 检测PyCharm调试器
            'pydevd' in sys.modules or
            # 检测调试相关环境变量
            os.getenv('YOLO_DEBUG', '').lower() in ['true', '1', 'yes'] or
            # 检测命令行参数
            any(arg in sys.argv for arg in ['--debug', '-d']) if hasattr(sys, 'argv') else False
        )
        print(f"debug_mode: {debug_mode}")
        if debug_mode:
            return 'DEBUG'
        
        # 3. 默认级别
        return 'INFO'
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认日志配置"""
        # 检测调试模式并动态设置日志级别
        default_level = self._detect_log_level()
        
        return {
            'level': default_level,
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_enabled': True,
            'console_enabled': True,
            'log_dir': 'logs',
            'max_file_size': '10MB',
            'backup_count': 5,
            'encoding': 'utf-8'
        }
    
    def _setup_root_logger(self) -> None:
        """设置根日志器"""
        root_logger = logging.getLogger()
        log_level = getattr(logging, self.config['level'].upper())
        root_logger.setLevel(log_level)
        
        # 清除已有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建格式化器
        formatter = logging.Formatter(
            self.config['format'],
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 添加控制台处理器
        if self.config['console_enabled']:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.DEBUG)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        if self.config['file_enabled']:
            self._add_file_handler(root_logger, formatter)
        
        # 控制第三方库的日志级别
        self._configure_third_party_loggers()
        
        # 如果是DEBUG级别，输出调试信息
        if self.config['level'].upper() == 'DEBUG':
            # 使用print而不是logger，避免循环依赖
            print(f"🐛 日志系统已启用DEBUG模式 - 级别: {self.config['level']}")
            debug_reasons = []
            if 'debugpy' in sys.modules:
                debug_reasons.append("检测到debugpy模块")
            if 'pydevd' in sys.modules:
                debug_reasons.append("检测到pydevd模块")
            if os.getenv('YOLO_DEBUG', '').lower() in ['true', '1', 'yes']:
                debug_reasons.append("YOLO_DEBUG环境变量")
            if os.getenv('YOLO_LOG_LEVEL', '').upper() == 'DEBUG':
                debug_reasons.append("YOLO_LOG_LEVEL环境变量")
            if hasattr(sys, 'argv') and any(arg in sys.argv for arg in ['--debug', '-d']):
                debug_reasons.append("命令行参数")
            
            if debug_reasons:
                print(f"   启用原因: {', '.join(debug_reasons)}")
            else:
                print(f"   启用原因: 配置文件或其他方式")
    
    def _add_file_handler(self, logger: logging.Logger, formatter: logging.Formatter) -> None:
        """添加文件处理器"""
        log_dir = Path(self.config['log_dir'])
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成日志文件名
        timestamp = datetime.datetime.now().strftime('%Y%m%d')
        log_file = log_dir / f"yolo_utils_{timestamp}.log"
        
        # 解析文件大小
        max_size = self._parse_size(self.config['max_file_size'])
        
        try:
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=max_size,
                backupCount=self.config['backup_count'],
                encoding=self.config['encoding']
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            print(f"警告: 无法创建文件日志处理器: {e}")
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串为字节数
        
        Args:
            size_str: 大小字符串，如 '10MB'
            
        Returns:
            int: 字节数
        """
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(float(size_str[:-2]) * 1024)
        elif size_str.endswith('MB'):
            return int(float(size_str[:-2]) * 1024 * 1024)
        elif size_str.endswith('GB'):
            return int(float(size_str[:-2]) * 1024 * 1024 * 1024)
        else:
            return int(size_str)
    
    def _configure_third_party_loggers(self) -> None:
        """配置第三方库的日志级别，防止过多的调试信息"""
        # 第三方库日志级别映射
        third_party_loggers = {
            # PIL相关库 - 设置为WARNING级别，避免DEBUG输出
            'PIL': logging.WARNING,
            'PIL.PngImagePlugin': logging.WARNING,
            'PIL.Image': logging.WARNING,
            'PIL.TiffImagePlugin': logging.WARNING,
            'PIL.JpegImagePlugin': logging.WARNING,
            
            # OpenCV相关
            'cv2': logging.WARNING,
            
            # Matplotlib相关
            'matplotlib': logging.WARNING,
            'matplotlib.pyplot': logging.WARNING,
            'matplotlib.font_manager': logging.WARNING,
            
            # HTTP请求库
            'urllib3': logging.WARNING,
            'requests': logging.WARNING,
            
            # PyTorch相关
            'torch': logging.WARNING,
            'torchvision': logging.WARNING,
            
            # ultralytics内部一些过于详细的日志
            'ultralytics.utils.downloads': logging.INFO,
            'ultralytics.utils.files': logging.INFO,
        }
        
        for logger_name, level in third_party_loggers.items():
            logger = logging.getLogger(logger_name)
            logger.setLevel(level)
        
        # 如果当前是DEBUG模式，打印第三方库日志控制信息
        if self.config['level'].upper() == 'DEBUG':
            print(f"🔇 已设置第三方库日志级别，避免过多调试信息")
            print(f"   受控库: {', '.join(third_party_loggers.keys())}")
    
    def set_third_party_log_level(self, library_name: str, level: str = 'WARNING') -> None:
        """设置指定第三方库的日志级别
        
        Args:
            library_name: 库名称，如 'PIL', 'PIL.PngImagePlugin'
            level: 日志级别，如 'DEBUG', 'INFO', 'WARNING', 'ERROR'
        """
        logger = logging.getLogger(library_name)
        log_level = getattr(logging, level.upper())
        logger.setLevel(log_level)
        
        if self.config['level'].upper() == 'DEBUG':
            print(f"🔧 已设置 {library_name} 日志级别为 {level}")
    
    def suppress_noisy_loggers(self) -> None:
        """快速抑制常见的噪音日志输出
        
        调用此方法可以一键抑制PIL、matplotlib等库的过多调试信息
        """
        noisy_loggers = [
            'PIL.PngImagePlugin',
            'PIL.JpegImagePlugin', 
            'PIL.TiffImagePlugin',
            'PIL.Image',
            'PIL',
            'matplotlib.font_manager',
            'urllib3.connectionpool',
            'requests.packages.urllib3',
        ]
        
        for logger_name in noisy_loggers:
            self.set_third_party_log_level(logger_name, 'WARNING')
        
        if self.config['level'].upper() == 'DEBUG':
            print(f"🔇 已抑制 {len(noisy_loggers)} 个噪音日志输出")
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            logging.Logger: 日志器实例
        """
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def set_level(self, level: str, logger_name: Optional[str] = None) -> None:
        """设置日志级别
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            logger_name: 日志器名称，为None时设置根日志器
        """
        log_level = getattr(logging, level.upper())
        
        if logger_name is None:
            logging.getLogger().setLevel(log_level)
        else:
            self.get_logger(logger_name).setLevel(log_level)
    
    def disable_console_output(self) -> None:
        """禁用控制台输出"""
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stdout:
                root_logger.removeHandler(handler)
                break
    
    def enable_console_output(self) -> None:
        """启用控制台输出"""
        if not self.config['console_enabled']:
            return
        
        root_logger = logging.getLogger()
        
        # 检查是否已有控制台处理器
        has_console = any(
            isinstance(h, logging.StreamHandler) and h.stream == sys.stdout
            for h in root_logger.handlers
        )
        
        if not has_console:
            formatter = logging.Formatter(
                self.config['format'],
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.DEBUG)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
    
    def add_custom_handler(self, handler: logging.Handler, logger_name: Optional[str] = None) -> None:
        """添加自定义处理器
        
        Args:
            handler: 日志处理器
            logger_name: 日志器名称，为None时添加到根日志器
        """
        if logger_name is None:
            logging.getLogger().addHandler(handler)
        else:
            self.get_logger(logger_name).addHandler(handler)
    
    def create_module_logger(self, module_name: str, 
                           level: Optional[str] = None,
                           separate_file: bool = False) -> logging.Logger:
        """为模块创建专用日志器
        
        Args:
            module_name: 模块名称
            level: 日志级别，为None时使用全局级别
            separate_file: 是否创建独立的日志文件
            
        Returns:
            logging.Logger: 模块日志器
        """
        logger = self.get_logger(module_name)
        
        if level:
            logger.setLevel(getattr(logging, level.upper()))
        
        if separate_file and self.config['file_enabled']:
            self._add_module_file_handler(logger, module_name)
        
        return logger
    
    def _add_module_file_handler(self, logger: logging.Logger, module_name: str) -> None:
        """为模块添加独立的文件处理器"""
        log_dir = Path(self.config['log_dir']) / 'modules'
        log_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.datetime.now().strftime('%Y%m%d')
        log_file = log_dir / f"{module_name}_{timestamp}.log"
        
        formatter = logging.Formatter(
            self.config['format'],
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        max_size = self._parse_size(self.config['max_file_size'])
        
        try:
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=max_size,
                backupCount=self.config['backup_count'],
                encoding=self.config['encoding']
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            logger.error(f"无法创建模块日志文件: {e}")
    
    def cleanup_old_logs(self, days: int = 30) -> None:
        """清理旧日志文件
        
        Args:
            days: 保留天数
        """
        log_dir = Path(self.config['log_dir'])
        if not log_dir.exists():
            return
        
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
        
        for log_file in log_dir.rglob('*.log*'):
            try:
                file_mtime = datetime.datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_mtime < cutoff_date:
                    log_file.unlink()
                    print(f"已删除旧日志文件: {log_file}")
            except Exception as e:
                print(f"删除日志文件失败: {log_file}, 错误: {e}")
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息
        
        Returns:
            Dict[str, Any]: 日志统计信息
        """
        log_dir = Path(self.config['log_dir'])
        stats = {
            'log_dir': str(log_dir),
            'log_dir_exists': log_dir.exists(),
            'total_files': 0,
            'total_size': 0,
            'active_loggers': len(self.loggers),
            'logger_names': list(self.loggers.keys())
        }
        
        if log_dir.exists():
            log_files = list(log_dir.rglob('*.log*'))
            stats['total_files'] = len(log_files)
            stats['total_size'] = sum(f.stat().st_size for f in log_files if f.is_file())
        
        return stats


# 全局日志管理器实例
logger_manager = LoggerManager()

# 便捷函数
def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return logger_manager.get_logger(name)

def setup_logging(config: Dict[str, Any]) -> None:
    """设置日志的便捷函数"""
    global logger_manager
    logger_manager = LoggerManager(config)

def suppress_pil_debug_logs() -> None:
    """抑制PIL等第三方库的DEBUG日志输出的便捷函数
    
    这是一个快捷方法，专门用于解决PIL.PngImagePlugin等库输出过多调试信息的问题
    """
    logger_manager.suppress_noisy_loggers() 
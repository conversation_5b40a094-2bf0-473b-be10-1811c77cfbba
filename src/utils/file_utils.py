#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文件工具模块
提供基础的文件操作功能
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Union


def ensure_dir(path: Union[str, Path]) -> Path:
    """
    确保目录存在
    
    Args:
        path: 目录路径
        
    Returns:
        Path: 目录路径对象
    """
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def copy_file(src: Union[str, Path], dst: Union[str, Path]) -> bool:
    """
    复制文件
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
        
    Returns:
        bool: 是否成功
    """
    try:
        src_path = Path(src)
        dst_path = Path(dst)
        
        # 确保目标目录存在
        ensure_dir(dst_path.parent)
        
        # 复制文件
        shutil.copy2(src_path, dst_path)
        return True
    except Exception as e:
        print(f"复制文件失败: {src} -> {dst}, 错误: {e}")
        return False


def move_file(src: Union[str, Path], dst: Union[str, Path]) -> bool:
    """
    移动文件
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
        
    Returns:
        bool: 是否成功
    """
    try:
        src_path = Path(src)
        dst_path = Path(dst)
        
        # 确保目标目录存在
        ensure_dir(dst_path.parent)
        
        # 移动文件
        shutil.move(str(src_path), str(dst_path))
        return True
    except Exception as e:
        print(f"移动文件失败: {src} -> {dst}, 错误: {e}")
        return False


def get_file_list(directory: Union[str, Path], 
                  pattern: str = "*",
                  recursive: bool = False) -> List[Path]:
    """
    获取目录中的文件列表
    
    Args:
        directory: 目录路径
        pattern: 文件名模式
        recursive: 是否递归搜索
        
    Returns:
        List[Path]: 文件路径列表
    """
    directory = Path(directory)
    if not directory.exists():
        return []
    
    if recursive:
        return list(directory.rglob(pattern))
    else:
        return list(directory.glob(pattern))


def get_image_files(directory: Union[str, Path], 
                   recursive: bool = False) -> List[Path]:
    """
    获取目录中的图像文件
    
    Args:
        directory: 目录路径
        recursive: 是否递归搜索
        
    Returns:
        List[Path]: 图像文件路径列表
    """
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    directory = Path(directory)
    
    image_files = []
    for ext in image_extensions:
        if recursive:
            image_files.extend(directory.rglob(f"*{ext}"))
            image_files.extend(directory.rglob(f"*{ext.upper()}"))
        else:
            image_files.extend(directory.glob(f"*{ext}"))
            image_files.extend(directory.glob(f"*{ext.upper()}"))
    
    return sorted(image_files)


def get_label_files(directory: Union[str, Path], 
                   recursive: bool = False) -> List[Path]:
    """
    获取目录中的标签文件
    
    Args:
        directory: 目录路径
        recursive: 是否递归搜索
        
    Returns:
        List[Path]: 标签文件路径列表
    """
    directory = Path(directory)
    
    if recursive:
        return sorted(directory.rglob("*.txt"))
    else:
        return sorted(directory.glob("*.txt"))


def remove_file(file_path: Union[str, Path]) -> bool:
    """
    删除文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否成功
    """
    try:
        Path(file_path).unlink()
        return True
    except Exception as e:
        print(f"删除文件失败: {file_path}, 错误: {e}")
        return False


def remove_dir(dir_path: Union[str, Path]) -> bool:
    """
    删除目录
    
    Args:
        dir_path: 目录路径
        
    Returns:
        bool: 是否成功
    """
    try:
        shutil.rmtree(dir_path)
        return True
    except Exception as e:
        print(f"删除目录失败: {dir_path}, 错误: {e}")
        return False


def get_file_size(file_path: Union[str, Path]) -> int:
    """
    获取文件大小
    
    Args:
        file_path: 文件路径
        
    Returns:
        int: 文件大小（字节）
    """
    try:
        return Path(file_path).stat().st_size
    except Exception:
        return 0


def is_image_file(file_path: Union[str, Path]) -> bool:
    """
    检查是否是图像文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否是图像文件
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    return Path(file_path).suffix.lower() in image_extensions


def is_label_file(file_path: Union[str, Path]) -> bool:
    """
    检查是否是标签文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否是标签文件
    """
    return Path(file_path).suffix.lower() == '.txt' 
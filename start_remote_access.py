#!/usr/bin/env python3
"""
图片管理系统远程访问启动脚本

专门为远程访问配置的启动脚本，自动绑定到所有网络接口。
"""

import subprocess
import sys
import socket
from pathlib import Path

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

def main():
    """主函数"""
    print("=" * 60)
    print("图片质量管理系统 - 远程访问启动")
    print("=" * 60)
    
    # 获取服务器IP地址
    server_ip = get_local_ip()
    port = 8080
    
    print(f"🌐 服务器IP地址: {server_ip}")
    print(f"🔌 服务端口: {port}")
    print(f"🌍 远程访问地址: http://{server_ip}:{port}")
    print("=" * 60)
    
    # 启动服务器
    print("🚀 启动图片管理系统...")
    try:
        subprocess.run([
            sys.executable,
            "tools/start_image_manager.py",
            "--host", "0.0.0.0",
            "--port", str(port),
            "--debug"
        ])
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()

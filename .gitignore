# Python-generated files
__pycache__/
*.py[oc]
build/
dist/
wheels/
*.egg-info

# Virtual environments
.venv

# data
*.jpg
*.png
*.jpeg

/data/processed/backup_annotation/labels/*.txt
/data/processed/kaggle/
/data/processed/VisDrone2019/**/*.txt
/data/raw/VisDrone2019/
*.zip

*.log
# 排除 data/final/ 下的所有文件
data/final/*
/data/search/*

# 但允许 csv 文件
!data/final/*.csv
!data/search/*.yaml
!data/search/*.json

# 忽略runs目录
runs/

# 忽略预训练权重（若需要跟踪配置文件）
models/*.pt
*.pt

# 硬件配置建议
config_recommendations.json
.vscode/